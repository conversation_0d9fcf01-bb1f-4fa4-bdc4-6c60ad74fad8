syntax = "proto2";

package com.iflytek.traffic.log;
option java_package = "com.iflytek.traffic.log";
option java_outer_classname = "MaterialLogProto";

message MaterialLog {
  // dsp 信息
  required int64 dsp_id = 1;
  required int64 dsp_ep_id = 2;

  // 地区信息
  required string region = 4;
  // ssp 信息
  required int64 ssp_id = 5;
  required int64 ssp_ep_id = 6;
  optional string ssp_hash_id = 7;

  // 物料
  required Material material = 8;

  // 如果为 true 可能需要往 t_ssp_material_info 插入数据
  required bool ssp_audit = 9;
  optional string ssp_audit_hash_id = 11;
  // 如果为 true 可能需要往 t_machine_audit_material_info  插入数据
  required bool machine_audit = 10;
  optional string machine_audit_hash_id = 12;

  required int32 os = 13;


  message Material {
    required string hash_id = 1;

    // t_dsp_material
    optional string adm = 2;
    optional string bundle = 3;
    optional string iurl = 4;
    optional string cid = 5;
    repeated string cat = 6;
    required string crid = 7;
    repeated int32 attr = 8;
    optional int32 api = 9;
    optional int32 protocol = 10;
    optional string dealId = 11;
    optional int32 w = 12;
    optional int32 h = 13;
    optional int32 wratio = 14;
    optional int32 hratio = 15;
    optional string ext = 16;
    optional string admHashId = 17;
    optional int32 adType = 18;
  }
}