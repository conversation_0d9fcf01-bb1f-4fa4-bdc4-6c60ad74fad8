package com.iflytek.traffic.config;

import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
@MapperScan(basePackages = {"com.iflytek.traffic.data.mapper.info"}, sqlSessionFactoryRef = "InfoDataSourceFactory")
public class DataSourceConfig {
    /**
     * 源
     */
    @Bean(name = "InfoDataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.info")
    public DataSource infoDataSource() {
        return new HikariDataSource();
    }

    /**
     * 工厂
     */
    @Bean("InfoDataSourceFactory")
    @Primary
    @DependsOn("InfoDataSource")
    public SqlSessionFactory infoDataSourceFactory() throws Exception {
        SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();
        factoryBean.setDataSource(infoDataSource());
        factoryBean.setConfiguration(mybatisGlobalConfiguration());
        return factoryBean.getObject();
    }


    /**
     * 模板
     */
    @Bean("InfoSqlSessionTemplate")
    @Primary
    @DependsOn("InfoDataSourceFactory")
    public SqlSessionTemplate infoSqlSessionTemplate(
            @Qualifier("InfoDataSourceFactory") SqlSessionFactory sessionfactory) {
        return new SqlSessionTemplate(sessionfactory);
    }

    /**
     * 事务
     */
    @Bean(name = "InfoTransactionManager")
    @Primary
    @DependsOn("InfoDataSource")
    public DataSourceTransactionManager infoTransactionManager(@Qualifier("InfoDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean
    @ConfigurationProperties(prefix = "mybatis.configuration")
    public org.apache.ibatis.session.Configuration mybatisGlobalConfiguration() {
        return new org.apache.ibatis.session.Configuration();
    }
}
