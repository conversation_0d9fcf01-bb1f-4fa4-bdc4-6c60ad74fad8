package com.iflytek.traffic.procedure;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Scheduler;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import com.iflytek.traffic.data.entity.MachineAuditMaterialInfo;
import com.iflytek.traffic.data.entity.MachineAuditSspInfo;
import com.iflytek.traffic.data.entity.SspMaterialInfo;
import com.iflytek.traffic.data.provider.MachineAuditMaterialInfoProvider;
import com.iflytek.traffic.data.provider.SspMaterialInfoProvider;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.log.MaterialLogProto;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.asset.Asset;
import com.iflytek.traffic.session.request.Device;
import com.iflytek.traffic.session.response.Bid;
import com.iflytek.traffic.session.response.SeatBid;
import com.iflytek.traffic.util.Base64Utils;
import com.iflytek.traffic.util.Md5Utils;
import com.iflytek.traffic.util.SpringContextHelper;
import com.iflytek.traffic.util.constant.Constants;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@EventListener(listen = EventBusAddress.FILTER_CREATIVE)
public class MaterialAuditVerticle extends BasicVerticle {

    private static final Logger materialLogger = LoggerFactory.getLogger("material_log");

    private static final ConcurrentMap<String, Object> MATERIAL_CACHE = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(60, TimeUnit.SECONDS)
            .scheduler(Scheduler.systemScheduler())
//            .removalListener((RemovalListener<String, Object>) (key, value, cause) -> log.info("remove key:{}, value:{}", key, value))
            .<String, Object>build()
            .asMap();

    private static final Object MATERIAL_CACHE_VALUE = new Object();

    @Override
    void doHandle(SessionContext ctx) {
        if (MapUtil.isEmpty(ctx.getDspUnifiedResp())) {
            log.info("empty dsp response.");
            vertx.eventBus().request(EventBusAddress.RESPONSE_TO_MEDIA, ctx);
            return;
        }
        ctx.getDspUnifiedResp().forEach((k, v) -> {
            if (CollUtil.isEmpty(v.getSeatbids()) || CollUtil.isEmpty(v.getImpId2Bid())) {
                return;
            }
            DspEpObj dspEpObj = ctx.getResponseDspEpObj().get(k);
            for (SeatBid seatBid : v.getSeatbids()) {
                seatBid.getBids().removeIf(bid -> {
                    if (!materialVerify(ctx, dspEpObj, bid)) {
                        v.getImp2FilteredBid().put(bid.getImpId(), bid);
                        if (bid.getFilterReason() == null) {
                            bid.setFilterReason(Constants.FILTER_REASON_CREATIVE);
                        }
                        return true;
                    }
                    return false;
                });
            }
        });
        vertx.eventBus().request(EventBusAddress.PICK_ONE, ctx);
    }

    private boolean materialVerify(@NonNull SessionContext ctx, @NonNull DspEpObj dspEpObj, @NonNull Bid bid) {
        if (StringUtils.isEmpty(bid.getCrid())) {
            log.error("bid.crid is empty. sessionId = {}", ctx.getSessionId());
            return false;
        }

        String materialHashId = materialHashId(dspEpObj.getDspId(), bid.getCrid());
        bid.setMaterialHashId(materialHashId);

        // 物料黑名单
        if (isBlacklistMaterial(ctx, bid, materialHashId)) {
            log.info("hit material blacklist. sessionId = {}, dspId = {}, dspEpId = {}", ctx.getSessionId(), dspEpObj.getDspId(), dspEpObj.getDspEpId());
            return false;
        }

        boolean materialRequireMachineAudit = false;
        boolean materialRequireSspAudit = false;
        boolean materialRecorded = false;

        String region = getRegion(ctx.getRegion());

        try {
            //人审
            MachineAuditMaterialInfoProvider machineAuditMaterialInfoProvider = SpringContextHelper.getBean(MachineAuditMaterialInfoProvider.class);
            MachineAuditMaterialInfo materialAuditMaterialInfo = machineAuditMaterialInfoProvider.getMachineAuditMaterialInfo(materialHashId);

            if (materialAuditMaterialInfo != null && materialAuditMaterialInfo.getCheckStatus()!= null) {
                Integer checkStatus = materialAuditMaterialInfo.getCheckStatus();
                if (checkStatus == MachineAuditMaterialInfo.Constants.CHECK_STATUS_PASS) {
                    log.info("物料审核通过. sessionId = {}, dspEpId = {}, materialHashId = {}", ctx.getSessionId(), dspEpObj.getDspEpId(), materialHashId);
                    return true;
                } else if (checkStatus == MachineAuditMaterialInfo.Constants.CHECK_STATUS_DENY) {
                    log.info("物料素材审核拒绝. sessionId = {}, dspEpId = {}, materialHashId = {}", ctx.getSessionId(), dspEpObj.getDspEpId(), materialHashId);
                    return false;
                }
            }
            // 机器审核
            MachineAuditSspInfo machineAuditSspInfo = machineAuditMaterialInfoProvider.getMachineAuditMaterialInfo(region, ctx.getSspEp());
            if (isRequireMachineAudit(machineAuditSspInfo)) {
                String machinedHashId = machineHashId(region, dspEpObj, materialHashId, getOs(ctx));
                MachineAuditMaterialInfo machineAuditMaterialInfo = machineAuditMaterialInfoProvider.getMachineAuditMaterialInfo(machinedHashId);
                bid.setMachinedHashId(machinedHashId);
                if (machineAuditMaterialInfo == null) {
                    log.info("物料未入机审库, sessionId = {}, materialHashId = {}, machinedHashId = {}", ctx.getSessionId(), materialHashId, machinedHashId);

                    materialRequireMachineAudit = true;

                    if (isFirstAudit(machineAuditSspInfo)) {
                        log.info("先审后投, 机审未入库过滤. sessionId = {}, materialHashId = {}, machinedHashId = {}", ctx.getSessionId(), materialHashId, machinedHashId);
                        return false;
                    }
                } else {
                    materialRecorded = true;
                    Integer checkStatus = machineAuditMaterialInfo.getCheckStatus();
                    bid.setMaterialHashId(machineAuditMaterialInfo.getMaterialHashId());
                    if (isMachineAuditDeny(checkStatus)) {
                        log.info("机审拒绝, sessionId = {}, materialHashId = {}, machinedHashId = {}, checkStatus = {}",
                                ctx.getSessionId(), materialHashId, machinedHashId, checkStatus);
                        return false;
                    }

                    if (isFirstAuditNotPassed(machineAuditSspInfo, checkStatus)) {
                        log.info("先审后投, 机审未通过, sessionId = {}, materialHashId = {}, checkStatus = {}", ctx.getSessionId(), materialHashId, checkStatus);
                        return false;
                    }
                }
            }

            if (requireSppAudit()) {
                if (isSspAuditPass()) {
                    log.info("material ssp audit success. sessionId = {}, dspId = {}, dspEpId = {}", ctx.getSessionId(), dspEpObj.getDspId(), dspEpObj.getDspEpId());
                } else {
                    materialRequireSspAudit = true;
                    log.info("ssp audit failed. sessionId = {}, dspId = {}, dspEpId = {}", ctx.getSessionId(), dspEpObj.getDspId(), dspEpObj.getDspEpId());
                    return false;
                }
            }
        } finally {
            if (materialRequireMachineAudit || materialRequireSspAudit || !materialRecorded) {
                // 物料落盘
                recordMaterial(ctx, dspEpObj, bid, materialRequireSspAudit, materialRequireMachineAudit);
            }
        }

        log.info("物料审核通过. sessionId = {}, dspEpId = {}, materialHashId = {}", ctx.getSessionId(), dspEpObj.getDspEpId(), materialHashId);
        return true;
    }

    private boolean isFirstAudit(MachineAuditSspInfo machineAuditSspInfo) {
        return machineAuditSspInfo.getAuditType() == MachineAuditSspInfo.Constants.AUDIT_TYPE_FIRST_AUDIT;
    }

    private boolean isFirstAuditNotPassed(MachineAuditSspInfo machineAuditSspInfo, Integer checkStatus) {
        return machineAuditSspInfo.getAuditType() == MachineAuditSspInfo.Constants.AUDIT_TYPE_FIRST_AUDIT
                && checkStatus != MachineAuditMaterialInfo.Constants.CHECK_STATUS_PASS;
    }

    private boolean isMachineAuditDeny(Integer checkStatus) {
        return checkStatus == MachineAuditMaterialInfo.Constants.CHECK_STATUS_DENY;
    }

    private boolean isRequireMachineAudit(MachineAuditSspInfo machineAuditSspInfo) {
        return machineAuditSspInfo != null && machineAuditSspInfo.getEnabled() == MachineAuditSspInfo.Constants.ENABLED;
    }

    private boolean requireSppAudit() {
        return false;
    }

    private void recordMaterial(SessionContext ctx, DspEpObj dspEpObj, Bid bid, boolean sspAudit, boolean machineAudit) {
        if (bid == null) {
            log.info("bid is empty, not record log. sessionId = {}, dspId = {}, dspEpId = {}", ctx.getSessionId(), dspEpObj.getDspId(), dspEpObj.getDspEpId());
            return;
        }

        if (StringUtils.isEmpty(bid.getCrid())) {
            log.info("bid or bid.crid is null or empty, not record log. sessionId = {}", ctx.getSessionId());
            return;
        }

        String region = getRegion(ctx.getRegion());
        String materialHashId = materialHashId(dspEpObj.getDspId(), bid.getCrid());
        Device.Os os = getOs(ctx);
        Asset.AdType adType = bid.getAdType() == null ? Asset.AdType.UNKNOWN : bid.getAdType();

        // 检查是否需要记录物料日志
        String keyData = region + ":" + os.getValue() + ":" + materialHashId +
                ":" + dspEpObj.getDspId() + ":" + dspEpObj.getDspEpId() +
                ":" + ctx.getSspEp().getSspId() + ":" + ctx.getSspEp().getSspEpId() +
                ":" + sspAudit + ":" + machineAudit;
        String logKey = Md5Utils.digestAsHex(keyData);
        if (MATERIAL_CACHE.putIfAbsent(logKey, MATERIAL_CACHE_VALUE) != null) {
            log.info("日志已落盘, 1 min 内不在落盘, sessionId = {}, materialHashId = {}", ctx.getSessionId(), materialHashId);
            return;
        }

        MaterialLogProto.MaterialLog.Builder materialLogBuilder = MaterialLogProto.MaterialLog.newBuilder();
        materialLogBuilder.setDspId(dspEpObj.getDspId());
        materialLogBuilder.setDspEpId(dspEpObj.getDspEpId());
        materialLogBuilder.setRegion(region);
        materialLogBuilder.setSspId(ctx.getSspEp().getSspId());
        materialLogBuilder.setSspEpId(ctx.getSspEp().getSspEpId());
        materialLogBuilder.setSspAudit(sspAudit);

        materialLogBuilder.setMachineAudit(machineAudit);
        if (machineAudit) {
            materialLogBuilder.setMachineAuditHashId(machineHashId(region, dspEpObj, materialHashId, os));
        }

        materialLogBuilder.setOs(os.getValue());

        MaterialLogProto.MaterialLog.Material.Builder materialBuilder = materialLogBuilder.getMaterialBuilder();
        materialBuilder.setHashId(materialHashId);

        materialBuilder.setAdType(adType.getValue());
        if (StringUtils.isNotEmpty(bid.getAdm())) {
            materialBuilder.setAdm(bid.getAdm());
        }

        if (StringUtils.isNotEmpty(bid.getBundle())) {
            materialBuilder.setBundle(bid.getBundle());
        }

        if (StringUtils.isNotEmpty(bid.getIurl())) {
            materialBuilder.setIurl(bid.getIurl());
        }

        if (StringUtils.isNotEmpty(bid.getCid())) {
            materialBuilder.setCid(bid.getCid());
        }

        if (CollectionUtils.isNotEmpty(bid.getCat())) {
            for (String cat : bid.getCat()) {
                if (StringUtils.isNotEmpty(cat)) {
                    materialBuilder.addCat(cat);
                }
            }
        }

        materialBuilder.setCrid(bid.getCrid());

        if (CollectionUtils.isNotEmpty(bid.getAttr())) {
            for (Integer attr : bid.getAttr()) {
                if (attr != null) {
                    materialBuilder.addAttr(attr);
                }
            }
        }

        if (bid.getApi() != null) {
            materialBuilder.setApi(bid.getApi());
        }

        if (bid.getProtocol() != null) {
            materialBuilder.setProtocol(bid.getProtocol());
        }

        if (StringUtils.isNotEmpty(bid.getDealId())) {
            materialBuilder.setDealId(bid.getDealId());
        }

        if (bid.getW() != null) {
            materialBuilder.setW(bid.getW());
        }

        if (bid.getH() != null) {
            materialBuilder.setH(bid.getH());
        }

        if (bid.getWratio() != null) {
            materialBuilder.setWratio(bid.getWratio());
        }

        if (bid.getHratio() != null) {
            materialBuilder.setHratio(bid.getHratio());
        }

        if (bid.getExt() != null) {
            String ext = JSON.toJSONString(bid.getExt());
            if (StringUtils.isNotEmpty(ext)) {
                materialBuilder.setExt(ext);
            }
        }

        MaterialLogProto.MaterialLog materialLog = materialLogBuilder.build();
        if (log.isInfoEnabled()) {
            try {
                log.info("materialLog = {}", JsonFormat.printer().print(materialLog));
            } catch (InvalidProtocolBufferException ignored) {
            }
        }

        materialLogger.info("{} {}", ctx.getStartTime4Log(), Base64Utils.encodeAsString(materialLog.toByteArray()));
    }

    public Device.Os getOs(SessionContext ctx) {
        if (ctx.getUnifiedRequest() != null &&
                ctx.getUnifiedRequest().getDevice() != null
                && ctx.getUnifiedRequest().getDevice().getOs() != null) {
            return ctx.getUnifiedRequest().getDevice().getOs();
        }
        return Device.Os.UNKNOWN;
    }

    private String materialHashId(int dspId, String crid) {
        return Md5Utils.digestAsHex(dspId + ":" + crid);
    }

    private String machineHashId(String region, DspEpObj dspEpObj, String materialHashId, Device.Os os) {
        return Md5Utils.digestAsHex(region + ":" + dspEpObj.getDspId() + ":" + materialHashId + ":" + os.getValue());
    }

    private boolean isBlacklistMaterial(SessionContext ctx, Bid bid, String materialHashId) {
        return isBlacklistMaterial(ctx, bid, 0, materialHashId) || isBlacklistMaterial(ctx, bid, ctx.getSspEp().getSspId(), materialHashId);
    }

    private String getRegion(Long region) {
        return region == null ? "unknown" : String.valueOf(region);
    }

    private boolean isBlacklistMaterial(SessionContext ctx, Bid bid, Integer sspEpId, String materialHashId) {
        SspMaterialInfoProvider sspMaterialInfoProvider = SpringContextHelper.getBean(SspMaterialInfoProvider.class);
        SspMaterialInfo sspMaterialInfo = sspMaterialInfoProvider.getSspBlackMaterialInfo(getRegion(ctx.getRegion()), ctx.getSspEp().getSspId(), sspEpId, materialHashId);
        if (sspMaterialInfo == null) {
            log.info("sspMaterialInfo is null, not hit blacklist material. sessionId = {}, sspEpId = {}", ctx.getSessionId(), sspEpId);
            return false;
        }

        bid.setMaterialId(sspMaterialInfo.getMaterialId());
        if (SspMaterialInfo.Constants.CHECK_STATUS_BLACKLIST == sspMaterialInfo.getCheckStatus()) {
            log.info("hit blacklist material. sessionId = {}, ssp_material_id = {}", ctx.getSessionId(), sspMaterialInfo.getId());
            return true;
        }

        return false;
    }

    private boolean isSspAuditPass() {
        return true;
    }
}
