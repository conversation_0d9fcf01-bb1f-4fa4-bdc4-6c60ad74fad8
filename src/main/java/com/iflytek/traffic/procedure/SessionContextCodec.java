package com.iflytek.traffic.procedure;

import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.util.JsonUtil;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageCodec;

import java.io.IOException;

public class SessionContextCodec implements MessageCodec<SessionContext, SessionContext> {

    @Override
    public void encodeToWire(Buffer buffer, SessionContext sessionContext) {

        // Encode object to string
        String jsonToStr = null;
        try {
            jsonToStr = JsonUtil.encode(sessionContext);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // Length of JSON: is NOT characters count
        int length = jsonToStr.getBytes().length;

        // Write data into given buffer
        buffer.appendInt(length);
        buffer.appendString(jsonToStr);
    }

    @Override
    public SessionContext decodeFromWire(int position, Buffer buffer) {
        // My custom message starting from this *position* of buffer
        int _pos = position;

        // Length of JSON
        int length = buffer.getInt(_pos);

        // Get JSON string by it`s length
        // Jump 4 because getInt() == 4 bytes
        String jsonStr = buffer.getString(_pos+=4, _pos+=length);

        // We can finally create custom message object
        try {
            return JsonUtil.decode(jsonStr, SessionContext.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public SessionContext transform(SessionContext sessionContext) {
        return sessionContext;
    }

    @Override
    public String name() {
        return this.getClass().getSimpleName();
    }

    @Override
    public byte systemCodecID() {
        return -1;
    }
}
