package com.iflytek.traffic.procedure;

import cn.hutool.core.util.StrUtil;
import com.iflytek.traffic.service.MeterService;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.util.SpringContextHelper;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.eventbus.Message;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public abstract class BasicVerticle extends AbstractVerticle {

    private static final Map<String, Counter> verticleCounterMap = new ConcurrentHashMap<>();

    private static final Map<String, Timer> verticalTimerMap = new ConcurrentHashMap<>();

    @Override
    public void start() throws Exception {
        EventListener eventListen = this.getClass().getAnnotation(EventListener.class);
        String value = eventListen.listen();
        vertx.eventBus().consumer(value, this::handleMessage);
    }

    void handleMessage(Message<SessionContext> message) {
        Timer.Sample sampleT = Timer.start();
        Timer.Sample sampleM = Timer.start();
        SessionContext sessionContext = message.body();
        try {
            // 记录session, 便于后续处理未捕获异常
            vertx.getOrCreateContext().put("session", sessionContext);
            // 记录量级
            markVerticleCount(sessionContext);
            doHandle(sessionContext);
            markVerticleCostTime(sessionContext, sampleT, sampleM);
        } catch (Exception e) {
            log.error("ssp ep {} handle verticle {} error: {}", sessionContext.getSspEp().getSspEpId(), this.getClass().getSimpleName(), e.getMessage(), e);
            vertx.eventBus().request(EventBusAddress.RESPONSE_TO_MEDIA, sessionContext);
        }
    }

    abstract void doHandle(SessionContext sessionContext);

    @Override
    public void stop() throws Exception {
        log.info("stop {}: {}", this.getClass().getSimpleName(), deploymentID());
    }

    void markVerticleCount(SessionContext sessionContext) {
        EventListener eventListen = this.getClass().getAnnotation(EventListener.class);
        String verticleName = eventListen.listen().toLowerCase();
        String totalKey = verticleName + ".verticle.count.total";
        if (verticleCounterMap.containsKey(totalKey)) {
            verticleCounterMap.get(totalKey).increment();
        } else {
            synchronized (BasicVerticle.class) {
                if (verticleCounterMap.containsKey(totalKey)) {
                    verticleCounterMap.get(totalKey).increment();
                } else {
                    MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                    log.info("config verticle count meter: {}", totalKey);
                    Counter counter = meterService.count(totalKey);
                    counter.increment();
                    verticleCounterMap.put(totalKey, counter);
                }
            }
        }
        if (sessionContext.getSspEp() == null || StrUtil.isBlank(sessionContext.getSspEp().getPath())) {
            log.warn("sessionContext.getSspEp() is null or path is null, skip media vertical count");
            return;
        }
        String mediaKey = sessionContext.getSspEp().getPath() + "." + totalKey;
        if (verticleCounterMap.containsKey(mediaKey)) {
            verticleCounterMap.get(mediaKey).count();
        } else {
            synchronized (BasicVerticle.class) {
                if (verticleCounterMap.containsKey(mediaKey)) {
                    verticleCounterMap.get(mediaKey).count();
                    return;
                }
                MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                log.info("config verticle count meter: {}", mediaKey);
                Counter counter = meterService.count(mediaKey);
                counter.increment();
                verticleCounterMap.put(mediaKey, counter);
            }
        }
    }

    void markVerticleCostTime(SessionContext sessionContext, Timer.Sample sampleT, Timer.Sample sampleM) {
        EventListener eventListen = this.getClass().getAnnotation(EventListener.class);
        String verticleName = eventListen.listen().toLowerCase();
        String totalKey = verticleName + ".verticle.time";
        if (verticalTimerMap.containsKey(totalKey)) {
            sampleT.stop(verticalTimerMap.get(totalKey));
        } else {
            synchronized (BasicVerticle.class) {
                if (verticalTimerMap.containsKey(totalKey)) {
                    sampleT.stop(verticalTimerMap.get(totalKey));
                } else {
                    MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                    log.info("config verticle time meter: {}", totalKey);
                    Timer timer = meterService.time(totalKey);
                    sampleT.stop(timer);
                    verticalTimerMap.put(totalKey, timer);
                }
            }
        }
        String mediaKey = sessionContext.getSspEp().getPath() + "." + totalKey;
        if (verticalTimerMap.containsKey(mediaKey)) {
            sampleM.stop(verticalTimerMap.get(mediaKey));
        } else {
            synchronized (BasicVerticle.class) {
                if (verticalTimerMap.containsKey(mediaKey)) {
                    sampleT.stop(verticalTimerMap.get(mediaKey));
                } else {
                    MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                    log.info("config verticle time meter: {}", mediaKey);
                    Timer timer = meterService.time(mediaKey);
                    sampleM.stop(timer);
                    verticalTimerMap.put(mediaKey, timer);
                }
            }
        }
    }
}
