package com.iflytek.traffic.procedure;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.iflytek.traffic.procedure.callable.ResponseToMediaCallable;
import com.iflytek.traffic.protocol.ProtocolParser;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.request.UnifiedRequest;
import com.iflytek.traffic.session.response.UnifiedResponse;
import com.iflytek.traffic.ssp.SspEp;
import com.iflytek.traffic.util.GZip;
import io.vertx.core.Future;
import jakarta.servlet.AsyncContext;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

import java.io.OutputStream;

@Slf4j
@EventListener(listen = EventBusAddress.RESPONSE_TO_MEDIA)
public class Response2MediaVerticle extends BasicVerticle {

    void doHandle(SessionContext sessionContext) {
//        Future<Boolean> res = vertx.executeBlocking(new ResponseToMediaCallable(vertx, sessionContext), false);
//        res.onComplete(result -> {
//            if (result.succeeded()) {
//                log.info("response to media success.");
//            } else {
//                log.error("response to media failed, {}.", result.cause().getMessage(), result.cause());
//            }
//        });
        if (sessionContext.isAsyncContextCompleted()) {
            log.warn("async context already completed, sessionId: {}", sessionContext.getSessionId());
            return;
        }
        synchronized (sessionContext.getSessionId()) {
            if (sessionContext.isAsyncContextCompleted()) {
                log.warn("async context already completed, sessionId: {}", sessionContext.getSessionId());
                return;
            }
            sessionContext.setAsyncContextCompleted(true);
        }
        AsyncContext asyncContext = sessionContext.getAsyncContext();
        try {
            if (sessionContext.getWinner() != null) {
                ProtocolParser parser = sessionContext.getProtocolParser(sessionContext.getSspEp().getProtocol());
                Object response = parser.unifiedResp2ProtocolResp(sessionContext, sessionContext.getWinUResp());
                sessionContext.setRespStatus(200);
                sessionContext.setRespContentType("application/json");
                byte[] body =  JSON.toJSONBytes(response, SerializerFeature.NotWriteDefaultValue);
                sessionContext.setRespBody(body);
            }
            HttpServletResponse respServlet = (HttpServletResponse) asyncContext.getResponse();
            respServlet.setContentType(sessionContext.getRespContentType());
            respServlet.setStatus(sessionContext.getRespStatus());
            if (sessionContext.getSspEp().getIsGzip() == 1) {
                respServlet.setHeader("Content-Encoding", "gzip");
                respServlet.setHeader("Accept-Encoding", "gzip");
                sessionContext.setRespBody(GZip.compress(sessionContext.getRespBody()));
            }
            OutputStream out = respServlet.getOutputStream();
            if (ArrayUtil.isNotEmpty(sessionContext.getRespBody())) {
                out.write(sessionContext.getRespBody());
            }
            logMediaBidInfo(sessionContext);
        } catch (Exception e) {
            log.error("write response exception {}", e.getMessage(), e);
        } finally {
            asyncContext.getRequest().setAttribute("complete_time", System.currentTimeMillis());
            asyncContext.complete();
            vertx.eventBus().request(EventBusAddress.TO_LOG, sessionContext);
        }
    }

    private void logMediaBidInfo(SessionContext sessionContext) {
        UnifiedRequest uReq = sessionContext.getUnifiedRequest();
        UnifiedResponse uResp = sessionContext.getWinUResp();
        SspEp sspEp = sessionContext.getSspEp();
        int sspId = 0,sspEpId = 0;
        if (sspEp != null) {
            sspId = sspEp.getSspId();
            sspEpId = sspEp.getSspEpId();
        }
        String reqId = uReq.getMediaReqId();


        if (log.isInfoEnabled()) {
            log.info("SSP_ID={}, SSP_EP_ID={}, REQID={}, |to index:{}" ,sspId,sspEpId,reqId, JSON.toJSON(uReq).toString());
            if (uResp != null) {
                log.info("SSP_ID={}, SSP_EP_ID={}, REQID={}, |from index:{}", sspId, sspEpId, reqId, JSON.toJSON(uResp).toString());
            }
        }

    }

}
