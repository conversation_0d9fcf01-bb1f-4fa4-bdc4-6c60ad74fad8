package com.iflytek.traffic.procedure;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.iflytek.traffic.data.provider.DspDataProvider;
import com.iflytek.traffic.dsp.DspEpSupportWrapper;
import com.iflytek.traffic.search.index.DspEpSupportIndex;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.request.Impression;
import com.iflytek.traffic.session.request.UnifiedRequest;
import com.iflytek.traffic.util.SpringContextHelper;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

@EventListener(listen = EventBusAddress.CHOSE_DSP)
@Slf4j
public class ChoseDspVerticle extends BasicVerticle {

    @Override
    void doHandle(SessionContext sessionContext) {
        // TODO 倒排索引筛选
        UnifiedRequest unifiedRequest = sessionContext.getUnifiedRequest();
        if (MapUtil.isEmpty(unifiedRequest.getImps())) {
            log.warn("no imps in unified request.");
            vertx.eventBus().request(EventBusAddress.RESPONSE_TO_MEDIA, sessionContext);
            return;
        }
        DspEpSupportIndex index = SpringContextHelper.getBean(DspEpSupportIndex.class);
        for (Impression impression : sessionContext.getUnifiedRequest().getImps().values()) {
            List<DspEpSupportWrapper> qualifiedDes = index.pickAllObj(unifiedRequest.getReqDnf(), impression.getImpDnf(), sessionContext);
            if (CollUtil.isNotEmpty(qualifiedDes)) {
                sessionContext.getImpId2DesMap().put(impression.getImpId(), qualifiedDes);
            }
        }
        // TODO verifier过滤
        // TODO 比例
        if (MapUtil.isNotEmpty(sessionContext.getImpId2DesMap())) {
            sessionContext.getImpId2DesMap().forEach((k, v) -> {
                // 先根据dsp ep id聚合
                Map<Integer, List<DspEpSupportWrapper>> dspEpId2Support = v.stream().collect(Collectors.groupingBy(DspEpSupportWrapper::getDspEpId));
                // 按比例筛选
                if (MapUtil.isNotEmpty(dspEpId2Support)) {
                    List<DspEpSupportWrapper> supports = CollUtil.newArrayList();
                    dspEpId2Support.values().forEach(d -> {
                        DspEpSupportWrapper c = chooseByRatio(d);
                        if (c != null) {
                            supports.add(c);
                        }
                    });
                    sessionContext.getAfterFilterDesMap().put(k, supports);
                }
            });
        }
        // 未配置圈量规则的dsp ep
//        Set<Integer> configDspEpId = index.getAllConfigDspEpId();
//        DspDataProvider dspDataProvider = SpringContextHelper.getBean(DspDataProvider.class);
//        List<Integer> noConfigDspEpId = dspDataProvider.diffDspEpId(configDspEpId);
//        sessionContext.setNonConfigDspEpId(noConfigDspEpId);
        vertx.eventBus().request(EventBusAddress.TRAFFIC_FUSION, sessionContext);
    }

    private DspEpSupportWrapper chooseByRatio(List<DspEpSupportWrapper> desWrappers) {
        int sumWeight = desWrappers.stream().mapToInt(DspEpSupportWrapper::getWeight).sum();
        Random random = new Random();
        int bound = Math.max(sumWeight, 100);
        int rdn = random.nextInt(bound) + 1;
        int sum = 0;
        for (DspEpSupportWrapper p : desWrappers) {
            sum += p.getWeight();
            if (sum >= rdn) {
                log.info("select dsp ep support {}", p.getDspEpSupport().getDspEpSupportId());
                return p;
            }
        }
        log.info("no dsp ep support selected.");
        return null;
    }
}
