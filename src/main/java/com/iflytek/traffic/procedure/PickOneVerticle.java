package com.iflytek.traffic.procedure;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.traffic.data.provider.DspEpQpsProvider;
import com.iflytek.traffic.data.provider.InjectProvider;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.log.LogService;
import com.iflytek.traffic.log.urtb.AdxLog;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.request.Impression;
import com.iflytek.traffic.session.request.UnifiedRequest;
import com.iflytek.traffic.session.response.Bid;
import com.iflytek.traffic.session.response.UnifiedResponse;
import com.iflytek.traffic.ssp.SspEp;
import com.iflytek.traffic.util.SpringContextHelper;
import com.iflytek.traffic.util.constant.Constants;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Slf4j
@EventListener(listen = EventBusAddress.PICK_ONE)
public class PickOneVerticle extends BasicVerticle {
    @Override
    void doHandle(SessionContext sessionContext) {
        if (MapUtil.isEmpty(sessionContext.getDspUnifiedResp())) {
            log.info("empty dsp response.");
            vertx.eventBus().request(EventBusAddress.RESPONSE_TO_MEDIA, sessionContext);
            return;
        }
        // 选择出价最高的DSP
        Integer maxPriceDsp = null;
        Long maxPrice = -10000L;
        Set<Integer> bidSuccessDspEp = new HashSet<>();
        for(Map.Entry<Integer, UnifiedResponse> entry : sessionContext.getDspUnifiedResp().entrySet()) {
            Integer k = entry.getKey();
            UnifiedResponse v = entry.getValue();
            if (v.getCode() != 200 || CollUtil.isEmpty(v.getSeatbids())
                    || CollUtil.isEmpty(v.getImpId2Bid())) {
                log.info("dsp <{}> invalid response.", k);
                continue;
            }
            bidSuccessDspEp.add(k);
            // 如果有多个bid ，算总出价
            Long price = v.getSeatbids().get(0).getBids().get(0).getPrice();
            Map<String, Bid> impIdToBidMap = v.getImpId2Bid();
            if (impIdToBidMap.size() > 1) {
                price = 0l;
                UnifiedRequest res = sessionContext.getUnifiedRequest();
                for (String imp : impIdToBidMap.keySet()) {
                    Bid bid = impIdToBidMap.get(imp);
                    Impression impression = res.getImps().get(imp);
                    price += bid.getPrice() - impression.getBidfloor();
                }
            }
            if (price > maxPrice) {
                maxPrice = price;
                maxPriceDsp = k;
            }
        }
        // 记录竞价成功QPS
        DspEpQpsProvider dspEpQpsProvider = SpringContextHelper.getBean(DspEpQpsProvider.class);
        dspEpQpsProvider.recordDspEpBidQps(sessionContext.getSspEp(), sessionContext.getUnifiedRequest(), bidSuccessDspEp);
        if (maxPriceDsp != null) {
            sessionContext.setWinner(maxPriceDsp);
            UnifiedResponse winRsp = sessionContext.getDspUnifiedResp().get(maxPriceDsp);
            for (Bid bid : winRsp.getImpId2Bid().values()) {
                // 记录获胜价格&原始价格
                bid.setOriginPrice(bid.getPrice());
                bid.setWinPrice(maxPrice);
                // 替换价格宏
                DspEpObj winDspEp = sessionContext.getResponseDspEpObj().get(sessionContext.getWinner());
                sessionContext.setWinUResp(winRsp);
                if (winDspEp != null) {
                    replaceAuctionPrice(winDspEp, bid, bid.getPrice());
                    // 修改对下游的出价
                    modifyBidPrice(sessionContext, winDspEp, bid, bid.getPrice());
                    // 添加我方监测链接
                    addMonitorUrl(sessionContext, winDspEp, bid);
                }
            }
        }
        // 设置其他dsp过滤原因：出价
        setFilterReasonPrice(sessionContext);
        vertx.eventBus().request(EventBusAddress.RESPONSE_TO_MEDIA, sessionContext);
    }

    void replaceAuctionPrice(DspEpObj dspEpObj, Bid bid, Long price) {
        BigDecimal pb = new BigDecimal(price);
        String pstr = pb.divide(new BigDecimal(Constants.PRICE_MULTIPLY_MILLIONS)).toString();
        // nurl 默认替换
        if (StrUtil.isNotBlank(bid.getNurl())) {
            bid.setNurl(bid.getNurl().replace("${AUCTION_PRICE}", pstr));
        }
        // adm
        if (StrUtil.isNotBlank(bid.getAdm())) {
            bid.setAdm(bid.getAdm().replace("${AUCTION_PRICE}", pstr));
        }
        // burl
        if (StrUtil.isNotBlank(bid.getBurl())) {
            bid.setBurl(bid.getBurl().replace("${AUCTION_PRICE}", pstr));
        }
    }

    void modifyBidPrice(SessionContext sessionContext, DspEpObj dspEpObj, Bid bid, Long winPrice) {
        // 记录获胜价格&原始价格
        bid.setOriginPrice(bid.getPrice());
        bid.setWinPrice(winPrice);
        // 修改对下游的出价
        Impression impression = sessionContext.getUnifiedRequest().getImps().get(bid.getImpId());
        Float profit = impression.getDspProfitRatio().get(dspEpObj.getDspEpId());
        // (winPrice - x) / winPrice = profit; x = winPrice * (1 - profit)
        if (profit != null && profit > 0) {
            // 计算出价
            Float price = winPrice * (1 - profit);
            // 四舍五入
            price = new BigDecimal(price).setScale(0, BigDecimal.ROUND_HALF_UP).floatValue();
            bid.setPrice(price.longValue());
        }
    }

    void addMonitorUrl(SessionContext sessionContext, DspEpObj dspEpObj, Bid bid) {
        String reqId = sessionContext.getUnifiedRequest().getMediaReqId();
        String bidId = sessionContext.getSessionId();
        String impId = bid.getImpId();
        Impression impression = sessionContext.getUnifiedRequest().getImps().get(impId);
        AdxLog.AdxTrace.ParamInfo.Builder paramInfo = LogService.genParamInfo(sessionContext, dspEpObj, impression);
        String win = impression.getSecure() != null && impression.getSecure() == 1 ? SpringContextHelper.getProperty("monitor.win.url.https")
                : SpringContextHelper.getProperty("monitor.win.url.http");
        String winUrl = MessageFormat.format(win, Base64.encode(paramInfo.build().toByteArray()), reqId, bidId, impId);
        // 302 dsp win notice
        if (StrUtil.isNotBlank(bid.getNurl())) {
            winUrl = winUrl + "&landing=" + URLEncoder.encode(bid.getNurl(), CharsetUtil.CHARSET_UTF_8);
        }
        bid.setNurl(winUrl);
        // 处理曝光监测
        int type = switch (impression.getRequestAdType()) {
            case SUPPER_BANNER -> 1;
            case SUPPER_VIDEO -> 2;
            case SUPPER_NATIVE -> 3;
            case UNKNOWN -> 1;
            default -> 1;
        };
        // 结算类型，默认填0
        String st = "0";
        String monitorView = impression.getSecure() != null && impression.getSecure() == 1 ? SpringContextHelper.getProperty("monitor.view.url.https")
                : SpringContextHelper.getProperty("monitor.view.url.http");
        String impWithoutLanding = MessageFormat.format(monitorView, Base64.encode(paramInfo.build().toByteArray()), reqId, bidId, impId, st, "${AUCTION_PRICE}");
        InjectProvider injectProvider = SpringContextHelper.getBean(InjectProvider.class);
        SspEp sspEp = sessionContext.getSspEp();
        // adm
        if (sspEp.getSettlementType() == 1) {
            // dsp 采用adm结算 直接注入
            if (dspEpObj.getSettlementType() == 1) {
                String adm = injectProvider.injectView(bid.getAdm(), impWithoutLanding, type);
                bid.setAdm(adm);
            }
            // dsp 采用burl结算, 302 dsp的burl然后注入
            if (dspEpObj.getSettlementType() == 2) {
                if (StrUtil.isNotBlank(bid.getBurl())) {
                    paramInfo.addLandingList(bid.getBurl());
                    String impWithLanding = MessageFormat.format(monitorView, Base64.encode(paramInfo.build().toByteArray()), reqId, bidId, impId, st, "${AUCTION_PRICE}");
                    String adm = injectProvider.injectView(bid.getAdm(), impWithLanding, type);
                    bid.setAdm(adm);
                    bid.setBurl("");
                } else {
                    log.warn("dsp ep <{}> burl is empty, inject our view url.", dspEpObj.getDspEpId());
                    String adm = injectProvider.injectView(bid.getAdm(), impWithoutLanding, type);
                    bid.setAdm(adm);
                }
            }
        }
        // burl
        if (sspEp.getSettlementType() == 2) {
            // dsp 采用adm结算 burl字段填写我方监测即可
            if (dspEpObj.getSettlementType() == 1) {
                bid.setBurl(impWithoutLanding);
            }
            // dsp 采用burl结算 302 dsp的burl即可
            if (dspEpObj.getSettlementType() == 2) {
                if (StrUtil.isNotBlank(bid.getBurl())) {
                    paramInfo.addLandingList(bid.getBurl());
                }
                String impWithLanding = MessageFormat.format(monitorView, Base64.encode(paramInfo.build().toByteArray()), reqId, bidId, impId, st, "${AUCTION_PRICE}");
                bid.setBurl(impWithLanding);
            }
        }
    }

    void setFilterReasonPrice(SessionContext sessionContext) {
        Integer winner = sessionContext.getWinner();
        for (int dspEpId : sessionContext.getDspUnifiedResp().keySet()) {
            if (winner != null && dspEpId == winner) {
                continue;
            }
            UnifiedResponse response = sessionContext.getDspUnifiedResp().get(dspEpId);
            Map<String, Bid> impIdToBidMap = response.getImpId2Bid();
            if (CollUtil.isNotEmpty(impIdToBidMap)) {
                for (Bid bid : impIdToBidMap.values()) {
                    // 设置过滤原因
                    if (bid.getFilterReason() == null) {
                        bid.setFilterReason(Constants.FILTER_REASON_PRICE);
                    }
//                    response.getImp2FilterReason().putIfAbsent(bid.getImpId(), Constants.FILTER_REASON_PRICE);
                }
            }

        }
    }

}
