package com.iflytek.traffic.procedure.callable;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.iflytek.traffic.procedure.EventBusAddress;
import com.iflytek.traffic.protocol.ProtocolParser;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.request.UnifiedRequest;
import com.iflytek.traffic.session.response.UnifiedResponse;
import com.iflytek.traffic.ssp.SspEp;
import com.iflytek.traffic.util.GZip;
import io.vertx.core.Vertx;
import jakarta.servlet.AsyncContext;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.OutputStream;
import java.util.concurrent.Callable;

@Slf4j
@AllArgsConstructor
public class ResponseToMediaCallable implements Callable<Boolean> {

    private Vertx vertx;

    private SessionContext sessionContext;

    @Override
    public Boolean call() throws Exception {
        log.info(Thread.currentThread().getName());
        if (sessionContext.isAsyncContextCompleted()) {
            log.warn("async context already completed, sessionId: {}", sessionContext.getSessionId());
            return true;
        }
        AsyncContext asyncContext = sessionContext.getAsyncContext();
        try {
            if (sessionContext.getWinner() != null) {
                ProtocolParser parser = sessionContext.getProtocolParser(sessionContext.getSspEp().getProtocol());
                Object response = parser.unifiedResp2ProtocolResp(sessionContext, sessionContext.getWinUResp());
                sessionContext.setRespStatus(200);
                sessionContext.setRespContentType("application/json");
                byte[] body =  JSON.toJSONBytes(response, SerializerFeature.NotWriteDefaultValue);
                sessionContext.setRespBody(body);
            }
            HttpServletResponse respServlet = (HttpServletResponse) asyncContext.getResponse();
            respServlet.setContentType(sessionContext.getRespContentType());
            respServlet.setStatus(sessionContext.getRespStatus());
            if (sessionContext.getSspEp().getIsGzip() == 1) {
                respServlet.setHeader("Content-Encoding", "gzip");
                respServlet.setHeader("Accept-Encoding", "gzip");
                sessionContext.setRespBody(GZip.compress(sessionContext.getRespBody()));
            }
            OutputStream out = respServlet.getOutputStream();
            if (ArrayUtil.isNotEmpty(sessionContext.getRespBody())) {
                out.write(sessionContext.getRespBody());
            }
            logMediaBidInfo(sessionContext);
            return true;
        } catch (Exception e) {
            log.error("write response exception {}", e.getMessage());
            throw new Exception(e);
        } finally {
            sessionContext.setAsyncContextCompleted(true);
            asyncContext.getRequest().setAttribute("complete_time", System.currentTimeMillis());
            asyncContext.complete();
            vertx.eventBus().request(EventBusAddress.TO_LOG, sessionContext);
        }
    }

    private void logMediaBidInfo(SessionContext sessionContext) {
        UnifiedRequest uReq = sessionContext.getUnifiedRequest();
        UnifiedResponse uResp = sessionContext.getWinUResp();
        SspEp sspEp = sessionContext.getSspEp();
        int sspId = 0,sspEpId = 0;
        if (sspEp != null) {
            sspId = sspEp.getSspId();
            sspEpId = sspEp.getSspEpId();
        }
        String reqId = uReq.getMediaReqId();


        if (log.isInfoEnabled()) {
            log.info("SSP_ID={}, SSP_EP_ID={}, REQID={}, |to index:{}" ,sspId,sspEpId,reqId, JSON.toJSON(uReq).toString());
            if (uResp != null) {
                log.info("SSP_ID={}, SSP_EP_ID={}, REQID={}, |from index:{}", sspId, sspEpId, reqId, JSON.toJSON(uResp).toString());
            }
        }

    }
}
