package com.iflytek.traffic.procedure.callable;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.protocol.ProtocolParser;
import com.iflytek.traffic.session.SampleLog;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.ssp.SspEp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.Callable;

@AllArgsConstructor
@Slf4j
public class LogSessionSampleCallable implements Callable<Boolean> {

    private static final Logger sessionSampleLog = LoggerFactory.getLogger("abroad_session_sample_log");

    private SessionContext sessionContext;
    @Override
    public Boolean call() throws Exception {
        SspEp sspEp = sessionContext.getSspEp();
        SampleLog sampleLog = new SampleLog();
        sampleLog.setSessionId(sessionContext.getSessionId());
        sampleLog.setSessionStartTime(sessionContext.getStartTime());
        sampleLog.setSspEp(sspEp);
        if (ArrayUtil.isNotEmpty(sessionContext.getReqBody())) {
            ProtocolParser sspParser = sessionContext.getProtocolParser(sspEp.getProtocol());
            Object reqObj = JSON.parseObject(sessionContext.getReqBody(), sspParser.getTypeT());
            sampleLog.setMediaReq(reqObj);
            try {
                String mediaReqStr = new String(sessionContext.getReqBody(), StandardCharsets.UTF_8);
                sampleLog.setMediaReqStr(mediaReqStr);
            } catch (Exception e) {
                log.error("Failed to decode mediaReqStr: ", e);
            }
        }
        if (ArrayUtil.isNotEmpty(sessionContext.getRespBody())) {
            ProtocolParser sspParser = sessionContext.getProtocolParser(sspEp.getProtocol());
            Object respObj = JSON.parseObject(sessionContext.getRespBody(), sspParser.getTypeR());
            sampleLog.setMediaResp(respObj);
        }
        if (MapUtil.isNotEmpty(sessionContext.getDspReqBody())) {
            sessionContext.getDspReqBody().forEach((k, v) -> {
                if (ArrayUtil.isEmpty(v)) {
                    return;
                }
                DspEpObj dspEpObj = sessionContext.getRequestSucDspEp().get(k);
                ProtocolParser dspParser = sessionContext.getProtocolParser(dspEpObj.getProtocol());
                Object reqObj = JSON.parseObject(v, dspParser.getTypeT());
                sampleLog.getDspReq().put(k, reqObj);

            });
        }
        if (MapUtil.isNotEmpty(sessionContext.getDspRespBody())) {
            sessionContext.getDspRespBody().forEach((k, v) -> {
                if (ArrayUtil.isEmpty(v)) {
                    return;
                }
                DspEpObj dspEpObj = sessionContext.getResponseDspEpObj().get(k);
                ProtocolParser dspParser = sessionContext.getProtocolParser(dspEpObj.getProtocol());
                Object respObj = JSON.parseObject(v, dspParser.getTypeR());
                sampleLog.getDspResp().put(k, respObj);
            });
        }
        sampleLog.setDspEpObj(sessionContext.getRequestSucDspEp());
        if (sessionContext.getWinner() != null) {
            DspEpObj dspEpObj = sessionContext.getResponseDspEpObj().get(sessionContext.getWinner());
            sampleLog.setWinDspId(dspEpObj.getDspId());
            sampleLog.setWinDspEpId(dspEpObj.getDspEpId());
        }
        sessionSampleLog.info("{} ssp ep: {}, {}", sessionContext.getStartTime4Log(), sspEp.getSspEpId(), JSON.toJSONString(sampleLog));
        return true;
    }
}
