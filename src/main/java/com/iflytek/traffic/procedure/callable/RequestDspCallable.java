package com.iflytek.traffic.procedure.callable;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.procedure.handler.DspResponseHandler;
import com.iflytek.traffic.protocol.ProtocolParser;
import com.iflytek.traffic.redis.RedisService;
import com.iflytek.traffic.service.MeterService;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.util.GZip;
import com.iflytek.traffic.util.SpringContextHelper;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import io.vertx.core.Future;
import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.http.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@AllArgsConstructor
public class RequestDspCallable implements Callable<Boolean> {

    private Vertx vertx;

    private SessionContext sessionContext;

    private HttpClient client;

    private DspEpObj dspEpObj;

    private static final Map<String, Counter> dspRequestCounter = new ConcurrentHashMap<>();

    private static final Map<String, Timer> dspConnTime = new ConcurrentHashMap<>();

    @Override
    public Boolean call() throws Exception {
        log.debug("try to request dsp <{}> ep <{}>", dspEpObj.getDspId(), dspEpObj.getDspEpId());
        // QPS控制
        int qps = dspEpObj.getQps();
        if (qps > 0) {
            String key = dspEpObj.genQpsCtrlKey();
            if (RedisService.isQpsExceeded(key, qps, 1)) {
                log.info("dsp {} ep {} qps {} not ok.", dspEpObj.getDspId(), dspEpObj.getDspEpId(), qps);
                markQpsFilterCount(dspEpObj);
                sessionContext.increaseCompletedRequests();
                return false;
            }
        }
        //TODO 根据系数决定出价
        ProtocolParser parser = sessionContext.getProtocolParser(dspEpObj.getProtocol());
        Object reqObj = parser.unifiedReq2ProtocolReq(sessionContext, sessionContext.getUnifiedRequest(), dspEpObj);
        // 融量
        parser.trafficFusion(sessionContext, reqObj, dspEpObj);
        // 建立连接并发起请求
        Long timeout = dspEpObj.getTmax() != null ? dspEpObj.getTmax() : dspEpObj.getTimeout();
        Double connTimeout = timeout * 0.5;
        Timer.Sample connStartT = Timer.start();
        Timer.Sample connStartD = Timer.start();
        Timer.Sample reqStartT = Timer.start();
        Timer.Sample reqStartD = Timer.start();
        RequestOptions requestOptions = new RequestOptions();
        requestOptions.setAbsoluteURI(dspEpObj.getPath());
        requestOptions.setMethod(HttpMethod.POST);
        requestOptions.setConnectTimeout(connTimeout.longValue());
        Future<HttpClientRequest> requestFuture = client.request(requestOptions);
        requestFuture.onFailure(e -> {
            log.error("request to dsp {} ep {} failed: {}", dspEpObj.getDspId(), dspEpObj.getDspEpId(), e.getMessage(), e);
            sessionContext.increaseCompletedRequests();
            markDspConnTime(dspEpObj, connStartT, connStartD);
            markFailure(dspEpObj, "connect.failed");
        });
        requestFuture.onSuccess(request -> {
            sessionContext.getRequestSucDspEp().put(dspEpObj.getDspEpId(), dspEpObj);
            markDspConnTime(dspEpObj, connStartT, connStartD);
            // 设置请求头、body
            byte[] body = JSON.toJSONBytes(reqObj, SerializerFeature.NotWriteDefaultValue);
            sessionContext.getDspReqBody().put(dspEpObj.getDspEpId(), body);
            request.putHeader(HttpHeaders.CONTENT_TYPE, "application/json");
            if (dspEpObj.getIsGzip() == 1) {
                request.putHeader(HttpHeaders.CONTENT_ENCODING, "gzip");
                request.putHeader(HttpHeaders.ACCEPT_ENCODING, "gzip");
                body = GZip.compress(body);
            }
            request.putHeader(HttpHeaders.CONTENT_LENGTH, String.valueOf(body.length));
            // 超时时间
            request.idleTimeout(timeout);
            // 注册响应handler
            request.send(Buffer.buffer(body), new DspResponseHandler(sessionContext, dspEpObj, reqStartT, reqStartD));
            // 记录请求QPS
            markRequestCount(dspEpObj);
            markRequestFromMediaCount(sessionContext, dspEpObj);
        });
        return true;
    }

    void markFailure(DspEpObj dspObj, String failure) {
        String totalKey =  "dsp." + failure + ".total";
        if (dspRequestCounter.containsKey(totalKey)) {
            dspRequestCounter.get(totalKey).increment();
        } else {
            synchronized (RequestDspCallable.class) {
                if (dspRequestCounter.containsKey(totalKey)) {
                    dspRequestCounter.get(totalKey).increment();
                } else {
                    MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                    log.info("config dsp failure count: {}", totalKey);
                    Counter total = meterService.count(totalKey);
                    total.increment();
                    dspRequestCounter.put(totalKey, total);
                }
            }
        }
        String dspKey = "dsp." + dspObj.getPrefix() + "." + failure;
        if (dspRequestCounter.containsKey(dspKey)) {
            dspRequestCounter.get(dspKey).increment();
        } else {
            synchronized (RequestDspCallable.class) {
                if (dspRequestCounter.containsKey(dspKey)) {
                    dspRequestCounter.get(dspKey).increment();
                    return;
                }
                MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                log.info("config dsp failure count: {}", dspKey);
                Counter dsp = meterService.count(dspKey);
                dsp.increment();
                dspRequestCounter.put(dspKey, dsp);
            }
        }
    }

    void markRequestCount(DspEpObj dspObj) {
        String totalKey =  "dsp.request.count.total";
        if (dspRequestCounter.containsKey(totalKey)) {
            dspRequestCounter.get(totalKey).increment();
        } else {
            synchronized (RequestDspCallable.class) {
                if (dspRequestCounter.containsKey(totalKey)) {
                    dspRequestCounter.get(totalKey).increment();
                } else {
                    MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                    log.info("config dsp request count: {}", totalKey);
                    Counter total = meterService.count(totalKey);
                    total.increment();
                    dspRequestCounter.put(totalKey, total);
                }
            }
        }
        String dspKey = "dsp." + dspObj.getPrefix() + ".request.count";
        if (dspRequestCounter.containsKey(dspKey)) {
            dspRequestCounter.get(dspKey).increment();
        } else {
            synchronized (RequestDspCallable.class) {
                if (dspRequestCounter.containsKey(dspKey)) {
                    dspRequestCounter.get(dspKey).increment();
                    return;
                }
                MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                log.info("config dsp request count: {}", dspKey);
                Counter dsp = meterService.count(dspKey);
                dsp.increment();
                dspRequestCounter.put(dspKey, dsp);
            }
        }
    }

    void markRequestFromMediaCount(SessionContext sessionContext, DspEpObj dspEpObj) {
        if (sessionContext.getSspEp() == null || StrUtil.isBlank(sessionContext.getSspEp().getPath())) {
            log.warn("sessionContext sspEp is null or path is blank, skip media to dsp request count");
            return;
        }
        String media2DspKey = sessionContext.getSspEp().getPath() + ".to." + dspEpObj.getPrefix() + ".request.count";
        if (dspRequestCounter.containsKey(media2DspKey)) {
            dspRequestCounter.get(media2DspKey).increment();
        } else {
            synchronized (RequestDspCallable.class) {
                if (dspRequestCounter.containsKey(media2DspKey)) {
                    dspRequestCounter.get(media2DspKey).increment();
                } else {
                    MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                    log.info("config media to dsp request count: {}", media2DspKey);
                    Counter media = meterService.count(media2DspKey);
                    media.increment();
                    dspRequestCounter.put(media2DspKey, media);
                }
            }
        }
    }

    void markDspConnTime(DspEpObj dspObj, Timer.Sample sampleT, Timer.Sample sampleD) {
        String totalKey =  "dsp.connect.time";
        if (dspConnTime.containsKey(totalKey)) {
            sampleT.stop(dspConnTime.get(totalKey));
        } else {
            MeterService meterService = SpringContextHelper.getBean(MeterService.class);
            log.info("config dsp connect time: {}", totalKey);
            Timer total = meterService.time(totalKey);
            sampleT.stop(total);
            dspConnTime.put(totalKey, total);
        }

        String dspKey =  "dsp." + dspObj.getPrefix() + ".connect.time";
        if (dspConnTime.containsKey(dspKey)) {
            sampleD.stop(dspConnTime.get(dspKey));
        } else {
            MeterService meterService = SpringContextHelper.getBean(MeterService.class);
            log.info("config dsp connect time: {}", dspKey);
            Timer dsp = meterService.time(dspKey);
            sampleD.stop(dsp);
            dspConnTime.put(dspKey, dsp);
        }
    }

    void markQpsFilterCount(DspEpObj dspObj) {
        String dspKey = "dsp." + dspObj.getPrefix() + ".qps.filter.count";
        if (dspRequestCounter.containsKey(dspKey)) {
            dspRequestCounter.get(dspKey).increment();
        } else {
            synchronized (RequestDspCallable.class) {
                if (dspRequestCounter.containsKey(dspKey)) {
                    dspRequestCounter.get(dspKey).increment();
                    return;
                }
                MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                log.info("config dsp qps filter count: {}", dspKey);
                Counter dsp = meterService.count(dspKey);
                dsp.increment();
                dspRequestCounter.put(dspKey, dsp);
            }
        }
    }
}
