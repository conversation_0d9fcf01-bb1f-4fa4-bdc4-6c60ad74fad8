package com.iflytek.traffic.procedure.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.protocol.ProtocolParser;
import com.iflytek.traffic.service.MeterService;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.request.Impression;
import com.iflytek.traffic.session.request.UnifiedRequest;
import com.iflytek.traffic.session.response.Bid;
import com.iflytek.traffic.session.response.UnifiedResponse;
import com.iflytek.traffic.util.GZip;
import com.iflytek.traffic.util.SpringContextHelper;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.http.HttpClientResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@AllArgsConstructor
public class DspResponseHandler implements Handler<AsyncResult<HttpClientResponse>> {

    private SessionContext sessionContext;

    private DspEpObj dspEpObj;

    private Timer.Sample reqStartT;

    private Timer.Sample reqStartD;

    private static final Map<String, Counter> dspRespCounter = new ConcurrentHashMap<>();

    private static final Map<String, Timer> dspRequestTimer = new ConcurrentHashMap<>();

    private static final Map<String, Counter> failedCounter = new ConcurrentHashMap<>();

    @Override
    public void handle(AsyncResult<HttpClientResponse> event) {
        markRequestTime(dspEpObj);
        if (event.succeeded()) {
            handleSucceed(event.result());
        } else {
            if (event.cause() != null) {
                log.error("dsp <{}> ep {} response failed, {}", dspEpObj.getDspId(), dspEpObj.getDspEpId(), event.cause().getMessage(), event.cause());
            }
            markFailure(dspEpObj, "response.failed");
            sessionContext.increaseCompletedRequests();
        }
    }

    public void handleSucceed(HttpClientResponse resp) {
        log.debug("Received dsp <{}> ep <{}> response with status code: {}", dspEpObj.getDspId(), dspEpObj.getDspEpId(), resp.statusCode());
        if (resp.statusCode() == 200) {
            markResponse(dspEpObj, "valid");
            markResponseForMedia(sessionContext, "valid");
        } else {
            markResponse(dspEpObj, "invalid");
        }
        long bodyHandleStart = System.currentTimeMillis();
        resp.body().onComplete(result -> {
            if (result.succeeded()) {
                try {
                    ProtocolParser parser = sessionContext.getProtocolParser(dspEpObj.getProtocol());
                    byte[] respBytes = result.result().getBytes();
                    String encoding = resp.getHeader("Content-Encoding");
                    if (StrUtil.isNotBlank(encoding) && encoding.equalsIgnoreCase("gzip")) {
                        log.debug("dsp <{}> ep <{}> response is gzip encoded, decompressing...", dspEpObj.getDspId(), dspEpObj.getDspEpId());
                        respBytes = GZip.uncompress(result.result().getBytes());
                    }
                    UnifiedResponse response = parser.respBody2UnifiedResp(resp.statusCode(), respBytes, dspEpObj);
                    parseDspResponseEnd(sessionContext.getUnifiedRequest(), response);
                    sessionContext.getDspUnifiedResp().put(dspEpObj.getDspEpId(), response);
                    sessionContext.getDspRespBody().put(dspEpObj.getDspEpId(), result.result().getBytes());
                    sessionContext.getResponseDspEpObj().put(dspEpObj.getDspEpId(), dspEpObj);
                } catch (Exception e) {
                    log.error("parse dsp {} ep {}'s response error: {}", dspEpObj.getDspId(), dspEpObj.getDspEpId(), e.getMessage(), e);
                    markFailure(dspEpObj, "parseResp.error");
                }
            } else {
                log.error("dsp <{}> ep <{}> get body error : {}", dspEpObj.getDspId(), dspEpObj.getDspEpId(), result.cause().getMessage(), result.cause());
                markFailure(dspEpObj, "body.failed");
            }
            markBodyHandleTime(dspEpObj, System.currentTimeMillis() - bodyHandleStart);
            sessionContext.increaseCompletedRequests();
        });
    }

    void markRequestTime(DspEpObj dspEpObj) {
        String totalKey = "dsp.request.time.total";
        if (dspRequestTimer.containsKey(totalKey)) {
            reqStartT.stop(dspRequestTimer.get(totalKey));
        } else {
            MeterService meterService = SpringContextHelper.getBean(MeterService.class);
            log.debug("config dsp request timer: {}", totalKey);
            Timer total = meterService.time(totalKey);
            reqStartT.stop(total);
            dspRequestTimer.put(totalKey, total);
        }
        String dspKey = "dsp." + dspEpObj.getPrefix() + ".request.time";
        if (dspRequestTimer.containsKey(dspKey)) {
            reqStartD.stop(dspRequestTimer.get(dspKey));
        } else {
            MeterService meterService = SpringContextHelper.getBean(MeterService.class);
            log.info("config dsp request time: {}", dspKey);
            Timer dsp = meterService.time(dspKey);
            reqStartD.stop(dsp);
            dspRequestTimer.put(dspKey, dsp);
        }
    }

    void markResponse(DspEpObj dspEpObj, String status) {
        String totalStatus = "dsp.response." + status + ".count.total";
        if (dspRespCounter.containsKey(totalStatus)) {
            dspRespCounter.get(totalStatus).increment();
        } else {
            synchronized (DspResponseHandler.class) {
                if (dspRespCounter.containsKey(totalStatus)) {
                    dspRespCounter.get(totalStatus).increment();
                }
                MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                log.info("config dsp response count: {}", totalStatus);
                Counter total = meterService.count(totalStatus);
                total.increment();
                dspRespCounter.put(totalStatus, total);
            }
        }
        String dspStatus = "dsp." + dspEpObj.getPrefix() + ".response." + status + ".count";
        if (dspRespCounter.containsKey(dspStatus)) {
            dspRespCounter.get(dspStatus).increment();
        } else {
            synchronized (DspResponseHandler.class) {
                if (dspRespCounter.containsKey(dspStatus)) {
                    dspRespCounter.get(dspStatus).increment();
                    return;
                }
                MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                log.info("config dsp response count: {}", dspStatus);
                Counter dsp = meterService.count(dspStatus);
                dsp.increment();
                dspRespCounter.put(dspStatus, dsp);
            }
        }
    }

    void markResponseForMedia(SessionContext sessionContext, String status) {
        String responseForMedia = "dsp.response." + status + "." + sessionContext.getSspEp().getPath() + ".count";
        if (dspRespCounter.containsKey(responseForMedia)) {
            dspRespCounter.get(responseForMedia).increment();
        } else {
            synchronized (DspResponseHandler.class) {
                if (dspRespCounter.containsKey(responseForMedia)) {
                    dspRespCounter.get(responseForMedia).increment();
                }
                MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                log.info("config dsp response for media count: {}", responseForMedia);
                Counter response = meterService.count(responseForMedia);
                response.increment();
                dspRespCounter.put(responseForMedia, response);
            }
        }

    }

    void markFailure(DspEpObj dspEpObj, String failure) {
        String totalKey = "dsp." + failure + ".total";
        if (failedCounter.containsKey(totalKey)) {
            failedCounter.get(totalKey).increment();
        } else {
            synchronized (DspResponseHandler.class) {
                if (failedCounter.containsKey(totalKey)) {
                    failedCounter.get(totalKey).increment();
                } else {
                    MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                    log.info("config dsp failure count: {}", totalKey);
                    Counter total = meterService.count(totalKey);
                    total.increment();
                    failedCounter.put(totalKey, total);
                }
            }
        }
        String dspKey = "dsp." + dspEpObj.getPrefix() + "." + failure;
        if (failedCounter.containsKey(dspKey)) {
            failedCounter.get(dspKey).increment();
        } else {
            synchronized (DspResponseHandler.class) {
                if (failedCounter.containsKey(dspKey)) {
                    failedCounter.get(dspKey).increment();
                    return;
                }
                MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                log.info("config dsp failure count: {}", dspKey);
                Counter dsp = meterService.count(dspKey);
                dsp.increment();
                failedCounter.put(dspKey, dsp);
            }
        }
    }

    void markBodyHandleTime(DspEpObj dspEpObj, long mills) {
        String totalKey = "dsp.body.handle.time.total";
        if (dspRequestTimer.containsKey(totalKey)) {
            dspRequestTimer.get(totalKey).record(mills, TimeUnit.MILLISECONDS);
        } else {
            MeterService meterService = SpringContextHelper.getBean(MeterService.class);
            log.debug("config dsp body handle timer: {}", totalKey);
            Timer total = meterService.time(totalKey);
            total.record(mills, TimeUnit.MILLISECONDS);
            dspRequestTimer.put(totalKey, total);
        }
        String dspKey = "dsp." + dspEpObj.getPrefix() + ".body.handle.time";
        if (dspRequestTimer.containsKey(dspKey)) {
            dspRequestTimer.get(dspKey).record(mills, TimeUnit.MILLISECONDS);
        } else {
            MeterService meterService = SpringContextHelper.getBean(MeterService.class);
            log.info("config dsp body handle time: {}", dspKey);
            Timer dsp = meterService.time(dspKey);
            dsp.record(mills, TimeUnit.MILLISECONDS);
            dspRequestTimer.put(dspKey, dsp);
        }
    }

    private void parseDspResponseEnd(UnifiedRequest request, UnifiedResponse response) {
        // 设置广告位类型
        setBidAdType(request, response);
    }

    private void setBidAdType(UnifiedRequest request, UnifiedResponse response) {
        if (response == null || CollUtil.isEmpty(response.getSeatbids()) || CollUtil.isEmpty(response.getImpId2Bid())) {
            return;
        }
        Map<String, Impression> imps = request.getImps();
        if (CollUtil.isEmpty(imps)) {
            return ;
        }

        Collection<Bid> bids = response.getImpId2Bid().values();
        for (Bid bid : bids) {
            if (imps.containsKey(bid.getImpId())) {
                bid.setAdType(imps.get(bid.getImpId()).getAdType());
            }
        }
    }
}
