package com.iflytek.traffic.procedure.handler;

import com.iflytek.traffic.procedure.EventBusAddress;
import com.iflytek.traffic.session.SessionContext;
import io.vertx.core.Context;
import io.vertx.core.Handler;
import io.vertx.core.Vertx;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@AllArgsConstructor
public class DefaultExceptionHandler implements Handler<Throwable> {

    private Vertx vertx;

    @Override
    public void handle(Throwable event) {
        log.error("vertx catch exception: {}, {}", event.getMessage(), event);
        event.printStackTrace();
        Context context = vertx.getOrCreateContext();
        SessionContext sessionContext = context.get("session");
        vertx.eventBus().request(EventBusAddress.RESPONSE_TO_MEDIA, sessionContext);
    }
}
