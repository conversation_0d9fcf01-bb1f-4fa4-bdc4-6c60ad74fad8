package com.iflytek.traffic.procedure;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.traffic.data.entity.DspEpQps;
import com.iflytek.traffic.data.provider.DspDataProvider;
import com.iflytek.traffic.data.provider.DspEpQpsProvider;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.dsp.DspEpSupportWrapper;
import com.iflytek.traffic.procedure.callable.RequestDspCallable;
import com.iflytek.traffic.service.MeterService;
import com.iflytek.traffic.service.QpsControlService;
import com.iflytek.traffic.service.TimeWheel;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.util.SpringContextHelper;
import io.micrometer.core.instrument.Counter;
import io.vertx.core.http.HttpClient;
import io.vertx.core.http.HttpClientOptions;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@EventListener(listen = EventBusAddress.REQUEST_DSP)
@Slf4j
public class RequestDspVerticle extends BasicVerticle {

    private static final Map<Integer, HttpClient> dspClient = new ConcurrentHashMap<>();

    private static final Map<String, Counter> dspQpsFilterCounter = new ConcurrentHashMap<>();

    @Override
    void doHandle(SessionContext sessionContext) {
        // 确认需要放量的dsp ep id, 命中圈量策略的dsp ep + 未配置圈量策略的dsp ep
        Set<Integer> dspEpId = CollUtil.newHashSet();
        if (CollUtil.isNotEmpty(sessionContext.getNonConfigDspEpId())) {
            dspEpId.addAll(sessionContext.getNonConfigDspEpId());
        }
        if (MapUtil.isEmpty(sessionContext.getAfterFilterDesMap())) {
            log.debug("empty qualified dsp ep support.");
        }
        sessionContext.getAfterFilterDesMap().forEach((k, v) -> {
            for (DspEpSupportWrapper w : v) {
                dspEpId.add(w.getDspEpId());
            }
        });
        if (CollUtil.isEmpty(dspEpId)) {
            log.info("no qualified dsp ep.");
            vertx.eventBus().request(EventBusAddress.RESPONSE_TO_MEDIA, sessionContext);
            return;
        }
        // 记录qps
        DspEpQpsProvider dspEpQpsProvider = SpringContextHelper.getBean(DspEpQpsProvider.class);
        dspEpQpsProvider.recordAfterIndexDspEpQps(sessionContext.getSspEp(), sessionContext.getUnifiedRequest(), dspEpId);
        // 发起请求
        DspDataProvider dspDataProvider = SpringContextHelper.getBean(DspDataProvider.class);
        QpsControlService qpsControlService = SpringContextHelper.getBean(QpsControlService.class);
        long maxTime = 0l;
        long currentTime = System.currentTimeMillis();
        String str = SpringContextHelper.getProperty("ssp.tmax.inner.handle-time");
        long handleTime = StrUtil.isBlank(str) ? 30L : Long.parseLong(str);
        long sspTmaxLeft = sessionContext.getUnifiedRequest().getTmax() - (currentTime - sessionContext.getStartTime());
        if (sspTmaxLeft <= 0 || sspTmaxLeft < handleTime) {
            log.warn("ssp tmax {} left is {}, set to {}.", sessionContext.getUnifiedRequest().getTmax(), sspTmaxLeft, 2 * handleTime);
            sspTmaxLeft = 2 * handleTime;
        }
        Map<Integer, DspEpObj> requestDspEpObj = new HashMap<>();
        Map<Integer, HttpClient> requestDspEpClient = new HashMap<>();
        for (Integer id : dspEpId) {
            DspEpObj dspEpObj = dspDataProvider.getDspEpObj(id);
            if (dspEpObj == null) {
                log.info("dsp ep {} no config.", id);
                continue;
            }
            DspEpQps.KeyAndQps<String, Integer> subQps = dspEpQpsProvider.getDspEpQpsByReq(sessionContext.getSspEp(), dspEpObj, sessionContext.getUnifiedRequest());
            if (subQps != null) {
                String key = subQps.key();
                Integer qps = subQps.qps();
                if (!qpsControlService.qpsCtrl(key, qps)) {
                    log.info("dsp ep {} qps limit exceeded for key {}, skip.", id, key);
                    markQpsFilterCount(dspEpObj);
                    continue;
                }
            }

            if (!qpsControlService.qpsCtrl(dspEpObj.genQpsCtrlKey(), dspEpObj.getQps())) {
                log.info("dsp ep {} qps limit exceeded, skip.", id);
                markQpsFilterCount(dspEpObj);
                continue;
            }
            requestDspEpObj.put(id, dspEpObj);
            HttpClient client = getDspHttpClient(dspEpObj);
            requestDspEpClient.put(id, client);
            long dspTmax = Math.min(sspTmaxLeft - handleTime, dspEpObj.getTimeout());
            dspEpObj.setTmax(dspTmax);
            maxTime = Math.max(dspTmax, maxTime);
        }
        long endTime = currentTime + maxTime;
        sessionContext.setRequestDspEndTime(endTime);
        sessionContext.setRequestSize(requestDspEpObj.size());
        // 加入监听
        TimeWheel timeWheel = SpringContextHelper.getBean(TimeWheel.class);
        timeWheel.addSessionContext(sessionContext);
        // 记录真实请求QPS
        dspEpQpsProvider.recordDspEpReqQps(sessionContext.getSspEp(), sessionContext.getUnifiedRequest(), requestDspEpObj.keySet());
        // 异步请求
        for (DspEpObj dspEpObj : requestDspEpObj.values()) {
            HttpClient client = requestDspEpClient.get(dspEpObj.getDspEpId());
            vertx.executeBlocking(new RequestDspCallable(vertx, sessionContext, client, dspEpObj), false).onComplete(result -> {
                if (result.succeeded()) {
                    log.info("request to dsp {} ep {} success.", dspEpObj.getDspId(), dspEpObj.getDspEpId());
                } else {
                    // 请求发送失败，标记完成
                    sessionContext.increaseCompletedRequests();
                    log.error("request to dsp {} ep {} failed, {}.", dspEpObj.getDspId(), dspEpObj.getDspEpId(), result.cause().getMessage(), result.cause());
                }
            });
        }
    }

    HttpClient getDspHttpClient(DspEpObj dspEpObj) {
        if (dspClient.containsKey(dspEpObj.getDspId())) {
            return dspClient.get(dspEpObj.getDspId());
        } else {
            synchronized (RequestDspVerticle.class) {
                if (dspClient.containsKey(dspEpObj.getDspId())) {
                    return dspClient.get(dspEpObj.getDspId());
                }
                HttpClientOptions options = new HttpClientOptions().setKeepAlive(true);
                options.setTcpNoDelay(true);
                String keepAliveTimeout = SpringContextHelper.getProperty("httpclient.keepalive.timeout");
                String idleTimeout = SpringContextHelper.getProperty("httpclient.idle.timeout");
                String cleanPeriod = SpringContextHelper.getProperty("httpclient.pool.clean.period");
                String poolSize = SpringContextHelper.getProperty("httpclient.pool.max.poolSize");
                String queueSize = SpringContextHelper.getProperty("httpclient.pool.max.queueSize");
                options.setMaxPoolSize(Integer.parseInt(poolSize));
                options.setMaxWaitQueueSize(Integer.parseInt(queueSize));
                options.setKeepAliveTimeout(Integer.parseInt(keepAliveTimeout));
                options.setPoolCleanerPeriod(Integer.parseInt(cleanPeriod));
                options.setIdleTimeout(Integer.parseInt(idleTimeout));
                options.setIdleTimeoutUnit(TimeUnit.SECONDS);
                HttpClient client = vertx.createHttpClient(options);

                dspClient.put(dspEpObj.getDspId(), client);
                return client;
            }
        }
    }

    void markQpsFilterCount(DspEpObj dspObj) {
        String dspKey = "dsp." + dspObj.getPrefix() + ".qps.filter.count";
        if (dspQpsFilterCounter.containsKey(dspKey)) {
            dspQpsFilterCounter.get(dspKey).increment();
        } else {
            synchronized (RequestDspVerticle.class) {
                if (dspQpsFilterCounter.containsKey(dspKey)) {
                    dspQpsFilterCounter.get(dspKey).increment();
                    return;
                }
                MeterService meterService = SpringContextHelper.getBean(MeterService.class);
                log.info("config dsp qps filter count: {}", dspKey);
                Counter dsp = meterService.count(dspKey);
                dsp.increment();
                dspQpsFilterCounter.put(dspKey, dsp);
            }
        }
    }
}
