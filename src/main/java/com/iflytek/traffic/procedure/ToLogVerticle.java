package com.iflytek.traffic.procedure;

import com.iflytek.traffic.log.LogService;
import com.iflytek.traffic.procedure.callable.LogBidTraceCallable;
import com.iflytek.traffic.procedure.callable.LogSessionSampleCallable;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.util.SpringContextHelper;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @datetime 2025/5/12 9:44
 */
@Slf4j
@EventListener(listen = EventBusAddress.TO_LOG)
public class ToLogVerticle extends BasicVerticle {
    @Override
    void doHandle(SessionContext sessionContext) {
//		vertx.executeBlocking(new LogBidTraceCallable(System.currentTimeMillis(), sessionContext), false).onComplete(res -> {
//			if (res.succeeded()) {
//				log.info("log bid trace success.");
//			} else {
//				log.error("log bid trace failed, {}.", res.cause().getMessage(), res.cause());
//			}
//		});
        // 记录日志改为同步
        try {
            LogService logService = SpringContextHelper.getBean(LogService.class);

            // 记录日志
            logService.logAdxTrace(sessionContext);
            logService.logRetrievalLog(sessionContext);
        } catch (Exception e) {
            log.error("log trace error: {}", e.getMessage(), e);
        }

        // 判断是否输出采样日志
        if (sessionContext.logSample()) {
            log.info("log session sample, sessionId: {}, sspEpId: {}", sessionContext.getSessionId(), sessionContext.getSspEp().getSspEpId());
            vertx.executeBlocking(new LogSessionSampleCallable(sessionContext), false).onComplete(res -> {
                if (res.succeeded()) {
                    log.info("log session sample success.");
                } else {
                    log.error("log session sample failed, {}.", res.cause().getMessage(), res.cause());
                }
            });
        }
    }
}
