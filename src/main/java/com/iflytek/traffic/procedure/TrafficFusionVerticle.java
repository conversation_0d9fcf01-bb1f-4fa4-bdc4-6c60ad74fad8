package com.iflytek.traffic.procedure;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.traffic.data.provider.DspDataProvider;
import com.iflytek.traffic.data.provider.FusionProvider;
import com.iflytek.traffic.dsp.DspEpSupportWrapper;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.request.Impression;
import com.iflytek.traffic.session.request.UnifiedRequest;
import com.iflytek.traffic.ssp.SspEp;
import com.iflytek.traffic.util.SpringContextHelper;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@EventListener(listen = EventBusAddress.TRAFFIC_FUSION)
@Slf4j
public class TrafficFusionVerticle extends BasicVerticle {
    @Override
    void doHandle(SessionContext sessionContext) {
        Set<Integer> dspIds = new HashSet<>();
        if (CollUtil.isNotEmpty(sessionContext.getNonConfigDspEpId())) {
            DspDataProvider dspDataProvider = SpringContextHelper.getBean(DspDataProvider.class);
            for (Integer dspEpId : sessionContext.getNonConfigDspEpId()) {
                Integer dspId = dspDataProvider.getDspId(dspEpId);
                if (dspId != null) {
                    dspIds.add(dspId);
                }
            }
        }
        if (CollUtil.isNotEmpty(sessionContext.getAfterFilterDesMap())) {
            sessionContext.getAfterFilterDesMap().values().forEach(i -> {
                for (DspEpSupportWrapper w : i) {
                    dspIds.add(w.getDspId());
                }
            });
        }
        if (CollUtil.isEmpty(dspIds)) {
            log.debug("empty request dsp. no need to fusion.");
        } else {
            Map<Integer, Map<String, FusionProvider.TargetTraffic>> result = new HashMap<>();
            FusionProvider fusionProvider = SpringContextHelper.getBean(FusionProvider.class);
            UnifiedRequest uniReq = sessionContext.getUnifiedRequest();
            if (CollUtil.isNotEmpty(uniReq.getImps())) {
                String pkg = "";
                if (uniReq.getApp() != null && StrUtil.isNotBlank(uniReq.getApp().getBundle())) {
                    pkg = uniReq.getApp().getBundle();
                }
                SspEp sspEp = sessionContext.getSspEp();
                for (Integer d : dspIds) {
                    Map<String, FusionProvider.TargetTraffic> fusionMap = new HashMap<>();
                    for (Impression imp : uniReq.getImps().values()) {
                        String tagId = imp.getTagId();
                        FusionProvider.TargetTraffic targetTraffic =
                            fusionProvider.queryAppFusion(sspEp.getSspId(), d, pkg, tagId, imp.getAdType().getValue());
                        if (targetTraffic != null) {
                            fusionMap.put(imp.getImpId(), targetTraffic);
                        }
                    }
                    if (MapUtil.isNotEmpty(fusionMap)) {
                        result.put(d, fusionMap);
                    }
                }
                sessionContext.setDspFusionTarget(result);
            }
        }
        vertx.eventBus().request(EventBusAddress.REQUEST_DSP, sessionContext);
    }
}
