package com.iflytek.traffic.procedure;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.iflytek.traffic.area.IpData;
import com.iflytek.traffic.area.IpDataService;
import com.iflytek.traffic.data.provider.AppCategoryProvider;
import com.iflytek.traffic.data.provider.SspDataProvider;
import com.iflytek.traffic.protocol.ProtocolParser;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.asset.Asset;
import com.iflytek.traffic.session.request.*;
import com.iflytek.traffic.ssp.SspEp;
import com.iflytek.traffic.util.Md5Utils;
import com.iflytek.traffic.util.SpringContextHelper;
import com.iflytek.traffic.util.Util;
import com.iflytek.traffic.util.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

@EventListener(listen = EventBusAddress.PARSE_REQUEST)
@Slf4j
public class ParseSspRequestVerticle extends BasicVerticle {

    @Override
    void doHandle(SessionContext sessionContext) {
        SspEp sspEp = sessionContext.getSspEp();
        // 解析请求
        ProtocolParser protocolParser = sessionContext.getProtocolParser(sspEp.getProtocol());
        UnifiedRequest unifiedReq = null;
        try {
            unifiedReq = protocolParser.reqBody2UnifiedReq(sessionContext.getReqBody(), sessionContext.getSspEp());
            sessionContext.setUnifiedRequest(unifiedReq);
            parseMediaRequestEnd(sessionContext, unifiedReq);
        } catch (Exception e) {
            log.error("parse ssp {} ep {} request error: {}", sspEp.getSspId(), sspEp.getSspEpId(), e.getMessage(), e);
            vertx.eventBus().request(EventBusAddress.RESPONSE_TO_MEDIA, sessionContext);
            return;
        }
        // TODO 包名黑白名单校验
        SspDataProvider dataProvider = SpringContextHelper.getBean(SspDataProvider.class);
        String pkg = unifiedReq.getApp() != null ? unifiedReq.getApp().getBundle() : null;
        if (!dataProvider.isAppInclude(sspEp.getSspId(), pkg)) {
            log.info("ssp {} ep {} app {} not include.", sspEp.getSspId(), sspEp.getSspEpId(), unifiedReq.getApp().getBundle());
            vertx.eventBus().request(EventBusAddress.RESPONSE_TO_MEDIA, sessionContext);
            return;
        }
        vertx.eventBus().request(EventBusAddress.CHOSE_DSP, sessionContext);
    }

    private void parseMediaRequestEnd(SessionContext sessionContext, UnifiedRequest request) {
        if (request.getDevice() != null) {
            setIpV4OrV6(request.getDevice());
            setIp(request.getDevice());
            setRegion(request.getDevice(), request.getReqTime());
        }
        SspEp sspEp = sessionContext.getSspEp();
        // 2.解析流量类型
        // 设备号id
        setGlobalUniqueValue(request, sspEp);
        // banner format
        setBannerFormatSize(request);

        setSubSlotType(request);

        // 计算索引
        // ssp & ep
        request.getReqDnf().add(Util.GenDNFByStr("ss", String.valueOf(sspEp.getSspId())));
        request.getReqDnf().add(Util.GenDNFByStr("se", String.valueOf(sspEp.getSspEpId())));
        // 包名 & 地域
        if (request.getApp() != null && StrUtil.isNotBlank(request.getApp().getBundle())) {
            String bundle = request.getApp().getBundle();
            request.getReqDnf().add(Util.GenDNFByStr("pk", bundle));
            AppCategoryProvider appCategoryProvider = SpringContextHelper.getBean(AppCategoryProvider.class);
            Integer category = appCategoryProvider.getPkgCategory(bundle);
            if (category != null) {
                request.getReqDnf().add(Util.GenDNFByStr("ac", String.valueOf(category)));
            }
        }
        if (request.getDevice() != null && request.getDevice().getRegionInfo() != null && request.getDevice().getRegionInfo().getRegion() != null) {
            request.getReqDnf().add(Util.GenDNFByStr("rg", String.valueOf(request.getDevice().getRegionInfo().getRegion())));
        }
        // 广告形式 & display manager
        if (CollUtil.isNotEmpty(request.getImps())) {
            for (Impression impression : request.getImps().values()) {
                impression.getImpDnf().add(Util.GenDNFByStr("st", impression.getAdType().toString()));
                if (StrUtil.isNotBlank(impression.getDisplaymanager())) {
                    impression.getImpDnf().add(Util.GenDNFByStr("dm", impression.getDisplaymanager()));
                }
                if (CollUtil.isNotEmpty(impression.getSubSlotType())) {
                    for (Asset.AdType slotType : impression.getSubSlotType()) {
                        impression.getImpDnf().add(Util.GenDNFByStr("su", slotType.toString()));
                    }
                    
                }
                if (impression.getInstl() != null) {
                    impression.getImpDnf().add(Util.GenDNFByStr("in", impression.getInstl().toString()));
                }
            }
        }
        // os & make
        if (request.getDevice() != null) {
            if (request.getDevice().getOs() != null) {
                request.getReqDnf().add(Util.GenDNFByStr("os", String.valueOf(request.getDevice().getOs().getValue())));
            }

            if (StrUtil.isNotBlank(request.getDevice().getMake())) {
                request.getReqDnf().add(Util.GenDNFByStr("br", request.getDevice().getMake().toLowerCase()));
            }
        }
        // 图片&视频尺寸
        if (CollUtil.isNotEmpty(request.getImps())) {
            List<String> hws = new ArrayList<>();
            for (Impression impression : request.getImps().values()) {
                if (CollUtil.isNotEmpty(impression.getAdUnitSize())) {
                    hws.addAll(impression.getAdUnitSize());
                }
            }
            if (CollUtil.isNotEmpty(hws)) {
                for (String hw : hws) {
                    request.getReqDnf().add(Util.GenDNFByStr("hw", hw));
                }
            }
        }

        if (request.getTmax() == null) {
            request.setTmax(Constants.T_MAX_DEFAULT);
        }
    }

    void setSubSlotType(UnifiedRequest request) {
        for (Impression impression : request.getImps().values()) {
            if (impression.getExt() != null && impression.getExt().getReward() != null) {
                if (impression.getExt().getReward() == 1) {
                    impression.getSubSlotType().add(Asset.AdType.SUPPER_REWARD_VIDEO);
                } else {
                    impression.getSubSlotType().add(Asset.AdType.SUPPER_NON_REWARD_VIDEO);
                }
            }
        }
    }

    void setBannerFormatSize(UnifiedRequest request) {
        for (Impression impression : request.getImps().values()) {
            if (impression.getBanner() != null && CollUtil.isNotEmpty(impression.getBanner().getFormat())) {
                for (Format format : impression.getBanner().getFormat()) {
                    if (format.getW() != null && format.getH() != null) {
                        impression.getAdUnitSize().add(format.getW() + "*" + format.getH());
                    }
                }
            }
        }
    }

    private void setIpV4OrV6(Device device) {
        if (StringUtils.isNotBlank(device.getIp())) {
            if (device.getIp().contains(":")) {
                if (StringUtils.isBlank(device.getIpv6())) {
                    device.setIpv6(device.getIp());
                }
            } else {
                if (StringUtils.isBlank(device.getIpv4())) {
                    device.setIpv4(device.getIp());
                }
            }
        }
    }

    private void setIp(Device device) {
        if (StringUtils.isBlank(device.getIp())) {
            if (StringUtils.isNotBlank(device.getIpv4())) {
                device.setIp(device.getIpv4());
            } else if (StringUtils.isNotBlank(device.getIpv6())) {
                device.setIp(device.getIpv6());
            }
        }
    }

    private void setRegion(Device device, long bidTimeBegin) {
        IpData.IpAreaData.Area area1 = null;
        String ipV4 = device.getIpv4();
        String ipV6 = device.getIpv6();

        IpDataService ipDataService = SpringContextHelper.getBean(IpDataService.class);
        if (StringUtils.isNotBlank(ipV4)) {
            log.debug("ip_area ipv4: {}", ipV4);
            area1 = ipDataService.getIpInfoByIp2Location(ipV4, bidTimeBegin);
            if (area1 != null) {
                setRegionInfo(area1, device);
                log.debug("Ip2Location found ip : {}", ipV4);
            }
        } else if (StringUtils.isNotBlank(ipV6)) {
            log.debug("ip_area ipv6: {}", ipV6);
            area1 = ipDataService.getIpInfoByIp2Location(ipV6, bidTimeBegin);
            if (area1 != null) {
                setRegionInfo(area1, device);
                log.debug("Ip2Location found ip : {}", ipV6);
            }
        }
        if (area1 == null) {
            do {
                try {
                    if (StringUtils.isNotBlank(ipV4)) {
                        log.debug("ip_area ipv4: {}", ipV4);
                        //req.region = ipDataService.getAreaCode4Ipv4(req.ipV4, req.bidTimeBegin);
                        IpData.IpAreaData.Area area = ipDataService.getAreaIpV4(ipV4, bidTimeBegin);
                        if (area != null) {
                            log.debug("ip_area_overseas found ip : {}", ipV4);
                            setRegionInfo(area, device);
                        }
                        break;
                    }
                } catch (Exception e) {
                    log.warn("ipDataService.getAreaCode exception, ip={}, err={}", device.getIp(), e.getMessage());
                }
            } while (false);
//			//尝试根据country iso code直接获取编码
//			Long queryCountryCode = SpecialParameters.queryCountryCodeByStr(req.adxGeoCountry);
//			if (queryCountryCode.longValue() != 0) {
//				req.region = queryCountryCode;
//				req.countryName = "";
//				req.countryCode = req.adxGeoCountry != null ? req.adxGeoCountry.substring(0, 2) : "";
//			}
        }
        log.debug("ip_area region: {}", device.getRegionInfo().getRegion());
    }

    private void setRegionInfo(IpData.IpAreaData.Area area, Device device) {
        Device.RegionInfo regionInfo = new Device.RegionInfo();
        if (area.getCityId() != null) {
            regionInfo.setRegion(area.getCityId());
        } else if (area.getProvinceId() != null) {
            regionInfo.setRegion(area.getProvinceId());
        } else if (area.getCountryId() != null) {
            regionInfo.setRegion(area.getCountryId());
        }

        if (area.getCountryId() != null) {
            regionInfo.setCountryId(area.getCountryId());
        }

        regionInfo.setCityName(area.getCityName());
        regionInfo.setProvinceName(area.getProvinceName());
        regionInfo.setCountryName(area.getCountryName());
        regionInfo.setCountryCode(area.getCountryCode());
        if (area.isIp2location()) {
            regionInfo.setIp2locationRegion(area.getRegion());
            if (device.getGeo() != null) {
                if (device.getGeo().getLat() == null) {
                    device.getGeo().setLat(area.getLatitude());
                }
                if (device.getGeo().getLon() == null) {
                    device.getGeo().setLon(area.getLongitude());
                }
            }
            regionInfo.setZipCode(area.getZipCode());
            regionInfo.setTimezone(area.getTimezone());
        }
        device.setRegionInfo(regionInfo);
    }

    private void setGlobalUniqueValue(UnifiedRequest request, SspEp sspEp) {
        // 生成全局唯一的ssp tag
        if (CollUtil.isNotEmpty(request.getImps())) {
            for (Impression imp : request.getImps().values()) {
                imp.setAdxTag(Md5Utils.digestAsHex(sspEp.getSspId() + "_" + imp.getTagId()));
            }
        }
        // 生成全局唯一的ssp app
        App app = request.getApp();
        if (app != null) {
            app.setAdxApp(Md5Utils.digestAsHex(sspEp.getSspId() + "_" + app.getBundle()));
        }
    }
}
