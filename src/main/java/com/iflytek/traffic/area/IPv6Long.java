package com.iflytek.traffic.area;

import org.apache.commons.lang3.StringUtils;

/**
 * IPv6地址对应的数值类型，方便比较、排序
 * 
 * <AUTHOR>
 *
 */
public class IPv6Long implements Comparable<IPv6Long> {
	// 从高往低，data[0]存储第一个32位，data[1]存储第二个32位，data[2]存储第三个32位，data[3]存储最后一个32位
	long[] data;

	public static IPv6Long fromString(String ipv6) throws Exception {
		try {
			if (StringUtils.isBlank(ipv6))
				throw new Exception("ipv6 is blank");

			String[] parts = StringUtils.splitPreserveAllTokens(ipv6.trim(), ":");

			int partIdx = 7; // ipv6分八段，从后往前解析，partIdx用来指示当前解析到哪一段了
			int doubleColonCount = 0; // ipv6中最多只能出现一次::，以此来记录出现的次数
			int blankCount = 0; // 记录一共出现多少个空格
			boolean isBlank = false; // ipv6采用::缩写，以:分隔会出现1至多个空字符的情况，以此标识目前正遇到空字符
			boolean hasIpv4 = false; // 最后一段是否是ipv4
			DoubleColonPosition doubleColonPosition = DoubleColonPosition.Unknown;

			long value0 = 0; // ipv6最左侧两个part解析出的值
			long value1 = 0;
			long value2 = 0;
			long value3 = 0;

			label: for (int i = parts.length - 1; i >= 0; i--) {
				String part = parts[i];
				long value = 0; // part解析成long值

				// ipv6的各种格式解析：空字符展开、兼容ipv4解析。并解析part的值，对应到partIdx坐标上
				do {
					if (partIdx == 7) {
						int idx = part.indexOf("/");
						if (idx >= 0)
							part = part.substring(0, idx);

						if (part.contains(".")) {
							hasIpv4 = true;
							partIdx--; // 最后一段是ipv4，由于ipv4占两段，partIdx往前多跳一段
							value = parseIpv4(part);
							break;
						}
					}

					if ("".equals(part)) {
						if (partIdx == 7)
							doubleColonPosition = setPosition(doubleColonPosition, DoubleColonPosition.RightMost);
						else
							doubleColonPosition = setPosition(doubleColonPosition, DoubleColonPosition.Inner);
						blankCount++;
						isBlank = true;
						continue label;
					}

					if (isBlank) {
						if (++doubleColonCount > 1)
							throw new Exception("more than 2 double-colon");
						isBlank = false;
						int expand = 8 - (parts.length - blankCount + (hasIpv4 ? 1 : 0));
						if (expand <= 0)
							throw new Exception("more than 8 parts");
						partIdx -= expand; // 把连续的多个空字符压缩成一个空字符，然后计算扩展量
					}

					if (part.length() > 4 && part.startsWith("0"))
						throw new Exception("part length greater than 4");
					value = Long.valueOf(part, 16);
					if (value > 65535)
						throw new Exception("part value out of range");
				} while (false);

				// 判断最右边和中间连续的冒号是否合法
				switch (doubleColonPosition) {
				case RightMost:
					// 当前从右往左碰到第一个非空
					// 最右侧要么没有冒号，要么由两个冒号，其余都是错误的
					switch (blankCount) {
					case 0:
					case 2:
						break;
					default:
						throw new Exception("incorrect rightmost colons");
					}
					break;
				case Inner:
					// 中间的冒号
					switch (blankCount) {
					case 0:
					case 1:
						break;
					default:
						throw new Exception("incorrect inner colons");
					}
					break;
				}

				// 将每一段的值加到对应的long字段里
				switch (partIdx--) {
				case 7:
					value3 += value;
					break;
				case 6:
					value3 += value * (hasIpv4 ? 1 : 65536);
					break;
				case 5:
					value2 += value;
					break;
				case 4:
					value2 += value * 65536;
					break;
				case 3:
					value1 += value;
					break;
				case 2:
					value1 += value * 65536;
					break;
				case 1:
					value0 += value;
					break;
				case 0:
					value0 += value * 65536;
					break;
				default:
					throw new Exception("more than 8 parts");
				}
			}

			if (isBlank) {
				if (++doubleColonCount > 1)
					throw new Exception("more than 2 double-colon");
				if (partIdx < 0)
					throw new Exception("more than 8 parts");

				// 判断全是冒号和最左边冒号的情况是否合法
				switch (doubleColonPosition) {
				case RightMost:
					// 从右到左都是空的情况
					if (blankCount != 3)
						throw new Exception("incorrect standalone colons");
					break;
				case Inner:
					// 最左边有空的情况
					// 最左侧要么没有冒号，要么有两个冒号，其余都是错误的
					switch (blankCount) {
					case 0:
					case 2:
						break;
					default:
						throw new Exception("incorrect leftmost colons");
					}
					break;
				}
			} else if (partIdx >= 0) {
				throw new Exception("less than 8 parts");
			}

			IPv6Long iPv6Long = new IPv6Long();
			iPv6Long.data = new long[4];
			iPv6Long.data[0] = value0;
			iPv6Long.data[1] = value1;
			iPv6Long.data[2] = value2;
			iPv6Long.data[3] = value3;

			return iPv6Long;
		} catch (Exception e) {
			throw new Exception("invalid ipv6: " + ipv6 + ", " + e.getMessage(), e);
		}
	}

	private static long parseIpv4(String ipv4) throws Exception {
		String[] parts = StringUtils.splitPreserveAllTokens(ipv4, ".");
		if (parts.length != 4)
			throw new Exception("invalid ipv4");

		if (parts[0].length() > 1 && parts[0].startsWith("0"))
			throw new Exception("invalid ipv4");

		if (parts[1].length() > 1 && parts[1].startsWith("0"))
			throw new Exception("invalid ipv4");

		if (parts[2].length() > 1 && parts[2].startsWith("0"))
			throw new Exception("invalid ipv4");

		if (parts[3].length() > 1 && parts[3].startsWith("0"))
			throw new Exception("invalid ipv4");

		long p0 = Long.valueOf(parts[0]);
		long p1 = Long.valueOf(parts[1]);
		long p2 = Long.valueOf(parts[2]);
		long p3 = Long.valueOf(parts[3]);

		if ((p0 < 0 || p0 > 255) || (p1 < 0 || p1 > 255) || (p2 < 0 || p2 > 255) || (p3 < 0 || p3 > 255))
			throw new Exception("invalid ipv4");

		return p0 * 16777216L + p1 * 65536L + p2 * 256L + p3;
	}

	static enum DoubleColonPosition {
		Unknown, LeftMost, Inner, RightMost
	}

	private static DoubleColonPosition setPosition(DoubleColonPosition prevValue, DoubleColonPosition newValue) {
		return prevValue == DoubleColonPosition.Unknown ? newValue : prevValue;
	}

	private IPv6Long() {
	}

	@Override
	public int compareTo(IPv6Long o) {
		if (data[0] < o.data[0]) {
			return -1;
		} else if (data[0] > o.data[0]) {
			return 1;
		}

		if (data[1] < o.data[1]) {
			return -1;
		} else if (data[1] > o.data[1]) {
			return 1;
		}

		if (data[2] < o.data[2]) {
			return -1;
		} else if (data[2] > o.data[2]) {
			return 1;
		}

		if (data[3] < o.data[3]) {
			return -1;
		} else if (data[3] > o.data[3]) {
			return 1;
		}

		return 0;
	}

}
