package com.iflytek.traffic.area;

import cn.hutool.core.collection.CollUtil;
import com.iflytek.traffic.service.MeterService;
import com.iflytek.traffic.util.Util;
import com.iflytek.traffic.util.constant.Constants;
import com.ip2location.IP2Location;
import com.ip2location.IPResult;
import io.micrometer.core.instrument.Counter;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
public class IpDataService {
	@Autowired
	private MeterService meterService;

	private Counter adReachedIp2LocationCounter;
	private Counter adReachedIpv4Counter;

	private Counter adSearchedByIp2LocationCounter;
	private Counter adSearchedByIpv4Counter;

	private Counter ipCountryTableNotFoundCounter;

	@Autowired
	private JdbcTemplate jdbcTemplate;

	// 由于该变量会定时重新赋值，在一次使用会话中，建议保存该引用至临时变量
	private IpContext ipContext = new IpContext();

	IP2Location ip2Location = null;

	@PostConstruct
	public void setup() throws Exception {
		update();
		try {
			String binfile = "data/IP2LOCATION-LITE-DB11.IPV6.BIN";
			ip2Location = new IP2Location();
			ip2Location.Open(binfile, true);
			log.info("init IP2Location success.");
			adReachedIp2LocationCounter = meterService.count("ad.reached.ip2location.total");
			adSearchedByIp2LocationCounter = meterService.count("ad.searched.by.ip2location.num");
			ipCountryTableNotFoundCounter = meterService.count("ip.country.table.not.found.num");
			adReachedIpv4Counter = meterService.count("ad.reached.ipv4.total");
			adSearchedByIpv4Counter = meterService.count("ad.searched.by.ipv4.total");
		} catch (Exception e) {
			log.error("init IP2Location error.", e);
		}
	}

	@PreDestroy
	public void destory() {
		assert ip2Location != null;
		ip2Location.Close();
	}

	// 定时加载间隔单位：毫秒
	@Scheduled(fixedRateString = "${caa_ip_area.update_interval}", initialDelayString = "${caa_ip_area.update_interval}")
	public void schdule() {
		try {
			update();
			logIpContext();
		} catch (Exception e) {
			log.error("schedule: {}", e.getMessage(), e);
		}
	}

	private void logIpContext() {
		if (!log.isInfoEnabled())
			return;

		IpConfig ipConfig = ipContext.getIpConfig();
		StringBuilder sb = new StringBuilder();
		DateTimeFormatter ftf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

		sb.append("本地加载地域库有:");
		List<IpConfig.Unit> areaUnits = ipConfig.getAreaUnits();
		if (CollUtil.isNotEmpty(areaUnits)) {
			for (IpConfig.Unit unit : areaUnits) {
				sb.append(" <version=").append(unit.getVersion()).append(", starttime=").append(ftf.format(
						LocalDateTime.ofInstant(Instant.ofEpochMilli(unit.getStartTime()), ZoneId.systemDefault())))
						.append(">");
			}
		}
		log.info(sb.toString());

		sb.setLength(0);
		sb.append("本地加载黑名单库有: ");
		List<IpConfig.Unit> blacklistUnits = ipConfig.getBlacklistUnits();
		if (CollUtil.isNotEmpty(blacklistUnits)) {
			for (IpConfig.Unit unit : blacklistUnits) {
				sb.append(" <version=").append(unit.getVersion()).append(", starttime=").append(ftf.format(
						LocalDateTime.ofInstant(Instant.ofEpochMilli(unit.getStartTime()), ZoneId.systemDefault())))
						.append(">");
			}
		}
		log.info(sb.toString());

		sb.setLength(0);
		sb.append("本地加载地域库（ipv6）有: ");
		List<IpConfig.Unit> areaV6Units = ipConfig.getAreaV6Units();
		if (CollUtil.isNotEmpty(areaV6Units)) {
			for (IpConfig.Unit unit : areaV6Units) {
				sb.append(" <version=").append(unit.getVersion()).append(", starttime=").append(ftf.format(
						LocalDateTime.ofInstant(Instant.ofEpochMilli(unit.getStartTime()), ZoneId.systemDefault())))
						.append(">");
			}
		}
		log.info(sb.toString());
	}

	/*****************************************
	 * ip黑名单及地域等数据加载相关
	 **********************************************/

	/**
	 * 更新ip黑名单和ip地域数据的任务
	 *
	 * @throws Exception
	 */
	private void update() throws Exception {
		// 从数据库加载最新的配置
		IpConfig ipConfigNew = queryIpConfig();

		// 通过新配置和老配置计算出新增的配置
		IpConfig ipConfigDelta = calcIpConfigDelta(ipContext.getIpConfig(), ipConfigNew);
		if (ipConfigDelta == null) { // 新老配置没有变化，无需继续更新数据
			return;
		}

		// 将新增的配置对应的数据从数据库加载进来
		IpData ipDataDelta = queryIpData(ipConfigDelta);

		// 补全全新的IpData
		IpData ipDataNew = completeIpDataNew(ipContext.getIpData(), ipDataDelta, ipConfigNew);

		// 生成全新的IpContext，并赋值给ipContext变量
		ipContext = completeIpContextNew(ipConfigNew, ipDataNew);
	}

	/**
	 * 根据IpData构造IpArea和IpBlacklist的两个随startTime从小到大排序的列表，方便后续查询方便
	 *
	 * @param ipConfigNew
	 * @param ipDataNew
	 * @return 不为null
	 */
	private IpContext completeIpContextNew(IpConfig ipConfigNew, IpData ipDataNew) {
		Map<IpConfig.Unit, IpData.IpAreaData> ipAreaDataSet = ipDataNew.getIpAreaData();
		Map<IpConfig.Unit, IpData.IpBlacklistData> ipBlacklistDataSet = ipDataNew.getIpBlacklistData();
		Map<IpConfig.Unit, IpData.IpAreaV6Data> ipAreaV6DataSet = ipDataNew.getIpAreaV6Data();

		List<IpData.IpAreaData> ipAreaData = new ArrayList<>(ipAreaDataSet.values());
		List<IpData.IpBlacklistData> ipBlacklistData = new ArrayList<>(ipBlacklistDataSet.values());
		List<IpData.IpAreaV6Data> ipAreaV6Data = new ArrayList<>(ipAreaV6DataSet.values());

		ipAreaData.sort((o1, o2) -> Util.compareCode(o1.getConfig().getStartTime() - o2.getConfig().getStartTime()));
		ipBlacklistData
				.sort((o1, o2) -> Util.compareCode(o1.getConfig().getStartTime() - o2.getConfig().getStartTime()));
		ipAreaV6Data.sort((o1, o2) -> Util.compareCode(o1.getConfig().getStartTime() - o2.getConfig().getStartTime()));

		return IpContext.builder().ipConfig(ipConfigNew).ipData(ipDataNew).ipAreaData(ipAreaData)
				.ipBlacklistData(ipBlacklistData).ipAreaV6Data(ipAreaV6Data).build();
	}

	/**
	 * 在ipDataDelta的基础上，根据ipConfigNew中的配置从ipDataOld中选出对应的IpData；本次实现可能会修改ipDataDelta的数据!!!
	 *
	 * @param ipDataOld
	 * @param ipDataDelta
	 * @param ipConfigNew
	 * @return 不为null
	 */
	private IpData completeIpDataNew(IpData ipDataOld, IpData ipDataDelta, IpConfig ipConfigNew) {
		if (ipDataOld == null) {
			return ipDataDelta;
		}

		Map<IpConfig.Unit, IpData.IpAreaData> ipAreaDataOld = ipDataOld.getIpAreaData();
		Map<IpConfig.Unit, IpData.IpBlacklistData> ipBlacklistDataOld = ipDataOld.getIpBlacklistData();
		Map<IpConfig.Unit, IpData.IpAreaV6Data> ipAreaV6DataOld = ipDataOld.getIpAreaV6Data();

		Map<IpConfig.Unit, IpData.IpAreaData> ipAreaDataDelta = ipDataDelta.getIpAreaData();
		Map<IpConfig.Unit, IpData.IpBlacklistData> ipBlacklistDataDelta = ipDataDelta.getIpBlacklistData();
		Map<IpConfig.Unit, IpData.IpAreaV6Data> ipAreaV6DataDelta = ipDataDelta.getIpAreaV6Data();

		List<IpConfig.Unit> areaUnitsNew = ipConfigNew.getAreaUnits();
		List<IpConfig.Unit> blacklistUnitsNew = ipConfigNew.getBlacklistUnits();
		List<IpConfig.Unit> areaV6UnitsNew = ipConfigNew.getAreaV6Units();

		for (IpConfig.Unit unit : areaUnitsNew) {
			ipAreaDataDelta.computeIfAbsent(unit, ipAreaDataOld::get);
		}

		for (IpConfig.Unit unit : blacklistUnitsNew) {
			ipBlacklistDataDelta.computeIfAbsent(unit, ipBlacklistDataOld::get);
		}

		for (IpConfig.Unit unit : areaV6UnitsNew) {
			ipAreaV6DataDelta.computeIfAbsent(unit, ipAreaV6DataOld::get);
		}

		return ipDataDelta;
	}

	/**
	 * 根据配置，从数据库加载ip黑名单和ip地域等数据
	 *
	 * @param ipConfig
	 * @return 不为null
	 * @throws Exception
	 */
	private IpData queryIpData(IpConfig ipConfig) throws Exception {
		Map<IpConfig.Unit, IpData.IpAreaData> ipAreaData = new HashMap<>();
		Map<IpConfig.Unit, IpData.IpBlacklistData> ipBlacklistData = new HashMap<>();
		Map<IpConfig.Unit, IpData.IpAreaV6Data> ipAreaV6Data = new HashMap<>();

		List<IpConfig.Unit> areaUnits = ipConfig.getAreaUnits();
		for (IpConfig.Unit unit : areaUnits) {
			IpData.IpAreaData data = queryIpAreaData(unit);
			ipAreaData.put(unit, data);
		}

		List<IpConfig.Unit> blacklistUnits = ipConfig.getBlacklistUnits();
		for (IpConfig.Unit unit : blacklistUnits) {
			IpData.IpBlacklistData data = queryIpBlacklistData(unit);
			ipBlacklistData.put(unit, data);
		}

		List<IpConfig.Unit> areaV6Units = ipConfig.getAreaV6Units();
		for (IpConfig.Unit unit : areaV6Units) {
			IpData.IpAreaV6Data data = queryIpAreaV6Data(unit);
			ipAreaV6Data.put(unit, data);
		}

		return IpData.builder().ipAreaData(ipAreaData).ipBlacklistData(ipBlacklistData).ipAreaV6Data(ipAreaV6Data)
				.build();
	}

	private IpData.IpAreaV6Data queryIpAreaV6Data(IpConfig.Unit unit) {
		String version = unit.getVersion();
		String suffix = version.isEmpty() ? "" : "_" + version;
		String sqlProvince = "select id, name from caa_ip_area.province_v6" + suffix;
		String sqlCity = "select id, name, parent_id from caa_ip_area.city_v6" + suffix;
		String sqlIpArea = "select start_ip, end_ip, area_code from caa_ip_area.ip_area_v6" + suffix;

		String sqlIpAreaOverseas = "select start_ip, end_ip, area_code from caa_ip_area.ip_area_v6_overseas" + suffix;
		String sqlCountry = "select id, name from caa_ip_area.country_v6" + suffix;

		SqlRowSet rsIpArea = jdbcTemplate.queryForRowSet(sqlIpAreaOverseas);
		List<IpData.IpAreaV6Data.IpAreaItem> ipAreaItems = new ArrayList<>();
		while (rsIpArea.next()) {
			String startIp = rsIpArea.getString("start_ip");
			String endIp = rsIpArea.getString("end_ip");
			String areaCode = rsIpArea.getString("area_code");

			if (StringUtils.isBlank(areaCode)) {
				continue;
			}

			try {
				IPv6Long startIpLong = IPv6Long.fromString(startIp);
				IPv6Long endIpLong = IPv6Long.fromString(endIp);
				ipAreaItems.add(new IpData.IpAreaV6Data.IpAreaItem(startIpLong, endIpLong, areaCode));
			} catch (Exception e) {
				log.error("{}, startIp={}, endIp={}, areaCode={}", e.getMessage(), startIp, endIp, areaCode, e);
			}
		}

		SqlRowSet rsCountry = jdbcTemplate.queryForRowSet(sqlCountry);
		Map<String, IpData.IpAreaV6Data.Country> countries = new HashMap<>();
		while (rsCountry.next()) {
			String id = rsCountry.getString("id");
			String name = rsCountry.getString("name");

			if (StringUtils.isBlank(id) || StringUtils.isBlank(name)) {
				continue;
			}

			countries.put(id, new IpData.IpAreaV6Data.Country(id, name));
		}

		Map<String, IpData.IpAreaV6Data.Area> areaInfo = new HashMap<>();
		for (String countryId : countries.keySet()) {
			IpData.IpAreaV6Data.Country c = countries.get(countryId);
			areaInfo.put(countryId, new IpData.IpAreaV6Data.Area(Long.valueOf(c.id), c.name, null, null, null, null));
		}

//		SqlRowSet rsProvince = jdbcTemplate.queryForRowSet(sqlProvince);
//		Map<String, IpData.IpAreaV6Data.Province> provinces = new HashMap<>();
//		while (rsProvince.next()) {
//			String id = rsProvince.getString("id");
//			String name = rsProvince.getString("name");
//
//			if (StringUtils.isBlank(id) || StringUtils.isBlank(name)) {
//				continue;
//			}
//
//			provinces.put(id, new IpData.IpAreaV6Data.Province(id, name));
//		}
//
//		SqlRowSet rsCity = jdbcTemplate.queryForRowSet(sqlCity);
//		Map<String, IpData.IpAreaV6Data.City> cities = new HashMap<>();
//		while (rsCity.next()) {
//			String id = rsCity.getString("id");
//			String name = rsCity.getString("name");
//			String parentId = rsCity.getString("parent_id");
//
//			if (StringUtils.isBlank(id) || StringUtils.isBlank(name) || StringUtils.isBlank(parentId)) {
//				continue;
//			}
//
//			cities.put(id, new IpData.IpAreaV6Data.City(id, name, parentId));
//		}

//		Map<String, IpData.IpAreaV6Data.Area> areaInfo = new HashMap<>();
//		for (String provinceId : provinces.keySet()) {
//			IpData.IpAreaV6Data.Province p = provinces.get(provinceId);
//			areaInfo.put(provinceId, new IpData.IpAreaV6Data.Area(Long.valueOf(p.id), p.name, null, null)); // key为广协的省ID，value为讯飞的省ID和省名称
//		}
//
//		for (String cityId : cities.keySet()) {
//			IpData.IpAreaV6Data.City c = cities.get(cityId);
//			IpData.IpAreaV6Data.Province p = provinces.get(c.parentId);
//			if (p == null) {
//				areaInfo.put(cityId, new IpData.IpAreaV6Data.Area(null, null, Long.valueOf(c.id), c.name));
//			} else {
//				areaInfo.put(cityId, new IpData.IpAreaV6Data.Area(Long.valueOf(p.id), p.name,
//						Long.valueOf(c.id), c.name));
//			}
//		}
		Collections.sort(ipAreaItems, ((o1, o2) -> o1.startIp.compareTo(o2.startIp))); // 根据startIp从小到大排序

		return IpData.IpAreaV6Data.builder().config(unit).areaInfo(areaInfo).sortedIpAreaItems(ipAreaItems).build();
	}

	/**
	 * 根据单个配置，从数据库加载ip地域等数据
	 *
	 * @param unit
	 * @return 不为null
	 */
	private IpData.IpAreaData queryIpAreaData(IpConfig.Unit unit) {
		String version = unit.getVersion();
		String suffix = version.isEmpty() ? "" : "_" + version;
		String sqlProvince = "select id, name from caa_ip_area.province" + suffix;
		String sqlCity = "select id, name, parent_id from caa_ip_area.city" + suffix;
		String sqlIpArea = "select start_ip, end_ip, area_code from caa_ip_area.ip_area" + suffix;

		//海外
		String sqlCountry = "select id, name, code from caa_ip_area.country" + suffix;
		String sqlIpAreaOverseas = "select start_ip, end_ip, area_code from caa_ip_area.ip_area_overseas" + suffix;

		SqlRowSet rsIpArea = jdbcTemplate.queryForRowSet(sqlIpAreaOverseas);
		List<IpData.IpAreaData.IpAreaItem> ipAreaItems = new ArrayList<IpData.IpAreaData.IpAreaItem>();
		while (rsIpArea.next()) {
			String startIp = rsIpArea.getString("start_ip");
			String endIp = rsIpArea.getString("end_ip");
			String areaCode = rsIpArea.getString("area_code");

			if (StringUtils.isBlank(areaCode)) {
				continue;
			}

			try {
				long startIpLong = ip2long(startIp);
				long endIpLong = ip2long(endIp);
				ipAreaItems.add(new IpData.IpAreaData.IpAreaItem(startIpLong, endIpLong, areaCode));
			} catch (Exception e) {
				log.error("{}, startIp={}, endIp={}, areaCode={}", e.getMessage(), startIp, endIp, areaCode, e);
			}
		}

		SqlRowSet rsCountry = jdbcTemplate.queryForRowSet(sqlCountry);
		Map<String, IpData.IpAreaData.Country> countries = new HashMap<>();
		Map<String, IpData.IpAreaData.Country> countries4Code = new HashMap<>();
		while (rsCountry.next()) {
			String id = rsCountry.getString("id");
			String name = rsCountry.getString("name");
			String code = rsCountry.getString("code");

			if (StringUtils.isBlank(id) || StringUtils.isBlank(name)) {
				continue;
			}

			countries.put(id, new IpData.IpAreaData.Country(id, name, code));
			countries4Code.put(code, new IpData.IpAreaData.Country(id, name, code));
		}

		Map<String, IpData.IpAreaData.Area> areaInfo = new HashMap<String, IpData.IpAreaData.Area>();
		for (String countryId : countries.keySet()) {
			IpData.IpAreaData.Country c = countries.get(countryId);
			areaInfo.put(countryId, new IpData.IpAreaData.Area(Long.valueOf(c.id), c.name, c.code, null, null, null, null));
		}

		Map<String, IpData.IpAreaData.Area> areaInfo4Code = new HashMap<String, IpData.IpAreaData.Area>();
		for (String countryCode : countries4Code.keySet()) {
			IpData.IpAreaData.Country c = countries4Code.get(countryCode);
			areaInfo4Code.put(countryCode, new IpData.IpAreaData.Area(Long.valueOf(c.id), c.name, c.code, null, null, null, null));
		}

//		SqlRowSet rsProvince = jdbcTemplate.queryForRowSet(sqlProvince);
//		Map<String, IpData.IpAreaData.Province> provinces = new HashMap<String, IpData.IpAreaData.Province>();
//		while (rsProvince.next()) {
//			String id = rsProvince.getString("id");
//			String name = rsProvince.getString("name");
//
//			if (StringUtils.isBlank(id) || StringUtils.isBlank(name)) {
//				continue;
//			}
//
//			provinces.put(id, new IpData.IpAreaData.Province(id, name));
//		}
//
//		SqlRowSet rsCity = jdbcTemplate.queryForRowSet(sqlCity);
//		Map<String, IpData.IpAreaData.City> cities = new HashMap<String, IpData.IpAreaData.City>();
//		while (rsCity.next()) {
//			String id = rsCity.getString("id");
//			String name = rsCity.getString("name");
//			String parentId = rsCity.getString("parent_id");
//
//			if (StringUtils.isBlank(id) || StringUtils.isBlank(name) || StringUtils.isBlank(parentId)) {
//				continue;
//			}
//
//			cities.put(id, new IpData.IpAreaData.City(id, name, parentId));
//		}

//		Map<String, IpData.IpAreaData.Area> areaInfo = new HashMap<String, IpData.IpAreaData.Area>();
//		for (String provinceId : provinces.keySet()) {
//			IpData.IpAreaData.Province p = provinces.get(provinceId);
//			areaInfo.put(provinceId, new IpData.IpAreaData.Area(Long.valueOf(p.id), p.name, null, null)); // key为广协的省ID，value为讯飞的省ID和省名称
//		}
//
//		for (String cityId : cities.keySet()) {
//			IpData.IpAreaData.City c = cities.get(cityId);
//			IpData.IpAreaData.Province p = provinces.get(c.parentId);
//			if (p == null) {
//				areaInfo.put(cityId, new IpData.IpAreaData.Area(null, null, Long.valueOf(c.id), c.name));
//			} else {
//				areaInfo.put(cityId, new IpData.IpAreaData.Area(Long.valueOf(p.id), p.name,
//						Long.valueOf(c.id), c.name));
//			}
//		}
		Collections.sort(ipAreaItems, ((o1, o2) -> Util.compareCode(o1.startIp - o2.startIp))); // 根据startIp从小到大排序

		return IpData.IpAreaData.builder().config(unit).areaInfo(areaInfo).areaInfo4Code(areaInfo4Code).sortedIpAreaItems(ipAreaItems).build();
	}

	/**
	 * 根据单个配置，从数据库加载ip黑名单数据
	 *
	 * @param unit
	 * @return 不为null
	 * @throws Exception
	 */
	private IpData.IpBlacklistData queryIpBlacklistData(IpConfig.Unit unit) throws Exception {
		String version = unit.getVersion();
		String suffix = version.isEmpty() ? "" : "_" + version;
		String sql = "select ip from caa_ip_area.ip_blacklist" + suffix;

		Set<Long> ipBlacklist = new HashSet<>();
		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql);
		while (rs.next()) {
			String ip = rs.getString("ip");
			long ipLong = ip2long(ip);
			ipBlacklist.add(ipLong);
		}

		return IpData.IpBlacklistData.builder().config(unit).ipBlacklist(ipBlacklist).build();
	}

	/**
	 * ipv4 string -> long
	 *
	 * @param ip
	 * @return
	 * @throws Exception
	 */
	public static long ip2long(String ip) throws Exception {
		try {
			String[] ipSecs = ip.trim().split("\\.");
			if (ipSecs.length != 4)
				throw new Exception("not a valid ipv4");

			long first = Long.valueOf(ipSecs[0]);
			if (first < 0 || first > 255)
				throw new Exception("not a valid ipv4");

			long second = Long.valueOf(ipSecs[1]);
			if (second < 0 || second > 255)
				throw new Exception("not a valid ipv4");

			long third = Long.valueOf(ipSecs[2]);
			if (third < 0 || third > 255)
				throw new Exception("not a valid ipv4");

			long fourth = Long.valueOf(ipSecs[3]);
			if (fourth < 0 || fourth > 255)
				throw new Exception("not a valid ipv4");

			return first * 16777216 + second * 65536 + third * 256 + fourth;
		} catch (Exception e) {
			throw new Exception("ip<" + ip + ">," + e.getMessage(), e);
		}
	}

	public IpData.IpAreaData.Area getIpInfoByIp2Location(String ip, long ts) {
		if (ip2Location == null) {
			return null;
		}
		try {
			adReachedIp2LocationCounter.increment();
			IPResult rec = ip2Location.IPQuery(ip);
			if ("OK".equals(rec.getStatus())) {
				adSearchedByIp2LocationCounter.increment();
				String code = rec.getCountryShort();
				IpData.IpAreaData data = null;

				// 找到startTime小于等于ts，且离得最近的data
				for (IpData.IpAreaData d : ipContext.getIpAreaData()) {
					long startTime = d.getConfig().getStartTime();
					if (startTime <= ts) {
						data = d;
					} else {
						break;
					}
				}

				if (data == null) {
					log.error("cant't find IpAreaData for ts: " + ts);
					return null;
				}
				if (data.getAreaInfo4Code() == null || !data.getAreaInfo4Code().containsKey(code)) {
					ipCountryTableNotFoundCounter.increment();
					log.info("code:{} not found in caa_ip_area.country", code);
					return null;
				}
				IpData.IpAreaData.Area area = data.getAreaInfo4Code().get(code);
				if (area != null) {
					area.setIp2location(true);
					if (!Objects.equals(rec.getZipCode(), Constants.NOT_SUPPORTED)) {
						area.setZipCode(rec.getZipCode());
					}
					if (!Objects.equals(rec.getCity(), Constants.NOT_SUPPORTED)) {
						area.setCityName(rec.getCity());
					}
					if (!Objects.equals(rec.getRegion(), Constants.NOT_SUPPORTED)) {
						area.setRegion(rec.getRegion());
					}
					if (!Objects.equals(rec.getTimeZone(), Constants.NOT_SUPPORTED)) {
						area.setTimezone(rec.getTimeZone());
					}
					area.setLatitude(rec.getLatitude());
					area.setLongitude(rec.getLongitude());
				}
				return data.getAreaInfo4Code().get(code);

			} else {
				log.info(rec.getStatus());
			}
		} catch (Exception e) {
			log.error("IP2Location search ip information error. ", e);
		}
		return null;
	}

	/**
	 * 取出只存在new里的，不在old里的unit
	 *
	 * @param ipConfigOld
	 * @param ipConfigNew
	 * @return null-新老配置没有发生变化；否则就是有变化
	 */
	private IpConfig calcIpConfigDelta(IpConfig ipConfigOld, IpConfig ipConfigNew) {
		if (ipConfigOld == null) { // 第一次初始化
			return ipConfigNew;
		}

		List<IpConfig.Unit> areaUnitsOld = ipConfigOld.getAreaUnits();
		List<IpConfig.Unit> blacklistUnitsOld = ipConfigOld.getBlacklistUnits();
		List<IpConfig.Unit> areaV6UnitsOld = ipConfigOld.getAreaV6Units();

		List<IpConfig.Unit> areaUnitsNew = ipConfigNew.getAreaUnits();
		List<IpConfig.Unit> blacklistUnitsNew = ipConfigNew.getBlacklistUnits();
		List<IpConfig.Unit> areaV6UnitsNew = ipConfigNew.getAreaV6Units();

		List<IpConfig.Unit> areaUnitsDelta = new ArrayList<>();
		List<IpConfig.Unit> blacklistUnitsDelta = new ArrayList<>();
		List<IpConfig.Unit> areaV6UnitsDelta = new ArrayList<>();

		label: for (IpConfig.Unit unitNew : areaUnitsNew) {
			for (IpConfig.Unit unitOld : areaUnitsOld) {
				if (unitNew.equals(unitOld)) {
					continue label;
				}
			}

			areaUnitsDelta.add(unitNew);
		}

		label: for (IpConfig.Unit unitNew : blacklistUnitsNew) {
			for (IpConfig.Unit unitOld : blacklistUnitsOld) {
				if (unitNew.equals(unitOld)) {
					continue label;
				}
			}

			blacklistUnitsDelta.add(unitNew);
		}

		label: for (IpConfig.Unit unitNew : areaV6UnitsNew) {
			for (IpConfig.Unit unitOld : areaV6UnitsOld) {
				if (unitNew.equals(unitOld)) {
					continue label;
				}
			}

			areaV6UnitsDelta.add(unitNew);
		}

		if (areaUnitsDelta.isEmpty() && blacklistUnitsDelta.isEmpty() && areaV6UnitsDelta.isEmpty()
				&& areaUnitsOld.size() == areaUnitsNew.size() && blacklistUnitsOld.size() == blacklistUnitsNew.size()
				&& areaV6UnitsOld.size() == areaV6UnitsNew.size()) {
			// 新老配置一样，则返回null
			return null;
		} else {
			return IpConfig.builder().areaUnits(areaUnitsDelta).blacklistUnits(blacklistUnitsDelta)
					.areaV6Units(areaV6UnitsDelta).build();
		}
	}

	/**
	 * 从数据库查询配置
	 *
	 * @return 不为null
	 */
	private IpConfig queryIpConfig() {
		long now = System.currentTimeMillis();

		List<IpConfig.Unit> areaUnits = new ArrayList<>();
		List<IpConfig.Unit> blacklistUnits = new ArrayList<>();
		List<IpConfig.Unit> areaV6Units = new ArrayList<>();

		String sql = "select * from caa_ip_area.config";
		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql);
		while (rs.next()) {
			long startTime = rs.getTimestamp("start_time").getTime();
			if (startTime > now + 3600000) { // 为了节约内存，生效时间在一个小时开外的配置暂时不加载进内存
				continue;
			}
			String version = rs.getString("version").trim();
			int type = rs.getInt("type");

			switch (type) {
			case 0:
				areaUnits.add(new IpConfig.Unit(startTime, version, type));
				break;
			case 1:
				blacklistUnits.add(new IpConfig.Unit(startTime, version, type));
				break;
			case 2:
				areaV6Units.add(new IpConfig.Unit(startTime, version, type));
				break;
			default:
				log.error("unknown type: {}", type);
				break;
			}
		}

		// 按照生效时间从大到小排序
		areaUnits.sort((a, b) -> Util.compareCode(b.getStartTime() - a.getStartTime()));
		blacklistUnits.sort((a, b) -> Util.compareCode(b.getStartTime() - a.getStartTime()));
		areaV6Units.sort((a, b) -> Util.compareCode(b.getStartTime() - a.getStartTime()));

		// 生效时间大于当前时间的配置全保留，小于当前时间的配置保留一个最近的就可以了
		List<IpConfig.Unit> areaUnits2 = new ArrayList<>();
		List<IpConfig.Unit> blacklistUnits2 = new ArrayList<>();
		List<IpConfig.Unit> areaV6Units2 = new ArrayList<>();

		for (IpConfig.Unit unit : areaUnits) {
			areaUnits2.add(unit);
			if (unit.getStartTime() <= now) {
				break;
			}
		}

		for (IpConfig.Unit unit : blacklistUnits) {
			blacklistUnits2.add(unit);
			if (unit.getStartTime() <= now) {
				break;
			}
		}

		for (IpConfig.Unit unit : areaV6Units) {
			areaV6Units2.add(unit);
			if (unit.getStartTime() <= now) {
				break;
			}
		}

		return IpConfig.builder().areaUnits(areaUnits2).blacklistUnits(blacklistUnits2).areaV6Units(areaV6Units2)
				.build();
	}

	/*****************************************
	 * ip黑名单相关
	 ************************************************************/

	private Set<Integer> advId2CheckIpBlacklist = new HashSet<>();

	public boolean isIpBlacklistCheckNeed4AdvId(int advId) {
		return advId2CheckIpBlacklist.contains(advId) ? true : false;
	}

	public boolean isIpInBlacklist4Advertiser(String ip, int advId, long ts) {
		if (!advId2CheckIpBlacklist.contains(advId)) {
			return false;
		}

		if (StringUtils.isBlank(ip)) {
			return true;
		}

		try {
			return ipContext.isIpInBlacklist(ip, ts);
		} catch (Exception e) {
			log.error("isIpInBlacklist4Advertiser: {}", e.getMessage());
			return true;
		}
	}

	public void updateAdvertiser(String value) {
		Set<Integer> newAdvId2CheckIpBlacklist = new HashSet<>();

		try {
			if (StringUtils.isNotBlank(value)) {
				newAdvId2CheckIpBlacklist.addAll(Util.splitByComma4Integer(value));
			}
			advId2CheckIpBlacklist = newAdvId2CheckIpBlacklist;
		} catch (Exception e) {
			log.error("updateAdvertiser: {}", e.getMessage());
		}

	}

	/*****************************************
	 * ip地域相关
	 ************************************************************/

	public long getAreaCode4Ipv4(String ip, long ts) throws Exception {
		return ipContext.getAreaCode4Ipv4(ip, ts);
	}

	public long getAreaCode4Ipv6(String ip, long ts) throws Exception {
		return ipContext.getAreaCode4Ipv6(ip, ts);
	}

	public IpData.IpAreaData.Area getAreaIpV4(String ip, long ts) throws Exception {
		adReachedIpv4Counter.increment();
		IpData.IpAreaData.Area area = ipContext.getAreaIpV4(ip, ts);
		if (area != null) {
			adSearchedByIpv4Counter.increment();
		}
		return area;
	}

	public IpData.IpAreaV6Data.Area getAreaIpV6(String ip, long ts) throws Exception {
		return ipContext.getAreaIpV6(ip, ts);
	}

}
