package com.iflytek.traffic.area;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IpConfig {

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class Unit {
		private long startTime;
		private String version;
		private int type;
	}

	private List<Unit> areaUnits;
	private List<Unit> blacklistUnits;
	private List<Unit> areaV6Units;

}
