package com.iflytek.traffic.area;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IpContext {

	private IpConfig ipConfig;

	private IpData ipData;

	// 按照IpConfig.Unit.startTime从小到大排序
	private List<IpData.IpAreaData> ipAreaData;

	// 按照IpConfig.Unit.startTime从小到大排序
	private List<IpData.IpBlacklistData> ipBlacklistData;

	// 按照IpConfig.Unit.startTime从小到大排序
	private List<IpData.IpAreaV6Data> ipAreaV6Data;

	public long getAreaCode4Ipv4(String ip, long ts) throws Exception {
		IpData.IpAreaData data = null;

		// 找到startTime小于等于ts，且离得最近的data
		for (IpData.IpAreaData d : ipAreaData) {
			long startTime = d.getConfig().getStartTime();
			if (startTime <= ts) {
				data = d;
			} else {
				break;
			}
		}

		if (data == null) {
			throw new Exception("cant't find IpAreaData for ts: " + ts);
		}

		return data.getAreaCode(ip);
	}

	public IpData.IpAreaData.Area getAreaIpV4(String ip, long ts) throws Exception {
		IpData.IpAreaData data = null;

		// 找到startTime小于等于ts，且离得最近的data
		for (IpData.IpAreaData d : ipAreaData) {
			long startTime = d.getConfig().getStartTime();
			if (startTime <= ts) {
				data = d;
			} else {
				break;
			}
		}

		if (data == null) {
			throw new Exception("cant't find IpAreaData for ts: " + ts);
		}

		return data.getArea(ip);
	}

	public long getAreaCode4Ipv6(String ip, long ts) throws Exception {
		IpData.IpAreaV6Data data = null;

		// 找到startTime小于等于ts，且离得最近的data
		for (IpData.IpAreaV6Data d : ipAreaV6Data) {
			long startTime = d.getConfig().getStartTime();
			if (startTime <= ts) {
				data = d;
			} else {
				break;
			}
		}

		if (data == null) {
			throw new Exception("cant't find IpAreaV6Data for ts: " + ts);
		}

		return data.getAreaCode(ip);
	}

	public IpData.IpAreaV6Data.Area getAreaIpV6(String ip, long ts) throws Exception {
		IpData.IpAreaV6Data data = null;

		// 找到startTime小于等于ts，且离得最近的data
		for (IpData.IpAreaV6Data d : ipAreaV6Data) {
			long startTime = d.getConfig().getStartTime();
			if (startTime <= ts) {
				data = d;
			} else {
				break;
			}
		}

		if (data == null) {
			throw new Exception("cant't find IpAreaV6Data for ts: " + ts);
		}

		return data.getArea(ip);
	}

	public boolean isIpInBlacklist(String ip, long ts) throws Exception {
		IpData.IpBlacklistData data = null;

		// 找到startTime小于等于ts，且离得最近的data
		for (IpData.IpBlacklistData d : ipBlacklistData) {
			long startTime = d.getConfig().getStartTime();
			if (startTime <= ts) {
				data = d;
			} else {
				break;
			}
		}

		if (data == null) {
			throw new Exception("cant't find IpBlacklistData for ts: " + ts);
		}

		return data.isIpInBlacklist(ip);
	}

}
