package com.iflytek.traffic.area;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IpData {

	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class IpAreaData {
		@Data
		public static class Area {
			private Long countryId;
			private String countryName;

			private String countryCode;
			private Long provinceId;
			private String provinceName;
			private Long cityId;
			private String cityName;

			/* 以下为从ip2location中解析的字段 */
			private String zipCode;
			private String region;
			private float latitude;
			private float longitude;
			private String timezone;
			/* 是否为ip2location解析而来 */
			private boolean ip2location = false;

			public Area(Long countryId, String countryName, Long provinceId, String provinceName, Long cityId, String cityName) {
				this.countryId = countryId;
				this.countryName = countryName;
				this.provinceId = provinceId;
				this.provinceName = provinceName;
				this.cityId = cityId;
				this.cityName = cityName;
			}

			public Area(Long countryId, String countryName, String countryCode, Long provinceId, String provinceName, Long cityId, String cityName) {
				this.countryId = countryId;
				this.countryName = countryName;
				this.countryCode = countryCode;
				this.provinceId = provinceId;
				this.provinceName = provinceName;
				this.cityId = cityId;
				this.cityName = cityName;
			}

			public Long getCountryId() {
				return countryId;
			}

			public String getCountryName() {
				return countryName;
			}

			public String getProvinceName() {
				return provinceName;
			}

			public String getCityName() {
				return cityName;
			}

			public Long getProvinceId() {
				return provinceId;
			}

			public Long getCityId() {
				return cityId;
			}

			public String getCountryCode() {
				return countryCode;
			}

			public void setCountryCode(String countryCode) {
				this.countryCode = countryCode;
			}
		}

		static class IpAreaItem {
			public long startIp;
			public long endIp;
			public String areaId; // 广协地域编码

			public IpAreaItem(long startIp, long endIp, String areaId) {
				this.startIp = startIp;
				this.endIp = endIp;
				this.areaId = areaId;
			}
		}

		static class Country {
			public String id;
			public String name;
			public String code;

			public Country(String id, String name) {
				this.id = id;
				this.name = name;
			}

			public Country(String id, String name, String code) {
				this.id = id;
				this.name = name;
				this.code = code;
			}
		}

		static class Province {
			public String id;
			public String name;

			public Province(String id, String name) {
				this.id = id;
				this.name = name;
			}
		}

		static class City {
			public String id;
			public String name;
			public String parentId;

			public City(String id, String name, String parentId) {
				this.id = id;
				this.name = name;
				this.parentId = parentId;
			}
		}

		private IpConfig.Unit config;

		// 使用二分查找找到的地域编码为广协的地域编码
		private List<IpAreaItem> sortedIpAreaItems; // 以IpAreaItem.startIp进行排序后的映射表

		// key为广协的地域编码，value内存的是讯飞的地域编码。因为页面目前使用的是讯飞的地域编码，所以此处做了广协->讯飞的映射
		private Map<String, Area> areaInfo;

		// key: caa_ip_area.country的code列
		private Map<String, Area> areaInfo4Code;

		/**
		 * 优先返回市，再返回省
		 *
		 * @param ip ipv4，点分式
		 * @return 如果没查到或遇到异常，则返回0
		 */
		public long getAreaCode(String ip) throws Exception {
			long code = 0;

			do {
				Area area = getArea(ip);
				if (area == null) {
					break;
				}

				Long city = area.getCityId();
				if (city != null) {
					code = city;
					break;
				}

				Long province = area.getProvinceId();
				if (province != null) {
					code = province;
					break;
				}

				Long country = area.getCountryId();
				if (country != null) {
					code = country;
					break;
				}
			} while (false);

			return code;
		}

		public Area getArea(String ip) throws Exception {
			if (StringUtils.isBlank(ip)) {
				throw new Exception("ip is blank");
			}

			long ipLong = IpDataService.ip2long(ip.trim());

			IpAreaItem item = search(sortedIpAreaItems, 0, sortedIpAreaItems.size() - 1, ipLong);
			if (item == null) {
				return null;
			}

			return areaInfo.get(item.areaId);
		}

		private static IpAreaItem search(List<IpAreaItem> sortedList, int start, int end, long ip) {
			if (end < start)
				return null;
			int middle = (start + end) / 2;
			IpAreaItem currentItem = sortedList.get(middle);

			if (ip >= currentItem.startIp) {
				if (ip <= currentItem.endIp)
					return currentItem;
				else
					return search(sortedList, middle + 1, end, ip);
			} else {
				return search(sortedList, start, middle - 1, ip);
			}
		}
	}

	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class IpBlacklistData {
		private IpConfig.Unit config;

		private Set<Long> ipBlacklist;

		public boolean isIpInBlacklist(String ip) throws Exception {
			long ipLong = IpDataService.ip2long(ip);
			return ipBlacklist.contains(ipLong);
		}
	}

	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class IpAreaV6Data {
		public static class Area {
			private Long countryId;
			private String countryName;
			private Long provinceId;
			private String provinceName;
			private Long cityId;
			private String cityName;

			public Area(Long countryId, String countryName, Long provinceId, String provinceName, Long cityId, String cityName) {
				this.countryId = countryId;
				this.countryName = countryName;
				this.provinceId = provinceId;
				this.provinceName = provinceName;
				this.cityId = cityId;
				this.cityName = cityName;
			}

			public Long getCountryId() {
				return countryId;
			}

			public String getCountryName() {
				return countryName;
			}

			public String getProvinceName() {
				return provinceName;
			}

			public String getCityName() {
				return cityName;
			}

			public Long getProvinceId() {
				return provinceId;
			}

			public Long getCityId() {
				return cityId;
			}

		}

		static class IpAreaItem {
			public IPv6Long startIp;
			public IPv6Long endIp;
			public String areaId; // 广协地域编码

			public IpAreaItem(IPv6Long startIp, IPv6Long endIp, String areaId) {
				this.startIp = startIp;
				this.endIp = endIp;
				this.areaId = areaId;
			}
		}

		static class Country {
			public String id;
			public String name;

			public Country(String id, String name) {
				this.id = id;
				this.name = name;
			}
		}

		static class Province {
			public String id;
			public String name;

			public Province(String id, String name) {
				this.id = id;
				this.name = name;
			}
		}

		static class City {
			public String id;
			public String name;
			public String parentId;

			public City(String id, String name, String parentId) {
				this.id = id;
				this.name = name;
				this.parentId = parentId;
			}
		}

		private IpConfig.Unit config;

		// 使用二分查找找到的地域编码为广协的地域编码
		private List<IpAreaItem> sortedIpAreaItems; // 以IpAreaItem.startIp进行排序后的映射表

		// key为广协的地域编码，value内存的是讯飞的地域编码。因为页面目前使用的是讯飞的地域编码，所以此处做了广协->讯飞的映射
		private Map<String, Area> areaInfo;

		/**
		 * 优先返回市，再返回省
		 *
		 * @param ipv6
		 * @return 如果没查到或遇到异常，则返回0
		 */
		public long getAreaCode(String ipv6) throws Exception {
			long code = 0;

			do {
				Area area = getArea(ipv6);
				if (area == null) {
					break;
				}

				Long city = area.getCityId();
				if (city != null) {
					code = city;
					break;
				}

				Long province = area.getProvinceId();
				if (province != null) {
					code = province;
					break;
				}

				Long country = area.getCountryId();
				if (country != null) {
					code = country;
					break;
				}
			} while (false);

			return code;
		}

		public Area getArea(String ipv6) throws Exception {
			if (StringUtils.isBlank(ipv6)) {
				throw new Exception("ipv6 is blank");
			}

			IPv6Long ipLong = IPv6Long.fromString(ipv6);

			IpAreaItem item = search(sortedIpAreaItems, 0, sortedIpAreaItems.size() - 1, ipLong);
			if (item == null) {
				return null;
			}

			return areaInfo.get(item.areaId);
		}

		private static IpAreaItem search(List<IpAreaItem> sortedList, int start, int end, IPv6Long ip) {
			if (end < start)
				return null;
			int middle = (start + end) / 2;
			IpAreaItem currentItem = sortedList.get(middle);

			if (ip.compareTo(currentItem.startIp) >= 0) {
				if (ip.compareTo(currentItem.endIp) <= 0)
					return currentItem;
				else
					return search(sortedList, middle + 1, end, ip);
			} else {
				return search(sortedList, start, middle - 1, ip);
			}
		}
	}

	private Map<IpConfig.Unit, IpAreaData> ipAreaData;

	private Map<IpConfig.Unit, IpBlacklistData> ipBlacklistData;

	private Map<IpConfig.Unit, IpAreaV6Data> ipAreaV6Data;

}
