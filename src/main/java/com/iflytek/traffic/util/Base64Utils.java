package com.iflytek.traffic.util;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 */
public abstract class Base64Utils {
    private Base64Utils() {
    }

    public static byte[] encode(byte[] data) {
        if (data == null) {
            return null;
        }
        return Base64.getEncoder().encode(data);
    }

    public static byte[] encode(String data) {
        if (data == null) {
            return null;
        }
        return encode(data.getBytes(StandardCharsets.UTF_8));
    }

    public static String encodeAsString(byte[] data) {
        if (data == null) {
            return null;
        }
        return Base64.getEncoder().encodeToString(data);
    }

    public static String encodeAsString(String data) {
        if (data == null) {
            return null;
        }
        return encodeAsString(data.getBytes(StandardCharsets.UTF_8));
    }
}
