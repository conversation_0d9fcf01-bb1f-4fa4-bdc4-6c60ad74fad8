package com.iflytek.traffic.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;


@Component
@Slf4j
public class HostUtil {
    private String HOST_NAME;

    private String IP_PORT;

    @Value("${server.port}")
    public void setHostName(int port) {
        try {
            InetAddress netAddress = InetAddress.getLocalHost();
            String hostname = netAddress.getHostName();
            hostname = hostname.replaceAll("[\\.|_]", "-");
            HOST_NAME = hostname + "_" + port;
            System.out.println("HOST_NAME:" + HOST_NAME);
            System.out.println("get env hostname success!");
            System.out.println(netAddress.getHostAddress());
        } catch (UnknownHostException e) {
            System.out.println("unknown host!");
            e.printStackTrace();
            log.error("unknow host! msg:{}",e.getMessage(),e);
        }

        try {
            String ip = System.getenv("NODE_IP");
            if (StringUtils.isNotEmpty(ip)) {
                IP_PORT = ip + "_" + port;
                IP_PORT = IP_PORT.replaceAll("\\.", "-");
                System.out.println("IP_PORT:" + IP_PORT);
                System.out.println("get env IP_PORT success!");
            } else {
                System.out.println("get env IP_PORT empty!");
            }
        } catch (Exception e){
            System.out.println("get env IP_PORT error!");
        }
    }

    public String getHostName() {
        return HOST_NAME;
    }

    public String getIpPort() {
        if (StringUtils.isNotBlank(IP_PORT)) {
            return IP_PORT;
        }
        return HOST_NAME;
    }
}
