package com.iflytek.traffic.util;

import cn.hutool.core.codec.Base64Encoder;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.hash.MurmurHash;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import io.lettuce.core.RedisURI;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.zip.CRC32;

@Slf4j
public class Util {
	private static final Logger LOG = LoggerFactory.getLogger(Util.class);

   public static List<RedisURI> nodes2Uris(String nodes, Duration timeout,String password) {
        List<RedisURI> uris = new ArrayList<>();

        for (String node : nodes.split(",")) {
            String[] ipPort = node.trim().split(":");
            String ip = ipPort[0].trim();
            int port = Integer.valueOf(ipPort[1].trim());
            RedisURI uri = new RedisURI(ip, port, timeout);
            uri.setPassword(password);
            uris.add(uri);
        }

        return uris;
    }
	
	public static int compareCode(long code) {
		if (code < 0) {
			return -1;
		} else if (code > 0) {
			return 1;
		} else {
			return 0;
		}
	}

	public static List<String> splitByCommaAndRemoveBlankElements(String s) {
		if (StrUtil.isBlank(s)) {
			return null;
		}

		List<String> results = new ArrayList<String>();
		String[] elements = s.split(",");
		for (int i = 0; i < elements.length; i++) {
			String element = elements[i].trim();
			if (StrUtil.isNotBlank(element)) {
				results.add(element);
			}
		}

		return results;
	}

	public static List<Integer> splitByComma4Integer(String s) {
		if (StrUtil.isBlank(s)) {
			return null;
		}

		List<Integer> results = new ArrayList<>();
		String[] elements = s.split(",");
		for (int i = 0; i < elements.length; i++) {
			String element = elements[i].trim();
			if (StrUtil.isNotBlank(element)) {
				results.add(Integer.valueOf(element));
			}
		}

		return results;
	}

	public static String getMD5(String str) {
		try {
			// 生成一个MD5加密计算摘要
			MessageDigest md = MessageDigest.getInstance("MD5");
			// 计算md5函数
			md.update(str.getBytes());
			// digest()最后确定返回md5 hash值，返回值为8为字符串。因为md5 hash值是16位的hex值，实际上就是8位的字符
			// BigInteger函数则将8位的字符串转换成16位hex值，用字符串来表示；得到字符串形式的hash值

			return HexFormat.of().formatHex(md.digest());
		} catch (Exception e) {
			LOG.debug("MD5加密出现错误");
			return "";
		}
	}

	/***
	 * 利用Apache的工具类实现SHA-256加密
	 * @param str 加密后的报文
	 * @return
	 */
	public static String getSHA256(String str){
		MessageDigest messageDigest;
		String encdeStr = "";
		try {
			messageDigest = MessageDigest.getInstance("SHA-256");
			byte[] hash = messageDigest.digest(str.getBytes("UTF-8"));
			encdeStr = HexFormat.of().formatHex(hash);
		} catch (NoSuchAlgorithmException e) {
			LOG.debug("SHA256加密出现错误(NoSuchAlgorithmException)");
		} catch (UnsupportedEncodingException e) {
			LOG.debug("SHA256加密出现错误(UnsupportedEncodingException)");
		}
		return encdeStr;
	}

	public static long GetCrc32(String key) {
		CRC32 crc = new CRC32();
		crc.update(key.getBytes());
		return crc.getValue();
	}

	public static int GetCurrentMinute() {
		try {
			Date now = new Date();
			SimpleDateFormat MIN_FORMAT = new SimpleDateFormat("mm", Locale.CHINA);
			return Integer.parseInt(MIN_FORMAT.format(now));
		} catch (Exception e) {
			return 0;
		}
	}

	public static String GetCurrentDayStr() {
		try {
			Date now = new Date();
			SimpleDateFormat DAY_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
			return DAY_FORMAT.format(now);
		} catch (Exception e) {
			return null;
		}
	}

	public static String GetCurrentHourStr() {
		try {
			Date now = new Date();
			SimpleDateFormat DAY_FORMAT = new SimpleDateFormat("HH");
			return DAY_FORMAT.format(now);
		} catch (Exception e) {
			return null;
		}
	}

	public static String GetMonthPostfix() {
		try {
			Date now = new Date();
			SimpleDateFormat MONTH_POSTFIX_FORMAT = new SimpleDateFormat("MM_dd");
			return MONTH_POSTFIX_FORMAT.format(now);
		} catch (Exception e) {
			return null;
		}
	}

	public static long GenHourBegin(TimeZone timeZone) {
		try {
			SimpleDateFormat HOUR_BEGIN_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:00:00");
			HOUR_BEGIN_FORMAT.setTimeZone(java.util.TimeZone.getTimeZone(timeZone.getGmt()));
			Date now = new Date();
			String hour_begin = HOUR_BEGIN_FORMAT.format(now);
			return HOUR_BEGIN_FORMAT.parse(hour_begin).getTime() / 1000;
		} catch (Exception e) {
			return 0;
		}
	}

	public static long GenDayBegin() {
		try {
			SimpleDateFormat DAY_BEGIN_FORMAT = new SimpleDateFormat("yyyy-MM-dd 00:00:00", Locale.CHINA);
			Date now = new Date();
			String day_begin = DAY_BEGIN_FORMAT.format(now);
			return DAY_BEGIN_FORMAT.parse(day_begin).getTime() / 1000;
		} catch (Exception e) {
			log.error("{}", e.getMessage(), e);
			return 0;
		}
	}

	public static long GenDayBegin(TimeZone timeZone) {
		try {
			SimpleDateFormat DAY_BEGIN_FORMAT = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
			DAY_BEGIN_FORMAT.setTimeZone(java.util.TimeZone.getTimeZone(timeZone.getGmt()));
			// Date now = new Date();
			Date now = new Date();
			String day_begin = DAY_BEGIN_FORMAT.format(now);
			return DAY_BEGIN_FORMAT.parse(day_begin).getTime() / 1000;
		} catch (Exception e) {
			log.error("{}", e.getMessage(), e);
			return 0;
		}
	}

	public static long GenMonthBegin(TimeZone timeZone) {
		try {
			SimpleDateFormat MONTH_BEGIN_FORMAT = new SimpleDateFormat("yyyy-MM-00 00:00:00");
			// Date now = new Date();
			MONTH_BEGIN_FORMAT.setTimeZone(java.util.TimeZone.getTimeZone(timeZone.getGmt()));
			Date now = new Date();
			String day_begin = MONTH_BEGIN_FORMAT.format(now);
			return MONTH_BEGIN_FORMAT.parse(day_begin).getTime() / 1000;
		} catch (Exception e) {
			log.error("{}", e.getMessage(), e);
			return 0;
		}
	}

	public static long GenWeekBegin(TimeZone timeZone) {
		try {
			SimpleDateFormat DAY_BEGIN_FORMAT = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
			DAY_BEGIN_FORMAT.setTimeZone(java.util.TimeZone.getTimeZone(timeZone.getGmt()));
			// Date now = new Date();
			Date now = new Date();
			String day_begin = DAY_BEGIN_FORMAT.format(now);
			Calendar cal = Calendar.getInstance();
			cal.setTimeZone(java.util.TimeZone.getTimeZone(timeZone.getGmt()));
			cal.setTime(now);

			// 把自然周从周日到周六改为周一到周日
			// int week_pos = cal.get(Calendar.DAY_OF_WEEK) - 1;
			int day_of_week = cal.get(Calendar.DAY_OF_WEEK);
			int week_pos = day_of_week >= 2 ? day_of_week - 2 : 6;

			return DAY_BEGIN_FORMAT.parse(day_begin).getTime() / 1000 - week_pos * 86400;
		} catch (Exception e) {
			log.error("{}", e.getMessage(), e);
			return 0;
		}
	}

	public static long DJBHash(String key) {
		long hash1 = 5381L;
		for (int i = 0; i < key.length(); i++) {
			hash1 = (hash1 << 5) + hash1 + (long) key.charAt(i);
		}
		hash1 = hash1 & 0xffffffL;
		long hash2 = 0;
		for (int i = 0; i < key.length(); i++) {
			hash2 = key.charAt(i) + (hash2 << 6) + (hash2 << 16) - hash2;
		}
		hash2 = hash2 & 0xffffff;
		return hash2 << 24 | hash1;
	}

	public static long GenDNFByStr(String prefix, String key) {
		if (prefix == null || prefix.length() < 2) {
			return 0L;
		}
		if (key == null) {
			return 0L;
		}
		long res = 0;
		res = res | ((long) prefix.charAt(0) << 56L);
		res = res | ((long) prefix.charAt(1) << 48L);
		res = res | (DJBHash(key) & 0xffffffffffffL);
		return res;
	}

	public static long GenDNFByInt(String prefix, long key) {
		if (prefix == null || prefix.length() < 2) {
			return 0L;
		}
		long res = 0;
		res = res | ((long) prefix.charAt(0) << 56L);
		res = res | ((long) prefix.charAt(1) << 48L);
		res = res | key;
		return res;
	}

	public static int parseInt(String str) {
		if (str == null) {
			return 0;
		}
		return Integer.parseInt(str);
	}

	public static long parseLong(String str) {
		if (str == null) {
			return 0L;
		}
		return Long.parseLong(str);
	}

	public static float parseFloat(String str) {
		if (str == null) {
			return 0;
		}
		return Float.parseFloat(str);
	}

	public static double parseDouble(String str) {
		if (str == null) {
			return 0L;
		}
		return Double.parseDouble(str);
	}

	public static String toTrim(String str) {
		if (null == str) {
			return null;
		}
		return str.trim();
	}

	public static String toUpper(String a) {
		if (a == null) {
			return null;
		}
		return a.toUpperCase();
	}

	public static boolean noEmpty(String str) {
		if (null != str && !str.isEmpty())
			return true;
		return false;
	}

	public static String genStackInfo(Exception e) {
		PrintWriter pw = null;
		StringWriter sw = null;
		try {
			sw = new StringWriter();
			pw = new PrintWriter(sw);
			e.printStackTrace(pw);
			pw.flush();
			sw.flush();
		} finally {
			if (sw != null) {
				try {
					sw.close();
				} catch (Exception e1) {
					log.error("{}", e.getMessage(), e);
				}
			}
			if (pw != null) {
				pw.close();
			}
		}
		return sw.toString();
	}

	
	public static String genThrowableStackInfo(Throwable throwable) {
        PrintWriter pw = null;
        StringWriter sw = null;
        try {
            sw = new StringWriter();
            pw = new PrintWriter(sw);
            throwable.printStackTrace(pw);
            pw.flush();
            sw.flush();
        } finally {
            if (sw != null) {
                try {
                    sw.close();
                } catch (Exception e1) {
                    log.error("{}", throwable.getMessage(), throwable);
                }
            }
            if (pw != null) {
                pw.close();
            }
        }
        return sw.toString();
    }
	
	public static String getAllTraceInfo(Exception e) {
		if (e == null) {
			return null;
		}

		List<List> message = new ArrayList<List>();

		Throwable t = e;
		while (t != null) {
			List<String> m = new ArrayList<String>();
			m.add(t.getMessage());

			StackTraceElement[] traces = t.getStackTrace();
			if (traces != null) {
				for (StackTraceElement ele : traces) {
					m.add(ele.toString());
				}
			}

			message.add(m);
			t = t.getCause();
		}

		return JSON.toJSON(message).toString();
	}

	public static Long GenDNFHead(String prefix) {
		if (prefix == null || prefix.length() < 2) {
			return 0L;
		}

		long res = 0;
		res = res | ((long) prefix.charAt(0) << 56L);
		res = res | ((long) prefix.charAt(1) << 48L);
		res = res >>> 48;

		return res;
	}

	public static Set<Integer> parseCommaSeparatedInteger(String line) {
		if (line == null)
			return null;

		Set<Integer> values = new HashSet<Integer>();

		String[] nodes = line.trim().split(",");
		for (String node : nodes) {
			String value = node.trim();
			if ("".equals(value))
				continue;
			Integer i = Integer.valueOf(value);
			values.add(i);
		}

		return values;
	}

	// 时长转String 格式：HH:MM:SS
	public static String secToTime(int time) {
		if (time < 0)
			return "00:00:00";
		Integer hour = time / 3600;
		Integer minute = time / 60;
		Integer second = time % 60;
		String hourString;
		String minuteString;
		String secondString;

		if (hour < 10) {
			hourString = "0" + hour;
		} else {
			hourString = Integer.toString(hour);
		}
		if (minute < 10) {
			minuteString = "0" + minute;
		} else {
			minuteString = Integer.toString(minute);
		}
		if (second < 10) {
			secondString = "0" + second;
		} else {
			secondString = Integer.toString(second);
		}
		return (hourString + ":" + minuteString + ":" + secondString);
	}

	public static class A implements Comparable<A> {
		public int price;
		public int weight;
		public String name;

		A(int price, int weight, String b) {
			this.price = price;
			this.weight = weight;
			this.name = b;
		}

		@Override
		public int compareTo(A o) {
			Random random = new Random();
			float ran = random.nextFloat();
			if ((float) (this.weight) / (float) (this.weight + o.weight) > ran) {
				return 1;
			} else {
				return -1;
			}
		}

		public String toString() {
			return this.name;
		}
	}

	private static final DateTimeFormatter dtf4Log = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH");

	public static String timestamp4Log(long t) {
		return DateUtil.format(DateTime.of(t), dtf4Log);
	}

	public static void main(String[] args) {
		System.out.println(Util.GenDNFHead("ex"));
	}

	public static String encrypt64(String plaintext, String key, boolean urlSafe) {
		try {
			byte[] encrypted = encrypt(plaintext.getBytes("UTF-8"), key.getBytes("UTF-8"));
			return urlSafe ? Base64Encoder.encodeUrlSafe(encrypted) : Base64Encoder.encode(encrypted);
		} catch (Exception e) {
			LOG.error(e.getMessage());
			return null;
		}
	}

	private static byte[] encrypt(byte[] plainBytes, byte[] keyBytes) throws Exception {
		Key key = new SecretKeySpec(keyBytes, "AES");
		Cipher chiper = Cipher.getInstance("AES/ECB/PKCS5Padding");
		chiper.init(Cipher.ENCRYPT_MODE, key);
		byte[] encrypted = chiper.doFinal(plainBytes);
		return encrypted;
	}
	
   public static long getLastSecondInToday() {
        LocalDateTime end = LocalDateTime.now().plusHours(24);
        String endDayBegin = end.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
        long endTtl = LocalDateTime.parse(endDayBegin, java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toEpochSecond(ZoneOffset.ofHours(8));
        return endTtl - System.currentTimeMillis()/1000;
    }

	public static Long murmurHash64(String data, Integer seed) {
		if (StrUtil.isBlank(data)) {
			return 0L;
		}
		int length = data.length();
		try {
			return MurmurHash.hash64(data.getBytes(StandardCharsets.UTF_8), length, seed) & 0x7fffffffffffffffL;
		} catch (Exception e) {
			log.error("cal hash error: ", e);
			return 0L;
		}
	}

	/**
	 * 计算两个整数的最大公约数。
	 *
	 * @param a 第一个整数。
	 * @param b 第二个整数。
	 * @return 最大公约数。
	 */
	public static int gcd(int a, int b) {
		if (b == 0) return a;
		return gcd(b, a % b);
	}

}
