package com.iflytek.traffic.util.qps;

import cn.hutool.core.util.StrUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.iflytek.traffic.util.SpringContextHelper;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class DimensionalQpsMonitorCache {

    // 使用Caffeine缓存维度组合计数器
    private final Cache<String, QpsMonitor> qpsMonitorCache = Caffeine.newBuilder()
//            .maximumSize(10_000) // 限制维度组合数量
            .expireAfterAccess(5, TimeUnit.MINUTES) // 5分钟后过期
            .build();


    public void recordRequest(String key) {
        String configStr = SpringContextHelper.getProperty("qps.monitor.window.size");
        int windowSize = StrUtil.isNotBlank(configStr) && StrUtil.isNumeric(configStr) ? Integer.parseInt(configStr) : 100; // 默认窗口大小为100
        QpsMonitor qpsMonitor = qpsMonitorCache.get(key, k -> new QpsMonitor(windowSize));
        qpsMonitor.record();
    }

    // 获取指定维度的QPS
    public double getQps(String key) {
        QpsMonitor qpsMonitor = qpsMonitorCache.getIfPresent(key);
        return qpsMonitor != null ? qpsMonitor.getQps() : 0;
    }

    public Map<String, Double> getAllQps() {
        Map<String, Double> allQps = new HashMap<>();
        qpsMonitorCache.asMap().forEach((key, monitor) -> {
            allQps.put(key, monitor.getQps());
        });
        return allQps;
    }
}
