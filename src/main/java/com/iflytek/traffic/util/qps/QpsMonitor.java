package com.iflytek.traffic.util.qps;

import com.codahale.metrics.Reservoir;
import com.codahale.metrics.SlidingTimeWindowReservoir;
import com.codahale.metrics.Snapshot;
import com.iflytek.traffic.service.NacosEventListener;
import com.iflytek.traffic.util.SpringContextHelper;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

/**
 * 基于 Dropwizard Metrics的SlidingTimeWindowReservoir滑动窗口QPS统计
 */
@Slf4j
public class QpsMonitor {
    // 计时器
    private final Reservoir reservoir;
    // 时间窗口大小和单位
    private final long windowSize;
    private final TimeUnit timeUnit = TimeUnit.SECONDS;

    /**
     * 创建QPS监控器
     *
     * @param windowSize 时间窗口大小，单位为秒
     */
    public QpsMonitor(long windowSize) {
        this.windowSize = windowSize;
        this.reservoir = new SlidingTimeWindowReservoir(windowSize, timeUnit);
    }

    public void record() {
        reservoir.update(System.nanoTime());
    }

    /**
     * 获取当前 QPS
     */
    public double getQps() {
        Snapshot snapshot = reservoir.getSnapshot();
        long eventCount = snapshot.size();

        // 计算实际时间跨度（纳秒）
        long actualWindow = calculateActualWindow(snapshot);

        // 转换为秒
        double seconds = TimeUnit.NANOSECONDS.toSeconds(actualWindow);

        // 防止除零错误
        double  qps = seconds > 0 ? eventCount / seconds : 0.0;
        NacosEventListener nacos = SpringContextHelper.getBean(NacosEventListener.class);
        if (nacos != null) {
            int serviceCount = nacos.getServiceCount();
            if (serviceCount > 0) {
                qps *= serviceCount;
            }
        } else {
            log.warn("NacosEventListener bean not found, using default service count of 1.");
        }
        return qps;
    }

    // 计算实际时间跨度
    private long calculateActualWindow(Snapshot snapshot) {
        if (snapshot.size() == 0) {
            return timeUnit.toNanos(windowSize);
        }

        // 获取时间窗口配置
        long configuredWindow = timeUnit.toNanos(windowSize);

        if (snapshot.size() == 1) {
            return Math.min(System.nanoTime() - snapshot.getValues()[0], configuredWindow);
        }
        // 获取实际最小和最大值
        long min = Long.MAX_VALUE;
        long max = Long.MIN_VALUE;
        for (long value : snapshot.getValues()) {
            if (value < min) min = value;
            if (value > max) max = value;
        }

        // 实际时间跨度不超过配置的时间窗口
        return Math.min(max - min, configuredWindow);
    }
}
