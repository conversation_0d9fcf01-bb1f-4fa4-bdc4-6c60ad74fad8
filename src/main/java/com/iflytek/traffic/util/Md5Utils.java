package com.iflytek.traffic.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 */
public abstract class Md5Utils {
    private static final char[] HEX_CHARS = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    private Md5Utils() {
    }

    public static byte[] digest(String data) {
        return digest(data.getBytes(StandardCharsets.UTF_8));
    }

    public static byte[] digest(byte[] data) {
        return getDigest("MD5").digest(data);
    }

    public static String digestAsHex(String data) {
        return new String(encodeHex(digest(data)));
    }

    public static String digestAsHex(byte[] data) {
        return new String(encodeHex(digest(data)));
    }


    public static MessageDigest getDigest(String algorithm) {
        try {
            return MessageDigest.getInstance(algorithm);
        } catch (NoSuchAlgorithmException ex) {
            throw new IllegalStateException("Could not find MessageDigest with algorithm \"" + algorithm + "\"", ex);
        }
    }

    private static char[] encodeHex(byte[] bytes) {
        char[] chars = new char[32];

        for (int i = 0; i < chars.length; i += 2) {
            byte b = bytes[i / 2];
            chars[i] = HEX_CHARS[b >>> 4 & 15];
            chars[i + 1] = HEX_CHARS[b & 15];
        }

        return chars;
    }


}
