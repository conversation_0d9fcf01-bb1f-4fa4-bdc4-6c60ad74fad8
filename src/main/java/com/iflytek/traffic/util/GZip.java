package com.iflytek.traffic.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

public class GZip {
  private static final Logger log = LoggerFactory.getLogger(GZip.class);

  public static byte[] compress(byte[] uncompressed) {
    return compress(uncompressed, null);
  }

  public static byte[] compress(byte[] uncompressed, ByteArrayOutputStream out) {
    if (uncompressed == null || uncompressed.length == 0) return null;
    try {
      if (out == null)
        out = new ByteArrayOutputStream();
      else
        out.reset();
      GZIPOutputStream gzip = new GZIPOutputStream(out);
      gzip.write(uncompressed);
      gzip.close();
      return out.toByteArray();
    } catch (Exception e) {
      log.warn(e.getMessage());
      return null;
    }
  }

  public static byte[] uncompress(byte[] compressed) {
    if (compressed == null || compressed.length == 0) return null;
    try {
      ByteArrayOutputStream out = new ByteArrayOutputStream();
      ByteArrayInputStream in = new ByteArrayInputStream(compressed);
      GZIPInputStream gunzip = new GZIPInputStream(in);
      byte[] buffer = new byte[256];
      int n;
      while ((n = gunzip.read(buffer)) >= 0) {
        out.write(buffer, 0, n);
      }
      return out.toByteArray();
    } catch (Exception e) {
      log.warn(e.getMessage());
      return null;
    }
  }

  public static void main(String[] args) {
    String plainStr =
        "{\"id\":\"eb59e6dc-8045-1540-4d50-de04lceb6ef1\",\"imp\":[{\"id\":\"eb59e6dc-8045-1540-4d50-de04lceb6ef1\",\"banner\":{\"id\":\"eb59e6dc-8045-1540-4d50-de04lceb6ef1\",\"w\":320,\"h\":50,\"battr\":[1,2,3,4,5,6,7,8,9,10,12,13,14,15,16],\"mimes\":[\"image/jpeg\",\"image/gif\",\"image/png\"],\"topframe\":0,\"api\":[3]},\"displaymanager\":\"inmobi_sdk\",\"instl\":\"0\",\"displaymanagerver\":\"i451\",\"bidfloor\":2,\"bidfloorcur\":\"CNY\",\"secure\":0,\"ext\":{\"dpl\":false}}],\"app\":{\"id\":\"test_app_id_1b\",\"name\":\"New 2048 plus--6 kinds of game modes\",\"cat\":[\"IAB10-2\",\"IAB1\",\"IAB19-29\"],\"bundle\":\"883891897\",\"privacypolicy\":0,\"paid\":0,\"storeurl\":\"https://itunes.apple.com/cn/app/hai-zei-wang-de-bao-cang/id883891897\",\"ext\":{\"fs\":\"0\",\"store\":{\"rating\":\"4+\",\"cat\":\"Games\",\"seccat\":[\"Games\",\"Entertainment\",\"Card\",\"Word\"]}}},\"device\":{\"dnt\":0,\"ua\":\"Mozilla/5.0 (iPhone; CPU iPhone OS 8_1_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Mobile/12B466\",\"ip\":\"*************\",\"geo\":{\"lat\":30.236,\"lon\":120.159,\"country\":\"CN\",\"city\":\"HANGZHOU\",\"type\":1},\"dpidsha1\":\"SHA1_HASHED_DEVICEID\",\"dpidmd5\":\"MD5_HASHED_DEVICEID\",\"carrier\":\"China Mobile\",\"language\":\"zh\",\"make\":\"Apple\",\"model\":\"iPhone 7s\",\"os\":\"iOS\",\"osv\":\"10.3\",\"ifa\":\"C793E239-1B17-49EB-833E-222307EA25A7\",\"pxratio\":2,\"connectiontype\":0,\"devicetype\":4,\"ext\":{\"idfamd5\":\"MD5_HASHED_IDFA\",\"idfa\":\"C793E239-1B17-49EB-833E-222307EA25A7\",\"idfasha1\":\"MD5_HASHED_IDFA\"}},\"user\":{\"yob\":-1},\"at\":2,\"tmax\":200,\"wseat\":[\"123456\"],\"cur\":[],\"bcat\":[\"IAB7-27\",\"IAB7-29\",\"IAB7-28\",\"IAB7-45\",\"IAB26\",\"IAB7-22\",\"IAB7-44\",\"IAB7-25\",\"IAB7-24\",\"IAB7-30\",\"IAB7-10\",\"IAB7-31\",\"IAB25-3\",\"IAB26-2\",\"IAB25-2\",\"IAB26-1\",\"IAB23-2\",\"IAB25-5\",\"IAB26-4\",\"IAB25-4\",\"IAB26-3\",\"IAB11-1\",\"IAB11-2\",\"IAB7-19\",\"IAB7-16\",\"IAB7-38\",\"IAB7-37\",\"IAB7-18\",\"IAB7-39\",\"IAB7-12\",\"IAB7-34\",\"IAB7-11\",\"IAB7-14\",\"IAB7-36\",\"IAB7-13\",\"IAB7-40\",\"IAB7-5\",\"IAB6-7\",\"IAB7-21\",\"IAB8-5\",\"IAB7-20\",\"IAB19-3\",\"IAB5-2\",\"IAB7-2\",\"IAB7-3\",\"IAB14-3\",\"IAB7-8\",\"IAB7-9\"],\"badv\":[\"king.com\",\"supercell.net\",\"paps.com\",\"fhs.com\",\"china.supercell.com\",\"supercell.com\"]}";

    byte[] plainBytes = plainStr.getBytes();
    System.out.println("plain length = " + plainBytes.length);

    byte[] compressBytes = compress(plainBytes);
    System.out.println("compressed length = " + compressBytes.length);
    System.out.println("compressed length = " + new String(compressBytes));
    byte[] uncompressBytes = uncompress(compressBytes);
    System.out.println("after uncompress: " + uncompressBytes.length);
    System.out.println(new String(uncompressBytes));
  }

}
