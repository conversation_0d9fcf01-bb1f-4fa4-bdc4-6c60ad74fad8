package com.iflytek.traffic.util;

import lombok.Getter;

public enum TimeZone {

    UTC_0(0, "UTC+00:00","GMT+00:00", -8),
    UTC_1(1, "UTC+01:00","GMT+01:00", -7), // 东一区
    UTC_2(2, "UTC+02:00","GMT+02:00", -6),
    UTC_3(3, "UTC+03:00","GMT+03:00", -5),
    UTC_4(4, "UTC+04:00","GMT+04:00", -4),
    UTC_5(5, "UTC+05:00","GMT+05:00", -3),
    UTC_6(6, "UTC+06:00","GMT+06:00", -2),
    UTC_7(7, "UTC+07:00","GMT+07:00", -1),
    UTC_8(8, "UTC+08:00","GMT+08:00", 0), // 东八区
    UTC_9(9, "UTC+09:00","GMT+09:00", 1),
    UTC_10(10, "UTC+10:00","GMT+10:00", 2),
    UTC_11(11, "UTC+11:00","GMT+11:00", 3),
    UTC_12(12, "UTC+12:00","GMT+12:00", 4), // 东西十二时区
    UTC_13(13, "UTC-11:00","GMT-11:00", -19), // 西十一区
    UTC_14(14, "UTC-10:00","GMT-10:00", -18),
    UTC_15(15, "UTC-09:00","GMT-09:00", -17),
    UTC_16(16, "UTC-08:00","GMT-08:00", -16),
    UTC_17(17, "UTC-07:00","GMT-07:00", -15),
    UTC_18(18, "UTC-06:00","GMT-06:00", -14),
    UTC_19(19, "UTC-05:00","GMT-05:00", -13),
    UTC_20(20, "UTC-04:00","GMT-04:00", -12),
    UTC_21(21, "UTC-03:00","GMT-03:00", -11),
    UTC_22(22, "UTC-02:00","GMT-02:00", -10),
    UTC_23(23, "UTC-01:00","GMT-01:00", -9); // 西一区

    @Getter
    private int id;

    @Getter
    private String utc;

    @Getter
    private String gmt;

    @Getter
    private int offsetHours;

    TimeZone(int id, String utc, String gmt, int offsetHours) {
        this.id = id;
        this.utc = utc;
        this.gmt = gmt;
        this.offsetHours = offsetHours;
    }

    public static TimeZone of(int id) {
        return TimeZone.values()[id];
    }
}
