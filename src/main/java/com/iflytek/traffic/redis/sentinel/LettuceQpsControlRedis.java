package com.iflytek.traffic.redis.sentinel;

import com.iflytek.traffic.redis.BeanNameOfRedisClient;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component(BeanNameOfRedisClient.QPS_CONTROL_REDIS)
@Slf4j
public class LettuceQpsControlRedis extends LettuceSentinelClient {

    @Value("${redis.qps.sentinel.nodes}")
    private String nodes;

    @Value("${redis.qps.masterid:qps}")
    private String masterId;

    @Value("${redis.qps.connection_timeout:200}")
    private long connectTimeout;
    @Value("${redis.qps.command_timeout:5}")
    private long commandTimeout;

    @Value("${redis.qps.lettuce.pool.max-active:0}")
    private int ioThreadPoolSize;

    @Value("${redis.qps.password:}")
    private String password;

    @PostConstruct
    public void startup() {
        try {
            initSentinel(nodes, masterId, ioThreadPoolSize, connectTimeout, commandTimeout, password);
        } catch (Exception e) {
            log.error("qpsCtrlRedis init error: {}", e.getMessage(), e);
        }
    }

    @PreDestroy
    public void shutdown() {
        super.shutdown();
    }

}
