package com.iflytek.traffic.redis.cluster;

import com.iflytek.traffic.redis.LettuceClient;
import io.lettuce.core.KeyValue;
import io.lettuce.core.RedisURI;
import io.lettuce.core.ScriptOutputType;
import io.lettuce.core.api.sync.RedisCommands;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.cluster.RedisClusterClient;
import io.lettuce.core.cluster.api.StatefulRedisClusterConnection;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Slf4j
public class LettuceClusterClient implements LettuceClient {

    protected RedisClusterClient client;
    protected StatefulRedisClusterConnection<String, String> connection;

    protected void initCluster(String clusterNodes, int ioThreadPoolSize, long connectTimeout, long commandTimeout,
        int redirect, boolean dynamicRefreshSources, String password) throws Exception {
        ClientResources resources = DefaultClientResources.builder()
                .ioThreadPoolSize(ioThreadPoolSize > 0 ? ioThreadPoolSize : Runtime.getRuntime().availableProcessors())
                .build();
        List<RedisURI> seedUris = nodes2Uris(clusterNodes, Duration.ofMillis(connectTimeout), password);
        client = RedisClusterClient.create(resources, seedUris);

        ClusterTopologyRefreshOptions topologyRefreshOptions = ClusterTopologyRefreshOptions.builder()
            .enablePeriodicRefresh().enableAllAdaptiveRefreshTriggers().dynamicRefreshSources(dynamicRefreshSources)
            .adaptiveRefreshTriggersTimeout(Duration.ofSeconds(600)).build();

        // TimeoutOptions timeoutOptions =
        // TimeoutOptions.builder().timeoutCommands(true)
        // .fixedTimeout(Duration.ofMillis(timeoutOption)).build();
        //
        // ClusterClientOptions clientOptions = ClusterClientOptions.builder()
        // .topologyRefreshOptions(topologyRefreshOptions).timeoutOptions(timeoutOptions)
        // .maxRedirects(maxRedirects).build();

        ClusterClientOptions clientOptions =
            ClusterClientOptions.builder().topologyRefreshOptions(topologyRefreshOptions).maxRedirects(redirect)
                .validateClusterNodeMembership(false).build();

        client.setOptions(clientOptions);
        client.setDefaultTimeout(Duration.ofMillis(commandTimeout));

        int connectCount = 0;
        while (true) {
            try {
                connection = client.connect();
                break;
            } catch (Exception e) {
                // 第一次初始化时容易超时，重试几次
                if (connectCount++ < 5) {
                    Thread.sleep(200);
                    continue;
                }
                throw new Exception("client.connect:" + e.getMessage(), e);
            }
        }
    }

    public void shutdown() {
        connection.close();
        client.shutdown();
    }

    @Override
    public String get(String key) {
        String result = connection.sync().get(key);
        if (log.isDebugEnabled()) {
            log.debug("SpringRedisClient|get key:{} result:{}", key, result);
        }
        return result;
    }

    @Override
    public void set(String key, String value) {
        // 先默认2小时
        connection.sync().setex(key, 7200, value);
        // redisTemplate.opsForValue().set(key, value);
        if (log.isDebugEnabled()) {
            log.debug("SpringRedisClient|set key:{} value:{}", key, value);
        }
    }

    @Override
    public List<String> mget(String... keys) {
        List<KeyValue<String, String>> sResult = connection.sync().mget(keys);
        List<String> result = getValueList(sResult);
        if (log.isDebugEnabled()) {
            log.debug("SpringRedisClient|mget keys:{} result:{}", keys, result);
        }
        if (CollectionUtils.isEmpty(result)) {
            return new LinkedList<String>();
        }
        return result;
    }

    @Override
    public List<String> mget(List<String> keys) {
        return mget(keys.toArray(new String[0]));
    }

    @Override
    public Map<String, String> hgetall(String key) {
        Map<String, String> result = connection.sync().hgetall(key);
        if (log.isDebugEnabled()) {
            log.debug("SpringRedisClient|hgetall key:{} result:{}", key, result);
        }
        return result;
    }

    @Override
    public void hset(String key, String field, String value) {
        if (log.isDebugEnabled()) {
            log.debug("SpringRedisClient|hset key:{} field:{} value:{}", key, field, value);
        }
        connection.sync().hset(key, field, value);
    }

    @Override
    public String hget(String key, String field) {
        String result = connection.sync().hget(key, field);
        if (log.isDebugEnabled()) {
            log.debug("SpringRedisClient|hget key:{} field:{} result:{}", key, field, result);
        }
        return result;
    }

    @Override
    public List<String> hmget(String key, String... fields) {
        List<KeyValue<String, String>> sResult = connection.sync().hmget(key, fields);
        List<String> result = getValueList(sResult);
        if (log.isDebugEnabled()) {
            log.debug("SpringRedisClient|hmget key:{} field:{} result:{}", key, fields, result);
        }
        return result;
    }

    @Override
    public List<String> hmget(String key, List<String> fields) {
        return hmget(key, fields.toArray(new String[0]));
    }

    @Override
    public Long incrby(String key, long amount) {
        Long result = connection.sync().incrby(key, amount);
        if (log.isDebugEnabled()) {
            log.debug("SpringRedisClient|incrBy key:{} amount:{} result:{}", key, amount, result);
        }
        return result;
    }

    @Override
    public void setex(String key, String value, int seconds) {
        if (log.isDebugEnabled()) {
            log.debug("SpringRedisClient|setex key:{} value:{} timeout:{}", key, value, seconds);
        }
        connection.sync().setex(key, seconds, value);
    }

    @Override
    public Long eval(DefaultRedisScript<Long> redisScript, List<String> keys, List<String> args) {
        try {
            Long result = connection.sync().evalsha(redisScript.getSha1(), ScriptOutputType.INTEGER,
                    keys.toArray(new String[0]), args.toArray(new String[0]));
            if (log.isDebugEnabled()) {
                log.debug("redis cluster eval lua:{} keys:{} args:{} result:{}", redisScript, keys, args, result);
            }
            return result;
        } catch (Exception e) {
            Long result = connection.sync().eval(redisScript.getScriptAsString(), ScriptOutputType.INTEGER,
                    keys.toArray(new String[0]), args.toArray(new String[0]));
            if (log.isDebugEnabled()) {
                log.debug("redis cluster eval lua:{} keys:{} args:{} result:{}", redisScript, keys, args, result);
            }
            return result;
        }
    }

    @Override
    public void del(String key) {
        connection.sync().del(key);
        if (log.isDebugEnabled()) {
            log.debug("SpringRedisClient|del key:{}", key);
        }
    }

    @Override
    public Long add(String key, String... values) {
        return connection.sync().sadd(key, values);
    }

    @Override
    public Long remove(String key, String... values) {
        return connection.sync().srem(key, values);
    }

    @Override
    public Long hincrby(String key, String field, long amount) {
        if (log.isDebugEnabled()) {
            log.debug("SpringRedisClient|hincrby key:{} field:{} amount:{}", key, field, amount);
        }
        return connection.sync().hincrby(key, field, amount);
    }

    @Override
    public boolean expire(String key, long seconds) {
        if (log.isDebugEnabled()) {
            log.debug("SpringRedisClient|expire key:{}  seconds:{}", key, seconds);
        }
        return connection.sync().expire(key, seconds);
    }

    public Long pttl(String key) {
        Long result = connection.sync().pttl(key);
        if (log.isDebugEnabled()) {
            log.debug("SpringRedisClient|get key:{} pttl:{}", key, result);
        }
        return result;
    }

    @Override
    public String spop(String key) {
        return connection.sync().spop(key);
    }
}
