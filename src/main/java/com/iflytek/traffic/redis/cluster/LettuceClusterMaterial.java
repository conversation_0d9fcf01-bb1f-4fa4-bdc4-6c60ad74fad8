package com.iflytek.traffic.redis.cluster;

import com.iflytek.traffic.redis.BeanNameOfRedisClient;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Component(BeanNameOfRedisClient.MATERIAL_REDIS)
@Slf4j
public class LettuceClusterMaterial extends LettuceClusterClient {

    @Value("${redis.material.cluster.nodes}")
    private String clusterNodes;

    @Value("${redis.material.cluster.max-redirects:1}")
    private int redirect;
    @Value("${redis.material.cluster.connection_timeout:200}")
    private long connectTimeout;
    @Value("${redis.material.cluster.command_timeout:5}")
    private long commandTimeout;

    @Value("${redis.material.cluster.lettuce.pool.max-active:0}")
    private int ioThreadPoolSize;

    @Value("${redis.material.cluster.dynamic_refresh_sources:true}")
    private boolean dynamicRefreshSources;
    @Value("${redis.material.password:}")
    private String password;

    @PostConstruct
    public void startup() {
        try {
            initCluster(clusterNodes, ioThreadPoolSize, connectTimeout, commandTimeout, redirect, dynamicRefreshSources,
                    password);
        } catch (Exception e) {
            log.error("materialRedis init error: {}", e.getMessage(), e);
        }
    }

    @PreDestroy
    public void shutdown() {
        super.shutdown();
    }

}
