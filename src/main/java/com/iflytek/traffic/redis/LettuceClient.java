package com.iflytek.traffic.redis;

import io.lettuce.core.KeyValue;
import io.lettuce.core.RedisURI;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.time.Duration;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public interface LettuceClient {
    public String get(String key);

    public void set(String key, String value);

    public List<String> mget(String... keys);

    public List<String> mget(List<String> keys);

    public Map<String, String> hgetall(String key);

    public String hget(String key, String field);

    public void hset(String key, String field, String value);

    public List<String> hmget(String key, String... fields);

    public List<String> hmget(String key, List<String> fields);

    public Long incrby(String key, long increment);

    public void setex(String key, String value, int seconds);

    public Long eval(DefaultRedisScript<Long> redisScript, List<String> keys, List<String> args);

    public void del(String key);

    public Long add(String key, String... values);

    public Long remove(String key, String... values);

    public Long hincrby(String key, String field, long amount);

    public boolean expire(String key, long seconds);

    String spop(String key);

    public default List<String> getValueList(List<KeyValue<String, String>> sResult) {
        List<String> result = new LinkedList<String>();
        for (KeyValue<String, String> keyValue : sResult) {
            if (keyValue.hasValue()) {
                result.add((String)keyValue.getValue());
            } else {
                result.add(null);
            }
        }
        return result;
    }

    public default List<RedisURI> nodes2Uris(String nodes, Duration timeout, String password) {
        List<RedisURI> uris = new ArrayList<>();

        for (String node : nodes.split(",")) {
            String[] ipPort = node.trim().split(":");
            String ip = ipPort[0].trim();
            int port = Integer.valueOf(ipPort[1].trim());
            RedisURI uri = new RedisURI(ip, port, timeout);
            uri.setPassword(password);
            uris.add(uri);
        }

        return uris;
    }
}
