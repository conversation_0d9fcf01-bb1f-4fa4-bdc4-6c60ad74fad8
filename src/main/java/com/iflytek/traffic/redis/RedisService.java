package com.iflytek.traffic.redis;

import com.iflytek.traffic.util.SpringContextHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 提供静态方法使用，为了非spring托管对象也能访问redis，并将redis进行了一层封装，提供业务相关的接口
 *
 * <AUTHOR>
 */
@ComponentScan(basePackages = {"com.iflytek.voiceads.adx.redis"})
@Service("redisService")
@Slf4j
@DependsOn({"springContextHelper"})
public class RedisService {
    /* ===== 业务访问静态接口 ===== */

    // QPS控制lua脚本
    private static DefaultRedisScript<Long> redisScriptOfQpsCtl = new DefaultRedisScript<Long>(
            "redis.replicate_commands() local r,k,limit,window=redis.call,KEYS[1],tonumber(ARGV[1]),tonumber(ARGV[2]) local now=r('TIME')[1] r('ZREMRANGEBYSCORE',k,0,now-window) local count=r('ZCARD',k) if count<limit then r('ZADD',k,now,now..':'..math.random()) r('EXPIRE',k,window) end return count>=limit and 1 or 0", Long.class);

    public static boolean isQpsExceeded(String key, int limit, int window) {
        try {
            LettuceClient qpsCtrl = SpringContextHelper.getBean(BeanNameOfRedisClient.QPS_CONTROL_REDIS, LettuceClient.class);
            List<String> keys = List.of(key);
            List<String> args = List.of(String.valueOf(limit), String.valueOf(window));
            Long result = qpsCtrl.eval(redisScriptOfQpsCtl, keys, args);
            return result != null && result == 1;
        } catch (Exception e) {
            log.error("RedisService isQpsExceeded key:{} limit:{} window:{} error:{}", key, limit, window, e.getMessage(), e);
            return true;
        }
    }

}
