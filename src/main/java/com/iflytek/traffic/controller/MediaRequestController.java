package com.iflytek.traffic.controller;

import com.iflytek.traffic.data.provider.SspDataProvider;
import com.iflytek.traffic.service.MediaRequestPoolService;
import com.iflytek.traffic.service.MeterService;
import com.iflytek.traffic.service.QpsControlService;
import com.iflytek.traffic.ssp.SspEp;
import io.micrometer.core.instrument.Counter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@RestController
@RequestMapping(value = "/engine/bid")
@Slf4j
public class MediaRequestController {

    @Autowired
    private MeterService meterService;

    @Autowired
    private MediaRequestPoolService mediaRequestPoolService;

    @Autowired
    private SspDataProvider sspDataProvider;

    private final Map<String, Counter> mediaRequestCountMeterMap = new ConcurrentHashMap<>();

    /**
     * 媒体请求计数
     *
     * @param sspEp 下游ep
     */
    private void markMediaRequest(SspEp sspEp) {
        if (mediaRequestCountMeterMap.containsKey(sspEp.getPath())) {
            mediaRequestCountMeterMap.get(sspEp.getPath()).increment();
        } else {
            synchronized (MediaRequestController.class) {
                if (mediaRequestCountMeterMap.containsKey(sspEp.getPath())) {
                    mediaRequestCountMeterMap.get(sspEp.getPath()).increment();
                    return;
                }
                String meterKey = "ssp." + sspEp.getPath() + ".request.count.total";
                log.info("config ssp request count meter: {}", meterKey);
                Counter counter = meterService.count(meterKey);
                counter.increment();
                mediaRequestCountMeterMap.put(sspEp.getPath(), counter);
            }
        }
    }

    @RequestMapping(value = "/{path}", method = RequestMethod.POST)
    public void req(@PathVariable("path") String path, HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("receive ssp ep request path: {}", path);
        SspEp sspEp = sspDataProvider.getSspEpByPath(path);
        if (sspEp == null) {
            log.warn("unknown ssp ep request path {}", path);
            response.setStatus(204);
            response.setContentType("text/plain");
            return;
        } else {
            markMediaRequest(sspEp);
        }
        mediaRequestPoolService.execute(request, response, sspEp);
    }

}
