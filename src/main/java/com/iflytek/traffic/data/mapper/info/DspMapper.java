package com.iflytek.traffic.data.mapper.info;

import com.iflytek.traffic.data.entity.DspEpInfo;
import com.iflytek.traffic.data.entity.DspEpLimited;
import com.iflytek.traffic.data.entity.DspInfo;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface DspMapper {
    @Select("select dde.*,dd.protocol,dd.dsp_name,dd.docking_type,dd.currency_id,dd.is_gzip, "
            + "dd.is_pmp,dd.settlement_type,dd.timeout,dd.imp_timeout as dsp_imp_timeout,dd.dsp_status from iflytek_overseas_adx.d_dsp_ep dde "
            + "left join iflytek_overseas_adx.d_dsp dd on dde.dsp_id = dd.id "
            + "where dde.is_del = 0 and dde.ep_status = 1 and dd.dsp_status = 1;")
    List<DspEpInfo> selectValidEp();

    @Select("select * from iflytek_overseas_adx.d_dsp dd where dd.dsp_status =1;")
    List<DspInfo> selectValidDsp();

    @Select("select * from iflytek_overseas_adx.d_dsp_ep_limited where is_del = 0 and is_limited = 1;")
    List<DspEpLimited> selectDspEpRpmQps();
}
