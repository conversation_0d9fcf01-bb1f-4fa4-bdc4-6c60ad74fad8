package com.iflytek.traffic.data.mapper.info;

import com.iflytek.traffic.data.entity.SspEpInfo;
import com.iflytek.traffic.data.entity.SspIncludeInfo;
import com.iflytek.traffic.data.entity.SspInfo;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface SspMapper {

    @Select("select * from iflytek_overseas_adx.d_ssp ds where ds.ssp_status =1;")
    List<SspInfo> selectValidSsp();


    @Select("select dse.*,ds.protocol,ds.ssp_identify,ds.ssp_name,ds.ssp_domain,ds.contacts,ds.phone,ds.mail,ds.remark,"+
            "ds.currency_id,ds.is_gzip,ds.settlement_type,ds.material_hour,ds.app_include,ds.crid_include,ds.ssp_status\n" +
            "from iflytek_overseas_adx.d_ssp_ep dse\n" +
            "left join iflytek_overseas_adx.d_ssp ds on dse.ssp_id = ds.id \n" +
            "where dse.is_del =0 and ds.ssp_status =1 and dse.ep_status =1;")
    List<SspEpInfo> selectValidSspEp();

    @Select("select dsi.id, dsi.ssp_id, dsi.include_app, dsi.include_crid, ds.app_include, ds.crid_include "
            + "from iflytek_overseas_adx.d_ssp_include dsi left join iflytek_overseas_adx.d_ssp ds on ds.id = dsi.ssp_id "
            + "where dsi.is_del = 0 and ds.ssp_status = 1")
    List<SspIncludeInfo> selectSspIncludeInfo();
}
