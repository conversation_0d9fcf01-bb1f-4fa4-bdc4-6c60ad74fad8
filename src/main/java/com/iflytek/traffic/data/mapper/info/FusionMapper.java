package com.iflytek.traffic.data.mapper.info;

import com.iflytek.traffic.data.entity.AppFusionInfo;
import com.iflytek.traffic.data.entity.SlotFusionInfo;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface FusionMapper {

    @Select("select r.dsp_id, sa.ssp_id, sa.pkg as srcPkg, r.adx_app, r.pkg as tarPkg, r.adx_tag from iflytek_overseas_adx.d_ssp_app as sa "
            + "join iflytek_overseas_adx.d_dsp_app_rule as r on sa.id = r.ssp_app_id where r.is_del = 0 and sa.app_status = 1")
    List<AppFusionInfo> selectAll();

    @Select("select r.dsp_id, ss.ssp_id, r.ssp_slot_id, r.dsp_slot_id, ss.ssp_tag as sspSlotTag, ds.adx_tag as tarSlotTag, ds.pkg as tarPkg, ds.adx_app as tarAppTag "
            + "from iflytek_overseas_adx.d_dsp_slot_rule as r join iflytek_overseas_adx.d_ssp_slot as ss on r.ssp_slot_id = ss.id "
            + "join iflytek_overseas_adx.d_dsp_slot as ds on r.dsp_slot_id = ds.id where r.is_del = 0 and ds.slot_status = 1 and ss.slot_status = 1")
    List<SlotFusionInfo> selectAllSlot();
}
