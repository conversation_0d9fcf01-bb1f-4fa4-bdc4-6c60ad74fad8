package com.iflytek.traffic.data.mapper.info;

import com.iflytek.traffic.data.entity.DspProfitInfo;
import com.iflytek.traffic.data.entity.SspProfitInfo;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ProfitMapper {
    @Select("select * from iflytek_overseas_adx.d_dsp_charge where is_del = 0")
    List<DspProfitInfo> selectAllDspProfit();


    @Select("select * from iflytek_overseas_adx.d_ssp_charge where is_del = 0")
    List<SspProfitInfo> selectAllSspProfit();
}
