package com.iflytek.traffic.data.mapper.info;

import com.iflytek.traffic.data.entity.AppCategoryResource;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface AppCategoryResourceMapper {

    @Select("select adx_app_pkg, category_id from iflytek_overseas_adx.d_app_category_resource where is_del = 0")
    List<AppCategoryResource> selectValidAppCategoryResource();

}
