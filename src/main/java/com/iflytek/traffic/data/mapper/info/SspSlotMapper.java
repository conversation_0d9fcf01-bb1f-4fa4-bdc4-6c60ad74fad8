package com.iflytek.traffic.data.mapper.info;

import com.iflytek.traffic.data.entity.SspSlotInfo;
import org.apache.ibatis.annotations.Select;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface SspSlotMapper {

	@Select("<script>" +
			"SELECT ssp_id, ssp_tag, slot_type " +
			"FROM iflytek_overseas_adx.d_ssp_slot " +
			"WHERE ssp_id IN " +
			"<foreach item='id' collection='sspIdList' open='(' separator=',' close=')'>" +
			"   #{id}" +
			"</foreach>" +
			" AND slot_status = 1" +
			"</script>")
	List<SspSlotInfo> selectSspSlotInfoBySspId(List<Integer> sspIdList);
}
