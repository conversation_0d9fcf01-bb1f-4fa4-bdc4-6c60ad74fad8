package com.iflytek.traffic.data.mapper.info;

import com.iflytek.traffic.data.entity.EpSupportInfo;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface SupportMapper {

    @Select("select ddes.id,ddes.ep_id,ddes.support_id,ddes.es_status,dds.support_status,ddsc.config_id,ddsc.config_value, ddsc.include\n" +
            "from iflytek_overseas_adx.d_dsp_ep_support ddes \n" +
            "left join iflytek_overseas_adx.d_dsp_support dds on ddes.support_id = dds.id \n" +
            "left join iflytek_overseas_adx.d_dsp_support_config ddsc on dds.id =ddsc.support_id\n" +
            "where ddes.es_status = 1 and ddes.is_del = 0 and dds.support_status =1 and dds.is_del =0 and ddsc.is_del =0;")
    List<EpSupportInfo> selectValidSupport();
}
