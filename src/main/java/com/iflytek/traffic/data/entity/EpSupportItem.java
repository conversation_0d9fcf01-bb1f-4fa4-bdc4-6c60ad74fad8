package com.iflytek.traffic.data.entity;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class EpSupportItem {
    private Integer configId;
    private List<String> configItemList;
    private Integer include; // 1: 包含, 2: 排除
    private List<FloorPriceDirect> fpDirects; // 底价定向
    private SupplierChainDirect supplierChainDirect;

    public void setConfigItemList(List<String> configItemList) {
        if (CollUtil.isNotEmpty(configItemList)) {
            this.configItemList = configItemList.stream()
                    .map(String::trim)
                    .filter(item -> !item.isEmpty())
                    .collect(Collectors.toList());
            if (configId.equals(1014)) {
                // 品牌转小写
                this.configItemList = this.configItemList.stream()
                        .map(String::toLowerCase)
                        .collect(Collectors.toList());
            }
            return;
        }
        this.configItemList = configItemList;
    }
}
