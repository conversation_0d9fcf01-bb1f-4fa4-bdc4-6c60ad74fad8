package com.iflytek.traffic.data.entity;

import lombok.Data;

@Data
public class DspInfo {
    private Integer id;
    /**
     * DSP平台名称
     */
    private String dspName;

    /**
     * 对接状态：1:测试；2:正式投放
     */
    private Integer dockingType;

    /**
     * 币种ID，1：美元；2:人民币
     */
    private Integer currencyId;

    /**
     * 是否支持 gzip：0:不支持；1:支持
     */
    private Integer isGzip;

    /**
     * 是否支持 pmp，0:不支持1；1:支持
     */
    private Integer isPmp;

    /**
     * 结算类型：1:adm；2:burl
     */
    private Integer settlementType;

    /**
     * DSP请求超时时间，单位毫秒
     */
    private Integer timeout;

    /**
     * 平台的可状态：1表示可使用，2表示不可使用
     */
    private Integer dspStatus;
}
