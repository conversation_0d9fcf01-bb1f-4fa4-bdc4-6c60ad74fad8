package com.iflytek.traffic.data.entity;

import lombok.Data;

/**
 * <AUTHOR>
 */

@Data
public class MachineAuditMaterialInfo {
    private String region;
    private Long materialId;
    private String materialHashId;
    private String admHashId;
    private Integer checkStatus;

    public interface Constants {
        int CHECK_STATUS_UPLOAD = 1;
        int CHECK_STATUS_WAIT = 2;
        int CHECK_STATUS_PASS = 3;
        int CHECK_STATUS_DENY = 4;
        int CHECK_STATUS_ERROR = 5;
    }
}
