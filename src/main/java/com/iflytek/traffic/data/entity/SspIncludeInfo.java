package com.iflytek.traffic.data.entity;

import lombok.Data;

@Data
public class SspIncludeInfo {

    private Integer id;
    /**
     * 第三方平台ID
     */
    private Integer sspId;

    /**
     * 屏蔽应用：0:不设置；1:指定；2:屏蔽
     */
    private Integer appInclude;

    /**
     * app
     */
    private String includeApp;

    /**
     * 屏蔽素材：0:不设置；1:指定；2:屏蔽
     */
    private Integer cridInclude;

    /**
     * 创意
     */
    private String includeCrid;

    /**
     * 是否被删除，0：正常，1：已删除
     */
    private Integer isDel;

}
