package com.iflytek.traffic.data.entity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

@Getter
@Setter
public class DspEpQps {

    private Integer dspEpId;

    private Map<Integer, SingleQpsConfig> singleQpsConfigMap = new HashMap<>();

    public List<KeyAndRatio<String, Double>> genAllConfigQpsKeyAndRatio() {
        List<KeyAndRatio<String, Double>> ssp = getAllConfigKeyAndRatio(1001);
        List<KeyAndRatio<String, Double>> bundle = getAllConfigKeyAndRatio(1003);
        List<KeyAndRatio<String, Double>> region = getAllConfigKeyAndRatio(1004);
        List<KeyAndRatio<String, Double>> adType = getAllConfigKeyAndRatio(1005);
        return combineLists(ssp, adType, region, bundle);
    }

    private static List<KeyAndRatio<String, Double>> combineLists(List<KeyAndRatio<String, Double>>... lists) {
        List<KeyAndRatio<String, Double>> result = new ArrayList<>();
        if (lists == null) return result;

        result.add(new KeyAndRatio<String, Double>("", 1.0d));  // 初始化空组合

        for (List<KeyAndRatio<String, Double>> list : lists) {
            if (list == null || list.isEmpty()) continue;  // 跳过空或null列表

            List<KeyAndRatio<String, Double>> newCombinations = new ArrayList<>();
            for (KeyAndRatio<String, Double> res : result) {
                for (KeyAndRatio<String, Double> element : list) {
                    String key = res.key.isBlank() ? element.key : res.key + "|" + element.key;  // 构建新键
                    Double ratio = res.ratio * element.ratio / 100;  // 计算新比例
                    newCombinations.add(new KeyAndRatio<String, Double>(key, ratio));  // 构建新组合
                }
            }
            result = newCombinations;  // 更新结果集
        }
        return result;
    }

    public List<KeyAndRatio<String, Double>> genQpsConfigKeyAndRatio(Integer sspId, Integer adType, Long region, String bundle) {
        KeyAndRatio<String, Double> sspKr = getConfigKeyAndRatio(1001, String.valueOf(sspId));
        KeyAndRatio<String, Double> adTypeKr = getConfigKeyAndRatio(1005, String.valueOf(adType));
        KeyAndRatio<String, Double> regionKr = getConfigKeyAndRatio(1004, String.valueOf(region));
        KeyAndRatio<String, Double> bundleKr = getConfigKeyAndRatio(1003, bundle);
        List<KeyAndRatio<String, Double>> keyAndRatios = Lists.newArrayList(sspKr, adTypeKr, regionKr, bundleKr);
        List<KeyAndRatio<String, Double>> afterFilter = keyAndRatios.stream().filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(afterFilter)) {
            return null;
        }
        return afterFilter;
    }

    private KeyAndRatio<String, Double> getConfigKeyAndRatio(Integer configId, String value) {
        if (!singleQpsConfigMap.containsKey(configId)) {
            return null;
        }
        SingleQpsConfig singleQpsConfig = singleQpsConfigMap.get(configId);
        if (!singleQpsConfig.getValue2Ratio().containsKey(value)) {
            if (singleQpsConfig.getTotalRatio() < 100.0d) {
                return new KeyAndRatio<String, Double>(configId + "_" + "others", 100.0d - singleQpsConfig.getTotalRatio());
            } else {
                return new KeyAndRatio<String, Double>(configId + "_" + "miss", 0.0d);
            }
        }
        return new KeyAndRatio<>(configId + "_" + value, singleQpsConfig.getValue2Ratio().get(value));
    }

    private List<KeyAndRatio<String, Double>> getAllConfigKeyAndRatio(Integer configId) {
        if (!singleQpsConfigMap.containsKey(configId)) {
            return null;
        }
        SingleQpsConfig singleQpsConfig = singleQpsConfigMap.get(configId);
        if (MapUtil.isEmpty(singleQpsConfig.getValue2Ratio())) {
            return null;
        }
        List<KeyAndRatio<String, Double>> keyAndRatios = Lists.newArrayList();
        for (Map.Entry<String, Double> entry : singleQpsConfig.getValue2Ratio().entrySet()) {
            keyAndRatios.add(new KeyAndRatio<>(configId + "_" + entry.getKey(), entry.getValue()));
        }
        if (singleQpsConfig.getTotalRatio() < 100.0d) {
            keyAndRatios.add(new KeyAndRatio<>(configId + "_" + "others", 100.0d - singleQpsConfig.getTotalRatio()));
        }
        return keyAndRatios;
    }

    public record KeyAndQps<String, Integer>(String key, Integer qps) {
    }

    public record KeyAndRatio<String, Double>(String key, Double ratio) {
    }

    @Getter
    @Setter
    public static class SingleQpsConfig {

        // configId：1001 ssp，configId：1003 包名，configId：1004 地域，configId：1005 广告形式
        private Integer configId;

        private Double totalRatio = 0.0;

        private Map<String, Double> value2Ratio = new HashMap<>();

    }

    @Getter
    @Setter
    public static class DspEpQpsConfig {

        private Integer dspEpId;

        private Integer configId;

        private Double ratio;

        private String configVal;

        private Integer qps;
    }

    public static void main(String[] args) {
        List<KeyAndRatio<String, Double>> ssp = Lists.newArrayList(
                new KeyAndRatio<>("ssp1", 50.0d),
                new KeyAndRatio<>("ssp2", 50.0d)
        );
        List<KeyAndRatio<String, Double>> adType = Lists.newArrayList(
                new KeyAndRatio<>("100", 60.0d),
                new KeyAndRatio<>("400", 30.0d),
                new KeyAndRatio<>("others", 10.0d)
        );
        List<KeyAndRatio<String, Double>> region = Lists.newArrayList(
//                new KeyAndRatio<>("1000000000", 70.0d),
//                new KeyAndRatio<>("200000000", 30.0d)
        );
        List<KeyAndRatio<String, Double>> bundle = Lists.newArrayList(
                new KeyAndRatio<>("com.iflytek.test", 50.0d),
                new KeyAndRatio<>("others", 50.0d)
        );
        List<KeyAndRatio<String, Double>> combined = combineLists(ssp, adType, region, bundle);
        System.out.println(combined);
    }

}