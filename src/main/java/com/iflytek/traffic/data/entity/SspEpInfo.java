package com.iflytek.traffic.data.entity;

import lombok.Data;

@Data
public class SspEpInfo {
    private Integer id;
    /**
     * ssp_id对应d_ssp表id
     */
    private Integer sspId;

    /**
     * EP节点ID：0：全部国家；1:东南亚；2:美洲；3:欧洲
     */
    private Integer nodeId;

    /**
     * 媒体请求qps
     */
    private Long qps;

    /**
     * EP的可使用状态，1表示可使用，2表示不可使用，
     */
    private Integer epStatus;

    /**
     * 是否被删除，0：正常，1：已删除
     */
    private Integer isDel;

    /**
     * 第三方平台标识
     */
    private String sspIdentify;

    /**
     * 第三方平台名称
     */
    private String sspName;

    /**
     * 第三方平台域名
     */
    private String sspDomain;

    /**
     * 联系人
     */
    private String contacts;

    /**
     * 联系人手机号
     */
    private String phone;

    /**
     * 联系人邮箱
     */
    private String mail;

    /**
     * 备注
     */
    private String remark;

    /**
     * 币种ID，1：美元
     */
    private Integer currencyId;

    /**
     * 是否支持Gzip， 0不支持，1支持
     */
    private Integer isGzip;

    /**
     * 结算类型：1:adm；2:burl
     */
    private Integer settlementType;

    /**
     * 素材有效期：小时
     */
    private Integer materialHour;

    /**
     * 屏蔽应用：0:不设置；1:指定；2:屏蔽
     */
    private Integer appInclude;

    /**
     * 屏蔽素材：0:不设置；1:指定；2:屏蔽
     */
    private Integer cridInclude;

    /**
     * 平台的可使用状态，1表示可使用，2表示不可使用，
     */
    private Integer sspStatus;

    /**
     * 请求入口path
     */
    private String path;

    /**
     * 协议
     */
    private String protocol;
}
