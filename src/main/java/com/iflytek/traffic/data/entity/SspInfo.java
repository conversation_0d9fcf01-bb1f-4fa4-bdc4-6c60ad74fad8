package com.iflytek.traffic.data.entity;

import lombok.Data;

@Data
public class SspInfo {
    private Integer id;
    /**
     * 第三方平台标识
     */
    private String sspIdentify;

    /**
     * 第三方平台名称
     */
    private String sspName;

    /**
     * 第三方平台域名
     */
    private String sspDomain;

    /**
     * 联系人
     */
    private String contacts;

    /**
     * 联系人手机号
     */
    private String phone;

    /**
     * 联系人邮箱
     */
    private String mail;

    /**
     * 备注
     */
    private String remark;

    /**
     * 币种ID，1：美元
     */
    private Integer currencyId;

    /**
     * 结算类型：1:adm；2:burl
     */
    private Integer settlementType;

    /**
     * 素材有效期：小时
     */
    private Integer materialHour;

    /**
     * 屏蔽应用：0:不设置；1:指定；2:屏蔽
     */
    private Integer appInclude;

    /**
     * 屏蔽素材：0:不设置；1:指定；2:屏蔽
     */
    private Integer cridInclude;

    /**
     * 平台的可使用状态，1表示可使用，2表示不可使用，
     */
    private Integer sspStatus;
}
