package com.iflytek.traffic.data.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * DSP下圈量定向配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@Data
public class EpSupportInfo{

    private Integer id;
    /**
     * ep_id对应d_dsp_ep表id
     */
    private Integer epId;

    /**
     * support_id对应d_dsp_support表id
     */
    private Integer supportId;

    /**
     * 是否启用：1:启用；2:暂停
     */
    private Integer esStatus;


    /**
     * 是否启用：1:启用；2:暂停
     */
    private Integer supportStatus;

    /**
     * 定向类型：1001: ssp; 1002 :ssp ep ; 1003 :包名；1004:地域；1005:广告形式
     */
    private Integer configId;

    /**
     * 定向内容，JOSN数组，包名配置逗号分隔
     */
    private String configValue;

    /**
     *  1：包含，2：排除
     */
    private Integer include;

}
