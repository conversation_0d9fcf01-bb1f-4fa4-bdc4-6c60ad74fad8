package com.iflytek.traffic.data.entity;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SspMaterialInfo {

    /**
     * ssp 物料 id
     */
    private Long id;
    /**
     * dsp 物料 id
     */
    private Long materialId;

    private String materialHashId;

    String region;

    Integer sspId;

    Integer sspEpId;

    /**
     * 审核状态, 0: 黑名单, 1: 待上传, 2: 待审核, 3: 审核通过, 4: 审核拒绝, 5: 审核错误
     */
    private Integer checkStatus;

    /**
     * 扩展信息
     */
    private String ext;


    public interface Constants {
        int CHECK_STATUS_BLACKLIST = 0;
        int CHECK_STATUS_UPLOAD = 1;
        int CHECK_STATUS_WAIT = 2;
        int CHECK_STATUS_PASS = 3;
        int CHECK_STATUS_DENY = 4;
        int CHECK_STATUS_ERROR = 5;
    }
}
