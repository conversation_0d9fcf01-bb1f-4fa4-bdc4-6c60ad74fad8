package com.iflytek.traffic.data.provider;

import com.iflytek.traffic.data.entity.SspMaterialInfo;
import com.iflytek.traffic.data.mapper.info.SspMaterialInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SspMaterialInfoProvider {

    private final SspMaterialInfoMapper sspMaterialInfoMapper;

    private volatile Map<String, SspMaterialInfo> materialInfoMap = new HashMap<>();

    @Scheduled(fixedRateString = "${data.update.interval.ssp.materialInfo:300000}")
    public void update() {
        updateMaterialInfo();
    }

    private void updateMaterialInfo() {
        List<SspMaterialInfo> sspMaterialInfoList = sspMaterialInfoMapper.findAllBlacklist();
        if (CollectionUtils.isEmpty(sspMaterialInfoList)) {
            materialInfoMap = new HashMap<>();
            log.info("sspMaterialInfoList is null");
            return;
        }

        HashMap<String, SspMaterialInfo> tmpSspMaterialInfoMap = new HashMap<>();
        for (SspMaterialInfo sspMaterialInfo : sspMaterialInfoList) {
            String key = sspMaterialInfo.getRegion() + ":" + sspMaterialInfo.getSspId()
                    + ":" + sspMaterialInfo.getSspEpId() + ":" + sspMaterialInfo.getMaterialHashId();
            tmpSspMaterialInfoMap.put(key, sspMaterialInfo);
        }

        materialInfoMap = tmpSspMaterialInfoMap;
    }

    public SspMaterialInfo getSspBlackMaterialInfo(String region, Integer sspId, Integer sspEpId, String materialHashId) {
        return materialInfoMap.get(region + ":" + sspId + ":" + sspEpId + ":" + materialHashId);
    }
}
