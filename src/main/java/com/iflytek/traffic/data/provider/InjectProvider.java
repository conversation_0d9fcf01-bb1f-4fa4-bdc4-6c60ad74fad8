package com.iflytek.traffic.data.provider;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class InjectProvider {

    private String jsInjectValue = "</script>\n" +
            "        <script type=\"text/javascript\">\n" +
            "          var a = document.createElement(\"img\");\n" +
            "          a.setAttribute(\"src\", \"__VIEW__\");\n" +
            "          a.style.display = \"none\";\n" +
            "          document.body.append(a);\n" +
            "        </script>";


    /**
     * type=1,js模板；type=2,vast模板；type=3，native
     *
     * @param adm
     * @param viewLink
     * @param type
     * @return
     */
    public String injectView(String adm, String viewLink, int type) {
        try {
            if (type == 1) {
                String jsReplace = jsInjectValue.replace("__VIEW__", viewLink);
                if (StringUtils.isNotEmpty(adm)) {
                    String target = "</script>";
                    // 找到最后一个匹配项的起始索引
                    int lastIndex = adm.lastIndexOf(target);
                    if (lastIndex != -1) { // 确保找到了匹配项
                        // 截取字符串，替换目标字符串
                        return adm.substring(0, lastIndex) + jsReplace + adm.substring(lastIndex + target.length());
                    } else {
                        log.warn("adm no /script>, {}.", adm);
                    }
                }
            } else if (type == 2) {
                if (StringUtils.isNotEmpty(adm)) {
                    String target = "<Impression>";
                    int lastIndex = adm.indexOf(target);
                    if (lastIndex != -1) { // 确保找到了匹配项
                        String vastInjectValue = "<Impression><![CDATA[__VIEW__]]></Impression>\n" +
                                "<Impression>";
                        String vastReplace = vastInjectValue.replace("__VIEW__", viewLink);
                        // 截取字符串，替换目标字符串
                        return adm.substring(0, lastIndex) + vastReplace + adm.substring(lastIndex + target.length());
                    }
                    target = "<InLine>";
                    // 找到最后一个匹配项的起始索引
                    lastIndex = adm.indexOf(target);
                    if (lastIndex != -1) { // 确保找到了匹配项
                        String vastInjectValue = "<InLine>\n" +
                                "<Impression><![CDATA[__VIEW__]]></Impression>";
                        String vastReplace = vastInjectValue.replace("__VIEW__", viewLink);
                        // 截取字符串，替换目标字符串
                        return adm.substring(0, lastIndex) + vastReplace + adm.substring(lastIndex + target.length());
                    }

                }
            } else if (type == 3) {
                JSONObject admJson = JSONObject.parseObject(adm);
                if (admJson != null) {
                    JSONObject aNative = admJson.getJSONObject("native");
                    if (aNative != null) {
                        JSONArray aImp = aNative.getJSONArray("imptrackers");
                        if (aImp != null) {
                            aImp.add(viewLink);
                            aNative.put("imptrackers", aImp);
                            admJson.put("native", aNative);
                            return JSONObject.toJSONString(admJson);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("InjectProvider injectView error: adm:{}, viewLink:{}, type:{}, error:{}", adm, viewLink, type, e.getMessage(), e);
            return null;
        }
        return null;
    }
}
