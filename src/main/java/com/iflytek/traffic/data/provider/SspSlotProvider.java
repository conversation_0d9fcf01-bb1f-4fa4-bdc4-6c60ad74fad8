package com.iflytek.traffic.data.provider;

import com.iflytek.traffic.data.entity.SspSlotInfo;
import com.iflytek.traffic.data.mapper.info.SspSlotMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @datetime 2025/6/17 17:01
 */
@Service
@Slf4j
public class SspSlotProvider {
	@Value("${ssp.id.list:}")
	private List<Integer> sspIdList;
	@Autowired
	private SspSlotMapper sspSlotMapper;

	/* key:ssp_id, value:key:ssp_tag*/
	private HashMap<Integer, Map<String, SspSlotInfo>> sspSlotMap = new HashMap<>();
	
	public SspSlotInfo getSspSlotInfoByTag(int sspId, String tagId) {
		if (sspSlotMap.containsKey(sspId)) {
			return sspSlotMap.get(sspId).get(tagId);
		} else {
			return null;
		}
	}

	@Scheduled(fixedRateString = "${data.update.interval.ssp.slot:300000}")
	private void update() {
		sspSlotMap.clear();
		if (sspIdList == null || sspIdList.isEmpty()) {
			return;
		}
		List<SspSlotInfo> sspSlotInfoList = sspSlotMapper.selectSspSlotInfoBySspId(sspIdList);

		HashMap<Integer, Map<String, SspSlotInfo>> tmp = new HashMap<>();
		sspSlotInfoList.forEach(info -> {
            tmp.computeIfAbsent(info.getSspId(), key -> new HashMap<>()).put(info.getSspTag(), info);
		});
		sspSlotMap = tmp;
		if (log.isDebugEnabled()) {
			log.debug("update sspSlotMap: {}", sspSlotMap);
		}
	}
}
