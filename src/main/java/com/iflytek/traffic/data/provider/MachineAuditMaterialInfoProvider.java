package com.iflytek.traffic.data.provider;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.benmanes.caffeine.cache.Scheduler;
import com.iflytek.traffic.data.entity.MachineAuditMaterialInfo;
import com.iflytek.traffic.data.entity.MachineAuditSspInfo;
import com.iflytek.traffic.data.mapper.info.MachineAuditSspInfoMapper;
import com.iflytek.traffic.redis.cluster.LettuceClusterMaterial;
import com.iflytek.traffic.ssp.SspEp;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.binder.cache.CaffeineCacheMetrics;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MachineAuditMaterialInfoProvider implements InitializingBean {

    private final MachineAuditSspInfoMapper machineAuditSspInfoMapper;

    private final LettuceClusterMaterial lettuceClient;

    @Value("${data.update.interval.machineAuditMaterialInfo.cache.maximumSize:100000}")
    private Long machineAuditMaterialInfoCacheMaximumSize;

    @Value("${data.update.interval.machineAuditMaterialInfo.cache.expireTime:60}")
    private Long machineAuditMaterialInfoCacheExpireTime;


    private volatile Map<String, MachineAuditSspInfo> machineAuditSspInfoMap = new HashMap<>();
    private static final MachineAuditMaterialInfo NULL_MACHINE_AUDIT_MATERIAL_INFO = new MachineAuditMaterialInfo();


    private LoadingCache<String, MachineAuditMaterialInfo> machineAuditMaterialInfoCache;

    @Override
    public void afterPropertiesSet() {
        machineAuditMaterialInfoCache = Caffeine.newBuilder()
                .expireAfterWrite(machineAuditMaterialInfoCacheExpireTime, TimeUnit.SECONDS)
                .scheduler(Scheduler.systemScheduler())
                .maximumSize(machineAuditMaterialInfoCacheMaximumSize)
                .recordStats()
                .build(this::loadMachineAuditMaterialInfo);

        CaffeineCacheMetrics.monitor(Metrics.globalRegistry, machineAuditMaterialInfoCache, "machine.audit.material");
    }

    private MachineAuditMaterialInfo loadMachineAuditMaterialInfo(String machinedHashId) {
        log.info("load MachineAuditMaterialInfo. machinedHashId = {}", machinedHashId);
        String tmp;
        try {
            tmp = lettuceClient.get("mm:" + machinedHashId);
        } catch (Exception e) {
            log.error("redis call error.", e);
            return null;
        }
        if (StringUtils.isEmpty(tmp)) {
            return NULL_MACHINE_AUDIT_MATERIAL_INFO;
        }

        try {
            return JSON.parseObject(tmp, MachineAuditMaterialInfo.class);
        } catch (Exception e) {
            log.error("load MachineAuditMaterialInfo. machinedHashId = {}", machinedHashId, e);
        }

        return NULL_MACHINE_AUDIT_MATERIAL_INFO;
    }

    @Scheduled(fixedRateString = "${data.update.interval.ssp.machineAuditSspInfo:300000}")
    public void fetchMachineAuditSspInfo() {
        List<MachineAuditSspInfo> machineAuditSspInfos = machineAuditSspInfoMapper.listEnabled();
        if (CollectionUtils.isEmpty(machineAuditSspInfos)) {
            log.info("machineAuditSspInfos is empty");
            machineAuditSspInfoMap = new HashMap<>();
            return;
        }
        HashMap<String, MachineAuditSspInfo> tmp = new HashMap<>();
        for (MachineAuditSspInfo machineAuditSspInfo : machineAuditSspInfos) {
            String key = machineAuditSspInfo.getRegion() + ":" + machineAuditSspInfo.getSspId() + ":" + machineAuditSspInfo.getSspEpId();
            tmp.put(key, machineAuditSspInfo);
        }
        log.info("machineAuditMaterialInfo map size:{}", tmp.size());
        machineAuditSspInfoMap = tmp;
    }


    public MachineAuditSspInfo getMachineAuditMaterialInfo(String region, SspEp sspEp) {
        MachineAuditSspInfo machineAuditSspInfo = machineAuditSspInfoMap.get(region + ":" + sspEp.getSspId() + ":" + sspEp.getSspEpId());
        if (machineAuditSspInfo != null) {
            return machineAuditSspInfo;
        }

        machineAuditSspInfo = machineAuditSspInfoMap.get(region + ":" + sspEp.getSspId() + ":0");
        if (machineAuditSspInfo != null) {
            return machineAuditSspInfo;
        }

        machineAuditSspInfo = machineAuditSspInfoMap.get("0:" + sspEp.getSspId() + ":" + sspEp.getSspEpId());
        if (machineAuditSspInfo != null) {
            return machineAuditSspInfo;
        }

        return machineAuditSspInfoMap.get("0:" + sspEp.getSspId() + ":0");
    }

    public MachineAuditMaterialInfo getMachineAuditMaterialInfo(String machinedHashId) {
        MachineAuditMaterialInfo machineAuditMaterialInfo = machineAuditMaterialInfoCache.get(machinedHashId);
        if (machineAuditMaterialInfo == NULL_MACHINE_AUDIT_MATERIAL_INFO) {
            return null;
        }
        return machineAuditMaterialInfo;
    }
}
