package com.iflytek.traffic.data.provider;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.iflytek.traffic.data.entity.*;
import com.iflytek.traffic.data.mapper.info.SupportMapper;
import com.iflytek.traffic.search.index.DspEpSupportIndex;
import com.iflytek.traffic.util.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SupportProvider {

    @Autowired
    private SupportMapper supportMapper;

    @Autowired
    private DspEpSupportIndex dspEpSupportIndex;


    @Scheduled(fixedRateString = "${data.update.interval.dsp:300000}")
    public void update() {
        updateSupport();
    }

    public void updateSupport() {
        List<EpSupportInfo> epSupportInfoList = supportMapper.selectValidSupport();
        Map<Integer, List<EpSupportInfo>> id2InfoMap = epSupportInfoList.stream().collect(Collectors.groupingBy(EpSupportInfo::getId));
        Map<Integer, EpSupport> epSupportMap = new HashMap<>();
        id2InfoMap.forEach((id, supportInfoList) -> {
            EpSupport epSupport = new EpSupport();
            epSupport.setId(id);
            EpSupportInfo esi = supportInfoList.get(0);
            epSupport.setSupportId(esi.getSupportId());
            epSupport.setDspEpId(esi.getEpId());
            List<EpSupportItem> supportItemList = new ArrayList<>();
            supportInfoList.forEach(s -> {
                if (StrUtil.isBlank(s.getConfigValue())
                        || s.getConfigValue().equals("{}")
                        || s.getConfigValue().equals("[]")) {
                    log.debug("support {} config {} value is empty, skip", id, s.getConfigId());
                    return;
                }
                EpSupportItem item = new EpSupportItem();
                item.setConfigId(s.getConfigId());
                item.setInclude(s.getInclude());
                //包名, DisplayManager、尺寸特殊处理
                if (s.getConfigId() == 1003 || s.getConfigId() == 1007 || s.getConfigId() == 1010) {
                    List<String> configItemList = Arrays.asList(s.getConfigValue().split("\n"));
                    item.setConfigItemList(configItemList);
                } else if (s.getConfigId() == 1008) {
                    // supplier chain
                    SupplierChainDirect direct = JSONObject.parseObject(s.getConfigValue(), SupplierChainDirect.class);
                    direct.setInclude(s.getInclude());
                    item.setSupplierChainDirect(direct);
                } else if (s.getConfigId() == 1011) {
                    // 底价
                    List<FloorPriceDirect> fpDirect = JSONArray.parseArray(s.getConfigValue(), FloorPriceDirect.class);
                    item.setFpDirects(fpDirect);
                } else if (s.getConfigId() == 1012) {
                    // 二级广告位形式
                    if (StrUtil.isNotBlank(s.getConfigValue())) {
                        List<String> type = new ArrayList<>();
                        List<String> values = JSON.parseArray(s.getConfigValue(), String.class);
                        for (String value : values) {
                            Map<String, String> config = JSON.parseObject(value, new TypeReference<Map<String, String>>() {
                            });
                            if (config.containsKey("subSlotType")) {
                                List<String> subSlotType = JSON.parseArray(config.get("subSlotType"), String.class);
                                if (CollUtil.isNotEmpty(subSlotType)) {
                                    type.addAll(subSlotType);
                                }
                            }
                        }
                        item.setConfigItemList(type);
                    }
                } else if (s.getConfigId() == 1013) {
                    // 插屏
                    List<Integer> instls = JSON.parseArray(s.getConfigValue(), Integer.class);
                    for (Integer inst : instls) {
                        if (inst == Constants.DSP_EP_SUPPORT_INSTL) {
                            item.setConfigItemList(List.of("1"));
                        } else if (inst == Constants.DSP_EP_SUPPORT_NOT_INSTL) {
                            item.setConfigItemList(List.of("0"));
                        }
                    }
                }
                else{
                    List<String> configItemList = JSONArray.parseArray(s.getConfigValue(), String.class);
                    item.setConfigItemList(configItemList);
                }
                supportItemList.add(item);
            });
            epSupport.setSupportItemList(supportItemList);
            epSupportMap.put(id, epSupport);
        });
        // TODO 加入索引
        dspEpSupportIndex.fresh(epSupportMap);
    }

}
