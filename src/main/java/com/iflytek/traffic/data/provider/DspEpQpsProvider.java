package com.iflytek.traffic.data.provider;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.traffic.data.entity.DspEpQps;
import com.iflytek.traffic.data.mapper.info.DspEpQpsMapper;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.session.request.Impression;
import com.iflytek.traffic.session.request.UnifiedRequest;
import com.iflytek.traffic.ssp.SspEp;
import com.iflytek.traffic.util.SpringContextHelper;
import com.iflytek.traffic.util.qps.DimensionalQpsMonitorCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DspEpQpsProvider {

    @Autowired
    private DspEpQpsMapper dspEpQpsMapper;

    private DimensionalQpsMonitorCache qpsCache = new DimensionalQpsMonitorCache();

    private DimensionalQpsMonitorCache dspEpReqCache = new DimensionalQpsMonitorCache();

    private DimensionalQpsMonitorCache dspEpBidCache = new DimensionalQpsMonitorCache();

    private volatile Map<Integer, DspEpQps> dspEpQpsMap = new HashMap<>();

    private Map<String, DspEpQps.KeyAndQps<String, Integer>> dspEpKeyAndQpsMap = new ConcurrentHashMap<>();

    @Scheduled(fixedRateString = "${data.update.interval.dspEp-qps:300000}")
    public void update() {
        updateDspEpQps();
    }

    private void updateDspEpQps() {

        List<DspEpQps.DspEpQpsConfig> dspEpQpsConfigs = dspEpQpsMapper.selectValidConfig();
        if (CollUtil.isEmpty(dspEpQpsConfigs)) {
            log.error("update with empty dsp ep qps config");
            return;
        }
        log.info("update dsp ep qps config, size: {}", dspEpQpsConfigs.size());
        Map<Integer, List<DspEpQps.DspEpQpsConfig>> groupedConfigs = dspEpQpsConfigs.stream()
                .collect(Collectors.groupingBy(DspEpQps.DspEpQpsConfig::getDspEpId));
        Map<Integer, DspEpQps> temp = new HashMap<>();
        groupedConfigs.forEach((k, v) -> {
            DspEpQps dspEpQps = temp.computeIfAbsent(k, id -> new DspEpQps());
            dspEpQps.setDspEpId(k);
            v.forEach(config -> {
                DspEpQps.SingleQpsConfig singleQpsConfig = dspEpQps.getSingleQpsConfigMap()
                        .computeIfAbsent(config.getConfigId(), id -> new DspEpQps.SingleQpsConfig());
                singleQpsConfig.setConfigId(config.getConfigId());
                singleQpsConfig.getValue2Ratio().put(config.getConfigVal(), config.getRatio());
                singleQpsConfig.setTotalRatio(singleQpsConfig.getTotalRatio() + config.getRatio());
            });
        });
        dspEpQpsMap = temp;
    }

    public DspEpQps.KeyAndQps<String, Integer> getDspEpQpsByReq(SspEp sspEp, DspEpObj dspEp, UnifiedRequest request) {
        // 按配置的维度叉乘后，进行最细粒度的QPS控制
        List<DspEpQps.KeyAndRatio<String, Double>> keyAndRatios = getQpsKeyAndRatio(sspEp, dspEp.getDspEpId(), request);
        if (CollUtil.isEmpty(keyAndRatios)) {
            return null;
        }
        // 读取最新QPS配置
        StringBuilder key = new StringBuilder("DSPEP_" + dspEp.getDspEpId() + "_QPS");
        Integer qps = dspEp.getQps();
        for (DspEpQps.KeyAndRatio<String, Double> kr : keyAndRatios) {
            key.append("_").append(kr.key());
            qps = (int) Math.ceil(qps * kr.ratio().intValue() / 100.0d);
        }
        if (dspEpKeyAndQpsMap.containsKey(key.toString())) {
            return dspEpKeyAndQpsMap.get(key.toString());
        }
        return new DspEpQps.KeyAndQps<>(key.toString(), qps);
    }

    private List<DspEpQps.KeyAndRatio<String, Double>> getQpsKeyAndRatio(SspEp sspEp, Integer dspEpId, UnifiedRequest request) {
        DspEpQps dspEpQps = dspEpQpsMap.get(dspEpId);
        if (dspEpQps == null || CollUtil.isEmpty(dspEpQps.getSingleQpsConfigMap())) {
            log.info("dsp ep {} qps config not found.", dspEpId);
            return null;
        }
        Integer sspId = sspEp.getSspId();
        String bundle = request.getApp() != null && StrUtil.isNotBlank(request.getApp().getBundle()) ? request.getApp().getBundle() : null;
        Long region = request.getDevice() != null && request.getDevice().getRegionInfo() != null ? request.getDevice().getRegionInfo().getRegion() : null;
        Impression imp = request.getImps().values().stream().filter(Objects::nonNull).findAny().orElse(null);
        Integer adType = imp != null ? imp.getAdType().getValue() : null;
        return dspEpQps.genQpsConfigKeyAndRatio(sspId, adType, region, bundle);
    }

    @Scheduled(initialDelayString = "${data.refresh.interval.dspEp-qps:30000}", fixedRateString = "${data.refresh.interval.dspEp-qps:30000}")
    public void refreshDspEpQps() {
        // 计算DSP EP的QPS
        calDspEpQps();
    }
    /**
     * 计算DSP EP的QPS
     */
    private void calDspEpQps() {
        log.info("start cal dsp ep qps.");
        if (MapUtil.isEmpty(dspEpQpsMap)) {
            return;
        }
        // 按DSP EP 逐个刷新新的QPS
        DspDataProvider dspDataProvider = SpringContextHelper.getBean(DspDataProvider.class);
        for (Map.Entry<Integer, DspEpQps> entry : dspEpQpsMap.entrySet()) {
            Integer dspEpId = entry.getKey();
            DspEpQps dspEpQps = entry.getValue();
            if (CollUtil.isEmpty(dspEpQps.getSingleQpsConfigMap())) {
                continue;
            }
            Integer totalQps = dspDataProvider.getDspEpQps(dspEpId);
            if (totalQps == null || totalQps <= 0) {
                log.warn("dsp ep {} no total qps limit.", dspEpId);
                continue;
            }
            // 计算每个DSP EP配置的QPS
            List<DspEpQps.KeyAndRatio<String, Double>> keyAndRatios = dspEpQps.genAllConfigQpsKeyAndRatio();
            if (CollUtil.isEmpty(keyAndRatios)) {
                continue;
            }
            List<QPS<Double, String>> qpsData = new ArrayList<>();
            double gap = 0.0;
            for (DspEpQps.KeyAndRatio<String, Double> kr : keyAndRatios) {
                Double sspQps = qpsCache.getQps(kr.key());
                Double reqQps = dspEpReqCache.getQps(kr.key());
                Double bid = dspEpBidCache.getQps(kr.key());
                QPS<Double, String> qps = null;
                if (reqQps != 0.0) {
                    qps = new QPS<Double, String>(kr.key(), sspQps, reqQps, bid / reqQps);
                } else {
                    qps = new QPS<Double, String>(kr.key(), sspQps, reqQps, 0.0);
                }
                log.info("cal dsp ep {} qps, key: {}, qps ratio {}, sspQps {}, reqQps {}, bid {}, bidRatio: {}.",
                        dspEpId, kr.key(), kr.ratio(), sspQps, reqQps, bid, qps.bidRatio);
                qpsData.add(qps);
                if (reqQps < totalQps * kr.ratio()) {
                    double krQpsGap = totalQps * kr.ratio() - reqQps;
                    log.info("cal dsp ep {} qps, key: {} not enough, qps ratio {}, sspQps: {}, reqQps: {}, bid ratio: {}, qps gap: {}",
                            dspEpId, kr.key(), kr.ratio(), sspQps, reqQps, qps.bidRatio, krQpsGap);
                    gap += krQpsGap;
                } else {
                    log.info("cal dsp ep {} qps, key: {} enough, qps ratio {}, sspQps: {}, reqQps: {}, bid ratio: {}",
                            dspEpId, kr.key(), kr.ratio(), sspQps, reqQps, qps.bidRatio);
                    // 先删除缓存
                    String[] kp = qps.key().split("\\|");
                    StringBuilder qpsCtrlKey = new StringBuilder("DSPEP_" + dspEpId + "_QPS");
                    for (String param : kp) {
                        qpsCtrlKey.append("_").append(param);
                    }
                    dspEpKeyAndQpsMap.remove(qpsCtrlKey.toString());
                }
            }
            // 按bidRatio排序

            Collections.sort(qpsData, new Comparator<QPS<Double, String>>() {
                @Override
                public int compare(QPS<Double, String> o1, QPS<Double, String> o2) {
                    return Double.compare(o2.bidRatio(), o1.bidRatio()); // 降序排序
                }
            });
            if (gap > 0.0) {
                // 如果有gap，说明当前QPS没有达到配置的QPS，需要进行调整
                // 调整规则：按参竞率从高到低分配gap值，分配额= sspQps - reqQps
                log.info("dsp ep {} total qps gap: {}, start adjust.", dspEpId, gap);
                for (QPS<Double, String> qps : qpsData) {
                    if (qps.sspQps() - qps.reqQps() > 0.0) {
                        double adjust = Math.min(gap, qps.sspQps() - qps.reqQps());
                        if (adjust > 0.0) {
                            int newQps = (int) Math.ceil(qps.reqQps() + adjust);
                            log.info("cal dsp ep {} qps, adjust key: {}, sspQps: {}, reqQps: {}, bid ratio: {}, adjust: {}",
                                    dspEpId, qps.key(), qps.sspQps(), qps.reqQps(), qps.bidRatio(), adjust);
                            // 更新缓存
                            String[] kp = qps.key().split("\\|");
                            StringBuilder qpsCtrlKey = new StringBuilder("DSPEP_" + dspEpId + "_QPS");
                            for (String param : kp) {
                                qpsCtrlKey.append("_").append(param);
                            }
                            dspEpKeyAndQpsMap.put(qpsCtrlKey.toString(), new DspEpQps.KeyAndQps<>(qpsCtrlKey.toString(), newQps));
                            gap -= adjust;
                        }
                    }
                    if (gap <= 0.0) {
                        break; // gap已分配完毕
                    }
                }
            }

        }
    }

    /**
     * QPS记录
     * @param key 控制单元key
     * @param sspQps ssp侧符合要求的QPS
     * @param reqQps 实际请求DSP的QPS
     * @param bidRatio 参竞率
     */
    record QPS<Double, String>(String key, Double sspQps, Double reqQps, Double bidRatio) {}

    private Set<String> genRecordKey(SspEp sspEp, UnifiedRequest request, Collection<Integer> dspEpIds) {
        if (CollUtil.isEmpty(dspEpIds)) {
            return null;
        }
        Set<String> recordKey = new HashSet<>();
        for (Integer dspEpId : dspEpIds) {
            List<DspEpQps.KeyAndRatio<String, Double>> keyAndRatios = getQpsKeyAndRatio(sspEp, dspEpId, request);
            if (CollUtil.isEmpty(keyAndRatios)) {
                continue;
            }
            String key = keyAndRatios.stream().map(DspEpQps.KeyAndRatio::key).collect(Collectors.joining("|"));
            recordKey.add(key);
        }
        if (CollUtil.isEmpty(recordKey)) {
            return null;
        }
        return recordKey;
    }

    public void recordAfterIndexDspEpQps(SspEp sspEp, UnifiedRequest request, Collection<Integer> dspEpIds) {
        Set<String> recordKey = genRecordKey(sspEp, request, dspEpIds);
        if (recordKey == null) return;
        for (String key : recordKey) {
            qpsCache.recordRequest(key);
        }
    }

    public void recordDspEpReqQps(SspEp sspEp, UnifiedRequest request, Collection<Integer> dspEpIds) {
        Set<String> recordKey = genRecordKey(sspEp, request, dspEpIds);
        if (recordKey == null) return;
        for (String key : recordKey) {
            dspEpReqCache.recordRequest(key);
        }
    }

    public void recordDspEpBidQps(SspEp sspEp, UnifiedRequest request, Collection<Integer> dspEpIds) {
        Set<String> recordKey = genRecordKey(sspEp, request, dspEpIds);
        if (recordKey == null) return;
        for (String key : recordKey) {
            dspEpBidCache.recordRequest(key);
        }
    }

    public Map<String, Double> getSspQps() {
        return qpsCache.getAllQps();
    }

    public Map<String, Double> getDspEpReqQps() {
        return dspEpReqCache.getAllQps();
    }

    public Map<String, Double> getDspEpBidQps() {
        return dspEpBidCache.getAllQps();
    }

    public Map<String, Integer> getDspEpCtrlQps() {
        Map<String, Integer> result = new HashMap<>();
        dspEpKeyAndQpsMap.forEach((k, v) -> result.put(k, v.qps()));
        return result;
    }

}
