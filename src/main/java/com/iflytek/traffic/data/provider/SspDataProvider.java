package com.iflytek.traffic.data.provider;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.traffic.data.entity.SspEpInfo;
import com.iflytek.traffic.data.entity.SspIncludeInfo;
import com.iflytek.traffic.data.mapper.info.SspMapper;
import com.iflytek.traffic.ssp.SspEp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SspDataProvider {

    @Autowired
    private SspMapper sspMapper;

    public Map<String, SspEpInfo> sspEpInfoMap = new HashMap<>();

    public Map<Integer, SspIncludeInfo> sspIncludeInfoMap = new HashMap<>();

    @Scheduled(fixedRateString = "${data.update.interval.ssp:300000}")
    public void update(){
        updateSspEpInfo();
    }

    /**
     * 定时刷新ep信息
     */
    public void updateSspEpInfo(){
        List<SspEpInfo> sspEpInfoList = sspMapper.selectValidSspEp();
        if (CollectionUtil.isEmpty(sspEpInfoList)) {
            log.error("update with empty ssp ep infos");
            return;
        }
        Map<String, SspEpInfo> tmp = sspEpInfoList.stream().filter(i -> StrUtil.isNotBlank(i.getPath())).collect(Collectors.toMap(SspEpInfo::getPath, Function.identity()));
        sspEpInfoMap = tmp;
        // ssp 包名&创意黑白名单
        List<SspIncludeInfo> sspIncludeInfos = sspMapper.selectSspIncludeInfo();
        if (CollUtil.isEmpty(sspIncludeInfos)) {
            log.info("empty ssp include app & crid.");
            return;
        }
        Map<Integer, SspIncludeInfo> tempSspInclude = new HashMap<>();
        for (SspIncludeInfo s : sspIncludeInfos) {
            if (s.getAppInclude().equals(0) && s.getCridInclude().equals(0)) {
                continue;
            }
            tempSspInclude.put(s.getSspId(), s);
        }
        sspIncludeInfoMap = tempSspInclude;
    }


    public SspEp getSspEpByPath(String path) {
        if (sspEpInfoMap.containsKey(path)) {
            SspEpInfo info = sspEpInfoMap.get(path);
            SspEp sspEp = new SspEp();
            sspEp.setSspId(info.getSspId());
            sspEp.setSspEpId(info.getId());
            sspEp.setSspName(info.getSspName());
            sspEp.setPath(info.getPath());
            sspEp.setImpTtl(info.getMaterialHour());
            sspEp.setQps(info.getQps().intValue());
            sspEp.setSettlementType(info.getSettlementType());
            sspEp.setProtocol(info.getProtocol());
            sspEp.setIsGzip(info.getIsGzip());
            return sspEp;
        }
        return null;
    }

    public boolean isAppInclude(Integer sspId, String pkg) {
        if (!sspIncludeInfoMap.containsKey(sspId)) {
            return true;
        }
        SspIncludeInfo sspIncludeInfo = sspIncludeInfoMap.get(sspId);
        // 黑名单
        if (sspIncludeInfo.getAppInclude().equals(2) && StrUtil.isNotBlank(sspIncludeInfo.getIncludeApp())) {
            if (StrUtil.isBlank(pkg) || sspIncludeInfo.getIncludeApp().contains(pkg)) {
                return false;
            }
        }
        // 白名单
        if (sspIncludeInfo.getAppInclude().equals(1) && StrUtil.isNotBlank(sspIncludeInfo.getIncludeApp())) {
            if (StrUtil.isBlank(pkg) && sspIncludeInfo.getIncludeApp().contains(pkg)) {
                return true;
            }
        }
        return false;
    }

    public boolean isCridInclude(Integer sspId, String crid) {
        if (sspIncludeInfoMap.containsKey(sspId)) {
            return true;
        }
        SspIncludeInfo sspIncludeInfo = sspIncludeInfoMap.get(sspId);
        // 黑名单
        if (sspIncludeInfo.getCridInclude().equals(2) && StrUtil.isNotBlank(sspIncludeInfo.getIncludeCrid())
                && sspIncludeInfo.getIncludeCrid().contains(crid)) {
            return false;
        }
        // 白名单
        if (sspIncludeInfo.getCridInclude().equals(1) && StrUtil.isNotBlank(sspIncludeInfo.getIncludeCrid())
                && sspIncludeInfo.getIncludeCrid().contains(crid)) {
            return true;
        }
        return false;
    }
}
