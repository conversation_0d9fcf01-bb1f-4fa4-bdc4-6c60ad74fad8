package com.iflytek.traffic.data.provider;

import cn.hutool.core.collection.CollUtil;
import com.iflytek.traffic.data.entity.AppCategoryResource;
import com.iflytek.traffic.data.mapper.info.AppCategoryResourceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AppCategoryProvider {

    private static volatile Map<String, Integer> pkg2Category = new HashMap<>();

    @Autowired
    private AppCategoryResourceMapper appCategoryResourceMapper;

    @Scheduled(fixedRateString = "${data.update.interval.app-category:300000}")
    public void update() {
        updateAppCategory();
    }

    private void updateAppCategory() {
        List<AppCategoryResource> appCategoryResourceList = appCategoryResourceMapper.selectValidAppCategoryResource();
        if (CollUtil.isEmpty(appCategoryResourceList)) {
            log.error("update with empty app category");
            return;
        }
        log.info("update app category, size: {}", appCategoryResourceList.size());
        Map<String, Integer> tmp = appCategoryResourceList.stream()
                .collect(Collectors.toMap(AppCategoryResource::getAdxAppPkg, AppCategoryResource::getCategoryId, (o1, o2) -> o2));
        pkg2Category = tmp;
    }

    public Integer getPkgCategory(String pkg) {
        return pkg2Category.get(pkg);
    }
}
