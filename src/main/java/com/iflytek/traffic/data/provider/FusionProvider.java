package com.iflytek.traffic.data.provider;

import com.alibaba.fastjson.JSON;
import com.iflytek.traffic.data.entity.AppFusionInfo;
import com.iflytek.traffic.data.entity.SlotFusionInfo;
import com.iflytek.traffic.data.mapper.info.FusionMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;

@Service
@Slf4j
public class FusionProvider {

    @Autowired
    private FusionMapper fusionMapper;

    //应用映射，key为sspid_pkg_dspid的格式
    private HashMap<String, AppFusion> sspPkg2AppFusion = new HashMap<>();

    //广告位映射，key为sspid_srcslottag_dspid的格式
    private HashMap<String, SlotFusionInfo> sspSlot2SlotFusion = new HashMap<>();

    @Scheduled(fixedRateString = "${data.update.interval.fusion:300000}")
    public void update() {
        //加载app fusion信息
        List<AppFusionInfo> appFusionInfoList = fusionMapper.selectAll();
        if (!CollectionUtils.isEmpty(appFusionInfoList)) {
            HashMap<String, AppFusion> tmpSspPkg2AppFusion = new HashMap<>();
            for (AppFusionInfo appFusionInfo : appFusionInfoList) {
                String key = appFusionInfo.getSspId() + "_" + appFusionInfo.getSrcPkg() + "_" + appFusionInfo.getDspId();
                try {
                    HashMap<String,String> adxTags = JSON.parseObject(appFusionInfo.getAdxTag(), HashMap.class);
                    AppFusion appFusion = new AppFusion();
                    appFusion.sspId = appFusionInfo.getSspId();
                    appFusion.dspId = appFusionInfo.getDspId();
                    appFusion.srcPkg = appFusionInfo.getSrcPkg();
                    appFusion.adxApp = appFusionInfo.getAdxApp();
                    appFusion.tarPkg = appFusionInfo.getTarPkg();
                    appFusion.adType2Slot = adxTags;
                    tmpSspPkg2AppFusion.put(key, appFusion);
                } catch (Exception e) {
                    log.error("skip adxTag:{} parse error", appFusionInfo.getAdxTag(), e);
                }
            }
            sspPkg2AppFusion = tmpSspPkg2AppFusion;
        }

        //加载广告位fusion信息
        List<SlotFusionInfo> slotFusionInfoList = fusionMapper.selectAllSlot();
        if (!CollectionUtils.isEmpty(slotFusionInfoList)) {
            HashMap<String, SlotFusionInfo> tmpSspSlot2SlotFusion = new HashMap<>();
            for (SlotFusionInfo slotFusionInfo : slotFusionInfoList) {
                String key = slotFusionInfo.getSspId() + "_" + slotFusionInfo.getSspSlotTag() + "_" + slotFusionInfo.getDspId();
                tmpSspSlot2SlotFusion.put(key, slotFusionInfo);
            }
            sspSlot2SlotFusion = tmpSspSlot2SlotFusion;
        }

        if (log.isDebugEnabled()) {
            log.debug("sspPkg2AppFusion:{}", JSON.toJSONString(sspPkg2AppFusion));
            log.debug("sspSlot2SlotFusion:{}", JSON.toJSONString(sspSlot2SlotFusion));
        }
    }

    public TargetTraffic queryAppFusion(Integer sspId, Integer dspId, String srcPkg, String srcTag, Integer adType) {
        String key1 = sspId + "_" + srcPkg + "_" + dspId;
        if (sspPkg2AppFusion.containsKey(key1)) {
            AppFusion appFusion = sspPkg2AppFusion.get(key1);
            if (appFusion.adType2Slot.containsKey(String.valueOf(adType))) {
                TargetTraffic targetTraffic = new TargetTraffic();
                targetTraffic.setType(1);
                targetTraffic.setTarPkg(appFusion.tarPkg);
                targetTraffic.setTarAppId(appFusion.adxApp);
                targetTraffic.setTarTagId(appFusion.adType2Slot.get(String.valueOf(adType)));
                return targetTraffic;
            } else {
                log.error("no adType content, sspId:{}, dspId:{}, srcPkd:{}, srcTag:{}, adType:{}",
                        sspId, dspId, srcPkg, srcTag, adType);
                return null;
            }

        }

        String key2 = sspId + "_" + srcTag + "_" + dspId;
        if (sspSlot2SlotFusion.containsKey(key2)) {
            SlotFusionInfo slotFusionInfo = sspSlot2SlotFusion.get(key2);
            TargetTraffic targetTraffic = new TargetTraffic();
            targetTraffic.setType(2);
            targetTraffic.setTarTagId(slotFusionInfo.getTarSlotTag());
            targetTraffic.setTarPkg(slotFusionInfo.getTarPkg());
            targetTraffic.setTarAppId(slotFusionInfo.getTarAppTag());
            return targetTraffic;
        }
        return null;
    }

    @Data
    public static class AppFusion {
        private Integer sspId;
        private Integer dspId;
        private String srcPkg;
        private String adxApp;
        private String tarPkg;

        private HashMap<String, String> adType2Slot;
    }

    @Data
    public static class TargetTraffic {
        // 1: app, 2: slot
        private Integer type;
        private String tarPkg;
        private String tarTagId;
        private String tarAppId;
    }
}
