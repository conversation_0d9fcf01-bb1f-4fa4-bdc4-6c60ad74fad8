package com.iflytek.traffic.data.provider;

import com.iflytek.traffic.data.entity.DspProfitInfo;
import com.iflytek.traffic.data.entity.SspProfitInfo;
import com.iflytek.traffic.data.mapper.info.ProfitMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

@Service
@Slf4j
public class ProfitProvider {

    @Autowired
    private ProfitMapper profitMapper;

    private HashMap<Integer, Float> dspProfitMap = new HashMap<>();

    private HashMap<String, Float> dspEpProfitMap = new HashMap<>();

    private HashMap<Integer, Float> sspProfitMap = new HashMap<>();

    private HashMap<String, Float> sspEpProfitMap = new HashMap<>();

    private HashMap<Integer, Float> lockDspProfitMap = new HashMap<>();

    private HashMap<String, Float> lockDspEpProfitMap = new HashMap<>();

    @Scheduled(fixedRateString = "${data.update.interval.profit:300000}")
    public void update() {
        List<DspProfitInfo> dspProfitInfos = profitMapper.selectAllDspProfit();
        List<SspProfitInfo> sspProfitInfos = profitMapper.selectAllSspProfit();

        HashMap<Integer, Float> tmpDspProfitMap = new HashMap<>();
        HashMap<String, Float> tmpDspEpProfitMap = new HashMap<>();
        HashMap<Integer, Float> tmpSspProfitMap = new HashMap<>();
        HashMap<String, Float> tmpSspEpProfitMap = new HashMap<>();
        HashMap<Integer, Float> tmpLockDspProfitMap = new HashMap<>();
        HashMap<String, Float> tmpLockDspEpProfitMap = new HashMap<>();

        for (DspProfitInfo p : dspProfitInfos) {
            if (p.getDspEpId() == 0) {
                if (p.getIsOnlyEffect() == 1) {
                    tmpLockDspProfitMap.put(p.getDspId(), p.getChargeRatio()/100.0f);
                }
                tmpDspProfitMap.put(p.getDspId(), p.getChargeRatio()/100.0f);
            } else {
                String key = p.getDspId()+"_"+p.getDspEpId();
                if (p.getIsOnlyEffect() == 1) {
                    tmpLockDspEpProfitMap.put(key, p.getChargeRatio()/100.0f);
                }
                tmpDspEpProfitMap.put(key, p.getChargeRatio()/100.0f);
            }
        }

        for (SspProfitInfo p : sspProfitInfos) {
            if (p.getSspEpId() == 0) {
                tmpSspProfitMap.put(p.getSspId(), p.getChargeRatio()/100.0f);
            } else {
                String key = p.getSspId() + "_" + p.getSspEpId();
                tmpSspEpProfitMap.put(key, p.getChargeRatio()/100.0f);
            }
        }

        lockDspEpProfitMap = tmpLockDspEpProfitMap;
        lockDspProfitMap = tmpLockDspProfitMap;
        dspProfitMap = tmpDspProfitMap;
        dspEpProfitMap = tmpDspEpProfitMap;
        sspProfitMap = tmpSspProfitMap;
        sspEpProfitMap = tmpSspEpProfitMap;
        if (log.isDebugEnabled()) {
            log.debug("profit update lockDspEpProfitMap: {}", lockDspEpProfitMap);
            log.debug("profit update lockDspProfitMap: {}", lockDspProfitMap);
            log.debug("profit update dspProfitMap: {}", dspProfitMap);
            log.debug("profit update dspEpProfitMap: {}", dspEpProfitMap);
            log.debug("profit update sspProfitMap: {}", sspProfitMap);
            log.debug("profit update sspEpProfitMap: {}", sspEpProfitMap);
            log.debug("profit update ------------------------------------------------");
        }
    }

    public Float getProfitRatio(Integer dspId, Integer dspEpId, String adxSlotId, String adxAppId, Integer sspId, Integer sspEpId) throws Exception {

        String key1 = dspId + "_" + dspEpId;

        if (lockDspEpProfitMap.containsKey(key1)) {
            return lockDspEpProfitMap.get(key1);
        }

        if (lockDspProfitMap.containsKey(dspId)) {
            return lockDspProfitMap.get(dspId);
        }

        String key2 = sspId + "_" + sspEpId;

        Float a,b;

        if (dspEpProfitMap.containsKey(key1)) {
            a = dspEpProfitMap.get(key1);
        } else a = dspProfitMap.getOrDefault(dspId, 0.0f);

        if (sspEpProfitMap.containsKey(key2)) {
            b = sspEpProfitMap.get(key2);
        } else b = sspProfitMap.getOrDefault(sspId, 0.0f);

        return a+b;
    }
}
