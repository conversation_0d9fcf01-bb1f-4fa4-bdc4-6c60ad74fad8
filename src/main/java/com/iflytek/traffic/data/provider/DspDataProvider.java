package com.iflytek.traffic.data.provider;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.traffic.data.entity.DspEpInfo;
import com.iflytek.traffic.data.entity.DspEpLimited;
import com.iflytek.traffic.data.mapper.info.DspMapper;
import com.iflytek.traffic.dsp.DspEpObj;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DspDataProvider {


    @Autowired
    private DspMapper dspMapper;

    public Map<Integer, DspEpInfo> dspEpInfoMap = new HashMap<>();

    public Map<Integer, Integer> dspEpRpmQpsHour = new HashMap<>();


    @Scheduled(fixedRateString = "${data.update.interval.dsp:300000}")
    public void update() {
        updateDspEpInfo();
        updateRpmQps();
    }

    /**
     * 定时刷新ep信息
     */
    private void updateDspEpInfo() {
        List<DspEpInfo> dspEpInfoList = dspMapper.selectValidEp();
        if (CollectionUtil.isEmpty(dspEpInfoList)) {
            log.error("update with empty dsp ep infos");
            return;
        }
        log.info("update dsp ep info, size: {}", dspEpInfoList.size());
        Iterator<DspEpInfo> iterator = dspEpInfoList.iterator();
        while (iterator.hasNext()) {
            DspEpInfo dspEpInfo = iterator.next();
            if (StrUtil.isBlank(dspEpInfo.getPrefix()) || dspEpInfo.getPrefix().equals("unknown")) {
                log.warn("dsp ep path is blank or path is unknown: {}", dspEpInfo.getId());
                iterator.remove();
            }
            if (StrUtil.isBlank(dspEpInfo.getProtocol())) {
                log.warn("dsp ep protocol is blank: {}", dspEpInfo.getId());
                iterator.remove();
            }
            if (StrUtil.isBlank(dspEpInfo.getPath())) {
                log.warn("dsp ep path is blank: {}", dspEpInfo.getId());
                iterator.remove();
            }
        }
        Map<Integer, DspEpInfo> tmp = dspEpInfoList.stream().collect(Collectors.toMap(DspEpInfo::getId, Function.identity()));
        dspEpInfoMap = tmp;
    }

    private void updateRpmQps() {
        List<DspEpLimited> dspEpLimiteds = dspMapper.selectDspEpRpmQps();
        if (CollectionUtil.isEmpty(dspEpLimiteds)) {
            log.info("update with empty dsp ep limited.");
            return;
        }
        Map<Integer, Integer> tmp = dspEpLimiteds.stream().collect(Collectors.toMap(DspEpLimited::getEpId, DspEpLimited::getHourQps));
        dspEpRpmQpsHour = tmp;
    }

    public DspEpObj getDspEpObj(Integer dspEpId) {
        if (dspEpId != null && dspEpInfoMap.containsKey(dspEpId)) {
            DspEpObj dspEpObj = new DspEpObj();
            DspEpInfo info = dspEpInfoMap.get(dspEpId);
            dspEpObj.setDspId(info.getDspId());
            dspEpObj.setDspEpId(info.getId());
            dspEpObj.setQps(determineQps(info));
            dspEpObj.setPath(info.getPath());
            dspEpObj.setPrefix(info.getPrefix());
            dspEpObj.setName(info.getDspName());
            dspEpObj.setTimeout(info.getTimeout());
            dspEpObj.setIsGzip(info.getIsGzip());
            dspEpObj.setSettlementType(info.getSettlementType());
            dspEpObj.setProtocol(info.getProtocol());
            dspEpObj.setImpTtl(info.getDspImpTimeout());
            if (info.getImpTimeout() != null && info.getImpTimeout() > 0) {
                dspEpObj.setImpTtl(info.getImpTimeout());
            }
            return dspEpObj;
        }
        return null;
    }

    public List<Integer> diffDspEpId(Set<Integer> dspEpId) {
        if (CollUtil.isEmpty(dspEpId)) {
            return CollUtil.newArrayList(dspEpInfoMap.keySet());
        }
        List<Integer> res = CollUtil.newArrayList();
        for (Integer id : dspEpInfoMap.keySet()) {
            if (!dspEpId.contains(id)) {
                res.add(id);
            }
        }
        return res;
    }

    public Integer getDspId(Integer dspEpId) {
        if (dspEpId != null && dspEpInfoMap.containsKey(dspEpId)) {
            DspEpInfo dspEpInfo = dspEpInfoMap.get(dspEpId);
            return dspEpInfo.getDspId();
        }
        return null;
    }

    public Integer getDspEpQps(Integer dspEpId) {
        if (dspEpId != null && dspEpInfoMap.containsKey(dspEpId)) {
            DspEpInfo dspEpInfo = dspEpInfoMap.get(dspEpId);
            return determineQps(dspEpInfo);
        }
        return null;
    }

    private int determineQps(DspEpInfo dspEpInfo) {
        int qps = Long.valueOf(dspEpInfo.getQps()).intValue();
        Integer rpmQps = dspEpRpmQpsHour.get(dspEpInfo.getId());
        log.debug("dsp ep {}, rmp_status: {}, qps: {}, qps_min: {}, rpm_qps_hour: {}",
                dspEpInfo.getId(), dspEpInfo.getRpmStatus(), qps, dspEpInfo.getQpsMin(), rpmQps);
        int finalQps = 0;
        if (dspEpInfo.getRpmStatus() != 1) {
            finalQps = qps;
        } else {
            if (rpmQps == null) {
                finalQps = qps;
            } else {
                if (rpmQps <= dspEpInfo.getQpsMin()) {
                    finalQps = dspEpInfo.getQpsMin();
                } else {
                    finalQps =  rpmQps > qps ? qps : rpmQps;
                }
            }
        }
        log.debug("dsp ep {}, final qps: {}", dspEpInfo.getId(), finalQps);
        return finalQps;
    }

}
