package com.iflytek.traffic.session.asset;

import cn.hutool.core.util.StrUtil;
import com.iflytek.traffic.util.Util;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * <AUTHOR>
 * @datetime 2025/5/8 15:34
 */
public class AssetRequest {
	public HashMap<String, Text> texts;
	public HashMap<String, Image> images;
	public HashMap<String, Video> videos;
	public HashMap<String, Audio> audios;
	public Attr attr;

	public void setText(Text text) {
		if (StrUtil.isNotBlank(text.fieldName)) {
			texts.put(text.fieldName, text);
		}
	}

	public void setImage(Image image) {
		if (StrUtil.isNotBlank(image.fieldName)) {
			images.put(image.fieldName, image);
		}
	}

	public void setVideo(Video video) {
		if (StrUtil.isNotBlank(video.fieldName)) {
			videos.put(video.fieldName, video);
		}
	}

	public void setAudio(Audio audio) {
		if (StrUtil.isNotBlank(audio.fieldName)) {
			audios.put(audio.fieldName, audio);
		}
	}

	public static abstract class Field {
		// 元素名称
		public String fieldName;

		// 是否必填
		public boolean required = true;

	}

	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	public static class Text extends Field {
		@Builder.Default public int minLen = 0;
		@Builder.Default public int maxLen = 1000;
	}

	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	public static class Image extends Field {
		public int width;
		public int height;
		public String mimes;
		public int maxSize;
		public String id;
		public List<Integer> companionType;
	}

	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	public static class Audio extends Field {
		public String mimes;
		@Builder.Default
		public int minDuration = 0;
		@Builder.Default
		public int maxDuration = 1000;
		public int maxSize;// 最大限制:单位为bit 1024bit 为1k
	}

	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	public static class Video extends Field {

		public int width;
		public int height;
		public String mimes;
		@Builder.Default
		public double minDuration = 0.0;
		@Builder.Default
		public double maxDuration = 1000.0;
		@Builder.Default
		public int maxSize = 0;// video最大的比特，单位为bit 1024bit 为1k
		public List<DurationInfo> durationInfos;

		@NoArgsConstructor
		@AllArgsConstructor
		public static class DurationInfo {
			public double minDuration = 0.0;
			public double maxDuration = 1000.0;

		}
	}


	public static class Attr {
		public boolean isSupportJump = true;
		public boolean isSuppportDownload = false;
		public boolean isSupportJs = false;
		public boolean isSupportVoicead = false;
		public boolean isSupportDirectVoicead = false;
		public boolean isSupportDriectDeeplink = false;
		public boolean isSupportH5Deeplink = false;
		public boolean isSupportMobileCall = false;
		public boolean isSupportFallback = false;
	}
}
