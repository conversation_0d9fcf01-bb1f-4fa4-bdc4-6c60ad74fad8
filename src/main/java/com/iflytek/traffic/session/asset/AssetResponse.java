package com.iflytek.traffic.session.asset;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.*;

/**
 * <AUTHOR>
 * @datetime 2025/5/8 15:34
 */
public class AssetResponse {
	public HashMap<String, Text> texts;
	public HashMap<String, Image> images;
	public HashMap<String, Video> videos;
	public HashMap<String, Audio> audios;

	public Attribute attribute;
	public AppAttribute appAttribute;
	public long tpDnf;
	@JSONField(deserialize = false)
	public Asset.AdType adType = Asset.AdType.UNKNOWN;
	public int crtType;
	public HashSet<Long> tpDnfList = new HashSet<>();
	/* 广告形式映射tpDnf */
	public Map<Long, Integer> tpDnfToAdType = new HashMap<>();
	public Map<Long, Map<String, List<String>>> tpDnfToFields = new HashMap<>();




	public static class Text {
		public String adm;
		public String fieldName;
		public int len;
		public int id;
		public Set<Integer> labelIds;
	}

	public static class Image {
		public String adm;
		public String fieldName;
		public int width;
		public int height;
		public String mime;
		public int adBytes;
		public int cdn;// creative_maretial.cdn
		public String md5;
		public int id;
		public int positionX;
		public int positionY;
		public int fillWidth;
		public Set<Integer> labelIds;
	}

	public static class Video {
		public String adm;
		public String fieldName;
		public int width;
		public int height;
		public String mime;
		public Double duration;
		public int adBytes;
		public int cdn;// creative_maretial.cdn
		public String md5;
		public int id;
		public int positionX;
		public int positionY;
		public int fillWidth;
		public Set<Integer> labelIds;

	}

	public static class Audio {
		public String adm;
		public String fieldName;
		public String mime;
		public Double duration;
		public int adBytes;
		public int cdn;// creative_maretial.cdn
		public int id;
		public Set<Integer> labelIds;

	}

	public static class Attribute {
		public boolean isJump = true;
		public boolean isDownload = false;
		public boolean hasDeeplink = false;
		public String deeplinkUrl;
		public boolean isJs = false;
		// 是否仅支持deeplink,1：全部支持 2：只投直接deeplihk
		public int onlyDeeplink = 0;
		public boolean isPhone = false;

		public boolean isStep2Download = false;

	}

	public static class AppAttribute {
		public Integer appId; // 应用ID
		public String appName; // 应用名称
		public String appType; // OS类型: 1 -> IOS, 2 -> Android
		public String packageName; // 包名
		public String industryId; // 行业ID
		public String appStoreUrl; // 应用商店url
		public String appDownloadUrl; // 下载地址
		public String appDomain; // APP域名
		public String appVersion; // 版本
		public String privacyPolicy; // 隐私策略 0 -> 否， 1 -> 是
		public String privacyUrl; // 隐私协议URL
		public String permissionUrl; // 用户权限URL
	}

}
