package com.iflytek.traffic.session.response;

import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @datetime 2025/5/8 17:24
 */
@Data
public class SeatBid {

	private String seat;

	private List<Bid> bids;

	// 0 = impressions can be won individually; 1 = impressions must be won or lost as a group
	private Integer group;

	// ext字段直接展开

	public SeatBid(){}

	public SeatBid(com.iflytek.traffic.protocol.rtb25.response.SeatBid source) {
		this.seat = source.seat;
		this.group = source.group;
		this.bids = source.bid != null ?
				source.bid.stream()
						.map(com.iflytek.traffic.session.response.Bid::new)
						.collect(Collectors.toList()) : null;
//
//		if (this.bids != null) {
//			bids.removeIf(bid -> bid.getPrice() == null);
//		}
	}

}
