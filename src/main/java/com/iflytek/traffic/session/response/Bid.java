package com.iflytek.traffic.session.response;

import com.alibaba.fastjson.JSONObject;
import com.iflytek.traffic.session.asset.Asset;
import com.iflytek.traffic.session.asset.AssetResponse;
import com.iflytek.traffic.util.constant.Constants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2025/5/8 17:24
 */
@Data
@Slf4j
public class Bid {

    // dsp bid id
    private String bidId;

    private String impId;

    private Asset.AdType adType = Asset.AdType.UNKNOWN;

    private Integer filterReason;

    /* 返回给媒体的价格 */
    private Long price;

    /* dsp传的原始价格 */
    private Long originPrice;

    /* dsp 获胜价格 */
    private Long winPrice;

    // win notice
    private String nurl;

    // impression
    private String burl;

    // loss notice
    private String lurl;

    // img or video url
    private String iurl;

    private String adm;

    private String adid;
    // Campaign ID
    private String cid;
    // Creative ID
    private String crid;

    private List<String> adomain;

    private String bundle;

    private Integer w;

    private Integer h;

    private Integer wratio;

    private Integer hratio;

    private String dealId;

    private String language;

    private Integer exp;

    private Integer qagmediarating;

    private Integer protocol;

    private List<String> cat;

    private List<Integer> attr;

    private String tactic;

    private String deeplink;

    private String fallback;

    private String storeurl;

    private Integer opentype;

    private Integer api;

    private String materialHashId;

    private String admHashId;

    private String machinedHashId;

    private Long materialId;
    //送审ssp
    private String sspHashId;

    private Integer advertiserId;

    private String templateId;

    private Ext ext;

    public static class Ext {
        public Integer opentype;
        //The video format needs to return the endCard URL
        public String endcard_utl;

        public String deeplink;

        public String fallback;

        public String storeurl;

        public String crtype;

        // 小米侧 app安装监测
        public String insurl;
        // xiaomi 0 represents a new user acquisition ad and a value of 1 represents a user retention ad.
        public Integer landingtype;

        public Ext(com.iflytek.traffic.protocol.rtb25.response.Bid.Ext ext) {
            this.opentype = ext.opentype;
            this.endcard_utl = ext.endcard_utl;
            this.deeplink = ext.deeplink;
            this.fallback = ext.fallback;
            this.storeurl = ext.storeurl;
            this.crtype = ext.crtype;
            this.insurl = ext.insurl;
            this.landingtype = ext.landingtype;
        }
    }

    public Bid(com.iflytek.traffic.protocol.rtb25.response.Bid bid) {
        this.bidId = bid.id;
        this.impId = bid.impid;
        if (bid.price != null) {
            this.price = (long) (bid.price * Constants.PRICE_MULTIPLY_MILLIONS);
            this.originPrice = this.price;
        } else {
            log.warn("bid null price: {}", JSONObject.toJSONString(bid));
        }
        this.nurl = bid.nurl;
        this.burl = bid.burl;
        this.lurl = bid.lurl;
        this.adm = bid.adm;
        this.adid = bid.adid;
        this.adomain = bid.adomain != null ? new ArrayList<>(bid.adomain) : null;
        this.bundle = bid.bundle;
        this.iurl = bid.iurl;
        this.cid = bid.cid;
        this.crid = bid.crid;
        this.tactic = bid.tactic;
        this.cat = bid.cat != null ? new ArrayList<>(bid.cat) : null;
        this.attr = bid.attr != null ? new ArrayList<>(bid.attr) : null;
        this.api = bid.api;
        this.protocol = bid.protocol;
        this.qagmediarating = bid.qagmediarating;
        this.language = bid.language;
        this.dealId = bid.dealid;
        this.w = bid.w;
        this.h = bid.h;
        this.wratio = bid.wratio;
        this.hratio = bid.hratio;
        this.exp = bid.exp;
        if (bid.ext != null) {
            this.setExt(new Ext(bid.ext));
        }
    }


}
