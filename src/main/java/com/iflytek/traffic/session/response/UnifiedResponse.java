package com.iflytek.traffic.session.response;

import cn.hutool.core.collection.CollUtil;
import com.iflytek.traffic.dsp.DspEpObj;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class UnifiedResponse {
    private Integer code;
    /* dsp响应是否有填充 */
    private boolean isFill = true;

    public long bidTime; // DSP 响应到达时间

    private DspEpObj dspEpObj;

    /* 响应是否解析异常 */
    private boolean parseError = false;

    private String id;

    private String mediaReqId;

    private String bidId;

    private String cur;

    private String customdata;

    // no bid reason
    private Integer nbr;

    private List<SeatBid> seatbids;

    /* 记录被过滤掉的bid */
    private Map<String, Bid> imp2FilteredBid = new HashMap<>();

    public UnifiedResponse() {
        this.bidTime = System.currentTimeMillis();
    }

    public Map<String, Bid> getImpId2Bid() {
        if (CollUtil.isNotEmpty(seatbids) ) {
            Map<String, Bid> impIdToBidMap = new HashMap<>();
            for (SeatBid seatBid : seatbids) {
                for (Bid bid : seatBid.getBids()) {
                    impIdToBidMap.put(bid.getImpId(), bid);
                }
            }
            return impIdToBidMap;
        }
        return null;
    }

}
