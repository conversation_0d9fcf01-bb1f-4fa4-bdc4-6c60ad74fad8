package com.iflytek.traffic.session.request;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Pmp {

    private Integer privateauction;

    private List<Deal> deals;

    public Pmp (com.iflytek.traffic.protocol.rtb25.request.Pmp pmp) {
        this.privateauction = pmp.privateauction;
        if (pmp.deals != null && !pmp.deals.isEmpty()) {
            this.deals = new ArrayList<>();
            for (com.iflytek.traffic.protocol.rtb25.request.Deal deal : pmp.deals) {
                this.deals.add(new Deal(deal));
            }
        }
    }
}
