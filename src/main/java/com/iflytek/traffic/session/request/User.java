package com.iflytek.traffic.session.request;

import java.util.ArrayList;
import java.util.List;

@lombok.Data
public class User {

    private String id;

    private String buyeruid;

    private Integer yob;

    private String gender;

    private String keywords;

    private String customdata;

    private Geo geo;

    private List<Data> data;

    public User(){}

    public User(com.iflytek.traffic.protocol.rtb25.request.User user) {
        this.id = user.id;
        this.buyeruid = user.buyeruid;
        this.yob = user.yob;
        this.gender = user.gender;
        this.keywords = user.keywords;
        this.customdata = user.customdata;
        this.geo = user.geo != null ? new Geo(user.geo) : null;
        if (user.data != null) {
            this.data = new ArrayList<>();
            for (com.iflytek.traffic.protocol.rtb25.request.Data d : user.data) {
                this.data.add(new Data(d));
            }
        }
    }



}
