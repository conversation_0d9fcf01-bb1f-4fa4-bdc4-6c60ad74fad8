package com.iflytek.traffic.session.request;

import lombok.Data;

@Data
public class Format {

    private Integer w;

    private Integer h;

    private Integer wratio;

    private Integer hratio;

    private Integer wmin;

    public Format(){}

    public Format (com.iflytek.traffic.protocol.rtb25.request.Format format) {
        this.w = format.w;
        this.h = format.h;
        this.wratio = format.wratio;
        this.hratio = format.hratio;
        this.wmin = format.wmin;
    }

}
