package com.iflytek.traffic.session.request;

import lombok.Data;

import java.util.List;
import java.util.ArrayList;

@Data
public class Banner {

    private List<Format> format;

    private Integer w;

    private Integer h;

    private Integer wmax;

    private Integer hmax;

    private Integer wmin;

    private Integer hmin;

    private List<Integer> btype;

    private List<Integer> battr;

    private Integer pos;

    private List<String> mimes;

    private Integer topframe;

    private List<Integer> expdir;

    private List<Integer> api;

    private String id;

    private Integer vcm;

    public Banner(){}

    public Banner (com.iflytek.traffic.protocol.rtb25.request.Banner banner) {
        this.w = banner.w;
        this.h = banner.h;
        this.wmax = banner.wmax;
        this.hmax = banner.hmax;
        this.wmin = banner.wmin;
        this.hmin = banner.hmin;
        this.pos = banner.pos;
        this.topframe = banner.topframe;
        this.id = banner.id;
        this.vcm = banner.vcm;

        this.btype = banner.btype != null ? new ArrayList<>(banner.btype) : null;
        this.battr = banner.battr != null ? new ArrayList<>(banner.battr) : null;
        this.mimes = banner.mimes != null ? new ArrayList<>(banner.mimes) : null;
        this.expdir = banner.expdir != null ? new ArrayList<>(banner.expdir) : null;
        this.api = banner.api != null ? new ArrayList<>(banner.api) : null;

        if (banner.format != null) {
            this.format = new ArrayList<>();
            for (com.iflytek.traffic.protocol.rtb25.request.Format srcFormat : banner.format) {
                this.format.add(new Format(srcFormat));
            }
        }

    }

}
