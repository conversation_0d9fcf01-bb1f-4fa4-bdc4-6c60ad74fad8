package com.iflytek.traffic.session.request;

import com.iflytek.traffic.util.constant.Constants;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

@Getter
@Setter
public class UnifiedRequest {

    private long reqTime;

    /* 是否需要将请求转发给DSP */
    private boolean needForward = true;

    private String mediaReqId;

    /* 内部生成自己的请求ID */
    private String innerReqId = UUID.randomUUID().toString();

    private Map<String, Impression> imps;

    private Device device;

    private App app;

    private Site site;

    private User user;

    private Integer test;

    private Integer at;

    private Integer tmax = Constants.T_MAX_DEFAULT;

    private List<String> bcat;

    private List<String> badv;

    private List<String> bapp;

    public List<String> wseat;

    public List<String> bseat;

    public Integer allimps;

    public List<String> cur;

    public List<String> wlang;

    private Source source;

    private Regs regs;

    private List<Long> reqDnf = new ArrayList<>();

    public UnifiedRequest() {
        this.reqTime = new Date().getTime();
    }
}
