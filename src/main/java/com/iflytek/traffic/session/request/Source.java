package com.iflytek.traffic.session.request;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Source {

    private Integer fd;

    private String tid;

    private String pchain;

    private SourceExt ext;

    public Source() {}

    @Data
    public static class SourceExt {

        private SChain schain;

        public SourceExt() {}

        public SourceExt(com.iflytek.traffic.protocol.rtb25.request.Source.SourceExt ext) {
            this.schain = ext.schain != null ? new SChain(ext.schain) : null;
        }

    }

    @Data
    public static class SChain {

        private String ver;

        private List<Node> nodes;

        private Integer complete;

        public SChain() {}

        public SChain(com.iflytek.traffic.protocol.rtb25.request.Source.SChain schain) {
            this.ver = schain.ver;
            this.complete = schain.complete;
            if (schain.nodes != null) {
                this.nodes = new ArrayList<>();
                for (com.iflytek.traffic.protocol.rtb25.request.Source.Node node : schain.nodes) {
                    this.nodes.add(new Node(node));
                }
            }
        }
    }

    @Data
    public static class Node {

        private String asi;

        private Integer hp;

        private String sid;

        private String rid;

        public Node() {}

        public Node(com.iflytek.traffic.protocol.rtb25.request.Source.Node node) {
            this.asi = node.asi;
            this.hp = node.hp;
            this.sid = node.sid;
            this.rid = node.rid;
        }

    }
    public Source(com.iflytek.traffic.protocol.rtb25.request.Source source) {
        this.fd = source.fd;
        this.tid = source.tid;
        this.pchain = source.pchain;
        this.ext = source.ext != null ? new SourceExt(source.ext) : null;

        if (source.schain != null && (this.ext == null || this.ext.schain == null)) {
            SourceExt ext = new SourceExt();
            ext.schain = new SChain(source.schain);
            this.ext = ext;
        }
    }


}
