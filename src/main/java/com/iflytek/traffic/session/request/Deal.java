package com.iflytek.traffic.session.request;

import com.iflytek.traffic.util.constant.Constants;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Deal {

    private String id;

    private Long bidfloor;

    private String bidfloorcur;

    private Integer at;

    private List<String> wseat;

    private List<String> wadomain;

    public Deal(com.iflytek.traffic.protocol.rtb25.request.Deal deal) {
        this.id = deal.id;
        this.bidfloor = (long) (deal.bidfloor * Constants.PRICE_MULTIPLY_MILLIONS);
        this.bidfloorcur = deal.bidfloorcur;
        this.at = deal.at;
        this.wseat = deal.wseat != null ? new ArrayList<>(deal.wseat) : null;
        this.wadomain = deal.wadomain != null ? new ArrayList<>(deal.wadomain) : null;
    }


}
