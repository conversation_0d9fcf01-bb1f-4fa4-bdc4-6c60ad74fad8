package com.iflytek.traffic.session.request;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class App {

    private String id;

    /* 该app在系统内部的id */
    private Integer innerId;

    private String name;

    private String bundle;

    private String domain;

    private String storeurl;

    private List<String> cat;

    private List<String> sectioncat;

    private List<String> pagecat;

    private String ver;

    private Integer privacypolicy;

    private Integer paid;

    private Publisher publisher;

    private Content content;

    private String keywords;

    // md5sum sspid_app pkg
    private String adxApp;

    public App(){}

    public App(com.iflytek.traffic.protocol.rtb25.request.App app) {
        this.id = app.id;
        this.name = app.name;
        this.bundle = app.bundle;
        this.domain = app.domain;
        this.storeurl = app.storeurl;
        this.ver = app.ver;
        this.privacypolicy = app.privacypolicy;
        this.paid = app.paid;
        this.keywords = app.keywords;

        this.cat = app.cat != null ? new ArrayList<>(app.cat) : null;
        this.sectioncat = app.sectioncat != null ? new ArrayList<>(app.sectioncat) : null;
        this.pagecat = app.pagecat != null ? new ArrayList<>(app.pagecat) : null;

        this.publisher = app.publisher != null ? new Publisher(app.publisher) : null;
        this.content = app.content != null ? new Content(app.content) : null;
    }

}
