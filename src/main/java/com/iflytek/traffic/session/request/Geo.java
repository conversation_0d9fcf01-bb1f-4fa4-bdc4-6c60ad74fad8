package com.iflytek.traffic.session.request;

import lombok.Data;

@Data
public class Geo {

    private Float lat;

    private Float lon;

    private Integer type;

    private Integer accuracy;

    private Integer lastfix;

    private Integer ipservice;

    private String country;

    private String region;

    private String regionfips104;

    private String metro;

    private String city;

    private String zip;

    private Integer utcoffset;

    public Geo() {}

    public Geo(com.iflytek.traffic.protocol.rtb25.request.Geo geo) {
        this.lat = geo.lat;
        this.lon = geo.lon;
        this.type = geo.type;
        this.accuracy = geo.accuracy;
        this.lastfix = geo.lastfix;
        this.ipservice = geo.ipservice;
        this.country = geo.country;
        this.region = geo.region;
        this.regionfips104 = geo.regionfips104;
        this.metro = geo.metro;
        this.city = geo.city;
        this.zip = geo.zip;
        this.utcoffset = geo.utcoffset;
    }

}
