package com.iflytek.traffic.session.request;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Site {

    private String id;

    private String name;

    private String domain;

    private List<String> cat;

    private List<String> sectioncat;

    private List<String> pagecat;

    private String page;

    private String ref;

    private String search;

    private Integer mobile;

    private Integer privacypolicy;

    private Publisher publisher;

    private Content content;

    private String keywords;
    public Site(){}

    public Site(com.iflytek.traffic.protocol.rtb25.request.Site site) {
        this.id = site.id;
        this.name = site.name;
        this.domain = site.domain;
        this.cat = site.cat != null ? new ArrayList<>(site.cat) : null;
        this.sectioncat = site.sectioncat != null ? new ArrayList<>(site.sectioncat) : null;
        this.pagecat = site.pagecat != null ? new ArrayList<>(site.pagecat) : null;
        this.page = site.page;
        this.ref = site.ref;
        this.search = site.search;
        this.mobile = site.mobile;
        this.privacypolicy = site.privacypolicy;
        this.publisher = site.publisher != null ? new Publisher(site.publisher) : null;
        this.content = site.content != null ? new Content(site.content) : null;
        this.keywords = site.keywords;
    }

}
