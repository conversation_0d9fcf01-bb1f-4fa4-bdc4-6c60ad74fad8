package com.iflytek.traffic.session.request;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.iflytek.traffic.protocol.rtb25.Native;
import com.iflytek.traffic.protocol.rtb25.request.Imp;
import com.iflytek.traffic.session.asset.Asset;
import com.iflytek.traffic.session.asset.AssetRequest;
import com.iflytek.traffic.util.constant.Constants;
import lombok.Data;

import java.util.*;

@Data
public class Impression {

    private String impId;

    /* 媒体传来的广告位ID */
    private String tagId;
    // 内部广告位id
    private Integer adUnitId;
    /* 对DSP展示ID */
    private String adUnitShowId;

    /* md5sum sspid_tagid 全局唯一 */
    private String adxTag;

    // 1 = the ad is interstitial or full screen, 0 = not interstitial.
    private Integer instl;

    // 万分之一分rmb/千次曝光 注意单位
    private Long bidfloor = 0L;
    // 货币单位
    private String bidfloorcur = "USD";


    private Integer secure;

    private String displaymanager;

    private String displaymanagerver;

    private Integer clickbrowser;

    private List<String> iframebuster;

    private Integer exp;

    // 素材要求
    private HashMap<Long, AssetRequest> assetRequestMap = new HashMap<>();

    private List<Long> impDnf = new ArrayList<>();

    private List<Metric> metric;

    private Pmp pmp;

    private Video video;

    private Banner banner;

    private Native native1;

    private Audio audio;

    private Asset.AdType adType = Asset.AdType.UNKNOWN;

    /* 二级广告位形式：是否激励视频 */
    private List<Asset.AdType> subSlotType = new ArrayList<>();

    /* 仅用于下发监测链接时判断。含义：请求中带过来的是banner还是video还是native*/
    private Asset.AdType requestAdType = Asset.AdType.UNKNOWN;

    /* key:dsp ep id*/
    private Map<Integer, Long> dspFloorPrice = new HashMap<>();

    /* key: dsp ep id*/
    private Map<Integer, Float> dspProfitRatio = new HashMap<>();

    private List<String> adUnitSize = new ArrayList<>();

    private Ext ext;

    @Data
    public static class Ext{
        // 1-激励视频，其他-非激励视频
        private Integer reward;

        private Integer deeplink;

        private Integer fallback;

        // 标识广告位类型：10-icon
        private Integer adtype;

        private List<String> packageList;
        // 广告数量受限于小米GetApps上推荐的广告位
        private Integer adCount;

        public Ext(){}
        public Ext(Imp.Ext ext) {
            this.reward = ext.reward;
            if (this.reward == null) {
                this.reward = ext.rewarded;
            }
            this.deeplink = ext.deeplink;
            this.fallback = ext.fallback;
        }
    }

    public Impression(){}

    public Impression(Imp imp) {
        this.setImpId(imp.id);
        if (this.getImpId() == null) {
            this.setImpId(UUID.randomUUID().toString());
        }

        this.setBidfloor((long) (imp.bidfloor * Constants.PRICE_MULTIPLY_MILLIONS));
        this.setBidfloorcur(imp.bidfloorcur);
        this.setTagId(imp.tagid);
        // todo 转换为内部广告位ID

        if (imp.metric != null && !imp.metric.isEmpty()) {
            this.setMetric(new ArrayList<>());
            for (com.iflytek.traffic.protocol.rtb25.request.Metric metric : imp.metric) {
                Metric innerMetric = new Metric(metric);
                this.getMetric().add(innerMetric);
            }
        }

        if (imp.pmp != null) {
            this.setPmp(new Pmp(imp.pmp));
        }
        this.setInstl(imp.instl);
        this.setSecure(imp.secure);
        this.setDisplaymanager(imp.displaymanager);
        this.setDisplaymanagerver(imp.displaymanagerver);
        this.setClickbrowser(imp.clickbrowser);
        this.setIframebuster(imp.iframebuster);
        this.setExp(imp.exp);

        if (imp.ext != null) {
            this.setExt(new Ext(imp.ext));
        }

        // 广告素材
        if (imp.banner != null) {
            this.setBanner(new Banner(imp.banner));
            this.setAdType(Asset.AdType.SUPPER_BANNER);
            if (imp.banner.w != null && imp.banner.h != null) {
                this.adUnitSize.add(imp.banner.w + "*" + imp.banner.h);
            }
            if (CollUtil.isNotEmpty(banner.getFormat())) {
                for (Format format : banner.getFormat()) {
                    if (format.getW() != null && format.getH() != null) {
                        this.adUnitSize.add(format.getW() + "*" + format.getH());
                    }
                }
            }
            this.setRequestAdType(Asset.AdType.SUPPER_BANNER);
        }
        if (imp.video != null) {
            this.setVideo(new Video(imp.video));
            this.setAdType(Asset.AdType.SUPPER_VIDEO);
            this.adUnitSize.add(imp.video.w + "*" + imp.video.h);
            if (ext != null) {
                if (ext.reward != null && ext.reward == 1) {
//                    this.setAdType(Asset.AdType.SUPPER_REWARD_VIDEO);
                }
            }
            if (imp.video.ext != null && imp.video.ext.rewarded != null && imp.video.ext.rewarded == 1) {
//                this.setAdType(Asset.AdType.SUPPER_REWARD_VIDEO);
                if (this.ext != null) {
                    this.getExt().setReward(imp.video.ext.rewarded);
                } else {
                    Ext ext = new Ext();
                    ext.setReward(imp.video.ext.rewarded);
                    this.ext = ext;
                }
            }
            this.setRequestAdType(Asset.AdType.SUPPER_VIDEO);
        }
        if (imp.audio!= null) {
            this.setAudio(new Audio(imp.audio));
        }
        if (imp.native1 != null) {
            this.setNative1(imp.native1);
            this.setAdType(Asset.AdType.SUPPER_NATIVE);
            getNativeAdunitSize(imp.native1);
            this.setRequestAdType(Asset.AdType.SUPPER_NATIVE);
        }
    }

    public void getNativeAdunitSize(Native n) {
        if (n.request != null && !n.request.isEmpty()) {
            Native.Request requestObject = JSON.parseObject(n.request, Native.Request.class);
            Native.NativeRequest requestNative;
            if (null != requestObject && null != requestObject.native1) {
                requestNative = requestObject.native1;
            } else {
                requestNative = JSON.parseObject(n.request, Native.NativeRequest.class);
            }
            if (null != requestNative && null != requestNative.assets) {
                int adSizeType = Constants.NATIVE_IMG_TYPE_IMG;
                if (this.getAdType() == Asset.AdType.SUPPER_ICON || this.getAdType() == Asset.AdType.SUPPER_PUSH) {
                    adSizeType = Constants.NATIVE_IMG_TYPE_ICON;
                }
                for (Native.NativeRequest.Asset asset : requestNative.assets) {
                    if (asset.img != null && asset.img.type == adSizeType) {
                        int w=0,h=0;
                        if (asset.img.w != null) {
                            w = asset.img.w;
                        } else if (asset.img.wmin != null) {
                            w = asset.img.wmin;
                        }
                        if (asset.img.h != null) {
                            h = asset.img.h;
                        } else if (asset.img.hmin != null) {
                            h = asset.img.hmin;
                        }
                        this.adUnitSize.add(w + "*" + h);
                        break;
                    } else if (asset.video != null) {
                        this.adUnitSize.add("16*9");
                        break;
                    }
                }
            }
        }
    }

}
