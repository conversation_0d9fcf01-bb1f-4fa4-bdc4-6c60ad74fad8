package com.iflytek.traffic.session.request;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Audio {

    private List<String> mimes;

    private Integer minduration;

    private Integer maxduration;

    private List<Integer> protocols;

    private Integer startdelay;

    private Integer sequence;

    private List<Integer> battr;

    private Integer maxextended;

    private Integer minbitrate;

    private Integer maxbitrate;

    private List<Integer> delivery;

    private List<Banner> companionad;

    private List<Integer> api;

    private List<Integer> companiontype;

    private Integer maxseq;

    private Integer feed;

    private Integer stitched;

    private Integer nvol;

    public Audio(com.iflytek.traffic.protocol.rtb25.request.Audio audio) {
        this.minduration = audio.minduration;
        this.maxduration = audio.maxduration;
        this.startdelay = audio.startdelay;
        this.sequence = audio.sequence;
        this.maxextended = audio.maxextended;
        this.minbitrate = audio.minbitrate;
        this.maxbitrate = audio.maxbitrate;
        this.maxseq = audio.maxseq;
        this.feed = audio.feed;
        this.stitched = audio.stitched;
        this.nvol = audio.nvol;

        this.mimes = audio.mimes != null ? new ArrayList<>(audio.mimes) : null;
        this.protocols = audio.protocols != null ? new ArrayList<>(audio.protocols) : null;
        this.battr = audio.battr != null ? new ArrayList<>(audio.battr) : null;
        this.delivery = audio.delivery != null ? new ArrayList<>(audio.delivery) : null;
        this.api = audio.api != null ? new ArrayList<>(audio.api) : null;
        this.companiontype = audio.companiontype != null ? new ArrayList<>(audio.companiontype) : null;

        if (audio.companionad != null) {
            this.companionad = new ArrayList<>();
            for (com.iflytek.traffic.protocol.rtb25.request.Banner banner : audio.companionad) {
                this.companionad.add(new Banner(banner));
            }
        }
    }

}
