package com.iflytek.traffic.session.request;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Video {

    private List<String> mimes;

    private Integer minduration;

    private Integer maxduration;

    private List<Integer> protocols;

    private Integer protocol;

    private Integer w;

    private Integer h;

    private Integer startdelay;

    private Integer placement;

    private Integer linearity;

    private Integer skip;

    private Integer skipmin;

    private Integer skipafter;

    private Integer sequence;

    private List<Integer> battr;

    private Integer maxextended;

    private Integer minbitrate;

    private Integer maxbitrate;

    private Integer boxingallowed;

    private List<Integer> playbackmethod;

    private Integer playbackend;

    private List<Integer> delivery;

    private Integer pos;

    private List<Banner> companionad;

    private List<Integer> api;

    private List<Integer> companiontype;

    private VideoExt ext;

    @Data
    public static class VideoExt {

        private Integer rewarded;

    }

    public Video(){}
    
    public Video(com.iflytek.traffic.protocol.rtb25.request.Video video) {
        this.mimes = video.mimes != null ? new ArrayList<>(video.mimes) : null;
        this.minduration = video.minduration;
        this.maxduration = video.maxduration;
        this.protocols = video.protocols != null ? new ArrayList<>(video.protocols) : null;
        this.protocol = video.protocol;
        this.w = video.w;
        this.h = video.h;
        this.startdelay = video.startdelay;
        this.placement = video.placement;
        this.linearity = video.linearity;
        this.skip = video.skip;
        this.skipmin = video.skipmin;
        this.skipafter = video.skipafter;
        this.sequence = video.sequence;
        this.battr = video.battr != null ? new ArrayList<>(video.battr) : null;
        this.maxextended = video.maxextended;
        this.minbitrate = video.minbitrate;
        this.maxbitrate = video.maxbitrate;
        this.boxingallowed = video.boxingallowed;
        this.playbackmethod = video.playbackmethod != null ? new ArrayList<>(video.playbackmethod) : null;
        this.playbackend = video.playbackend;
        this.delivery = video.delivery != null ? new ArrayList<>(video.delivery) : null;
        this.pos = video.pos;
        if ( video.companionad!= null) {
            this.companionad = new ArrayList<>();
            for (com.iflytek.traffic.protocol.rtb25.request.Banner b : video.companionad) {
                this.companionad.add(new Banner(b));
            }
        }
        this.api = video.api != null ? new ArrayList<>(video.api) : null;
        this.companiontype = video.companiontype != null ? new ArrayList<>(video.companiontype) : null;
        
        if (video.ext != null) {
            this.ext = new VideoExt();
            this.ext.rewarded = video.ext.rewarded;
        }
    }

}
