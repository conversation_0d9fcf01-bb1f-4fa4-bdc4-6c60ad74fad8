package com.iflytek.traffic.session.request;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Producer {

    private String id;

    private String name;

    private List<String> cat;

    private String domain;

    public Producer(com.iflytek.traffic.protocol.rtb25.request.Producer producer) {
        if (producer == null) {
            return;
        }
        this.id = producer.id;
        this.name = producer.name;
        this.domain = producer.domain;

        this.cat = producer.cat != null ? new ArrayList<>(producer.cat) : null;
    }

}
