package com.iflytek.traffic.session.request;


import java.util.List;
import java.util.ArrayList;

@lombok.Data
public class Content {

    private String id;

    private Integer episode;

    private String title;

    private String series;

    private String season;

    private String artist;

    private String genre;

    private String album;

    private String isrc;

    private Producer producer;

    private String url;

    private List<String> cat;

    private Integer prodq;

    private Integer videoquality;

    private Integer context;

    private String contentrating;

    private String userrating;

    private Integer qagmediarating;

    private String keywords;

    private Integer livestream;

    private Integer sourcerelationship;

    private Integer len;

    private String language;

    private Integer embeddable;

    private List<Data> data;

    public Content (com.iflytek.traffic.protocol.rtb25.request.Content content) {
        this.id = content.id;
        this.episode = content.episode;
        this.title = content.title;
        this.series = content.series;
        this.season = content.season;
        this.artist = content.artist;
        this.genre = content.genre;
        this.album = content.album;
        this.isrc = content.isrc;
        if (content.producer != null) {
            this.producer = new Producer(content.producer);
        }
        this.url = content.url;
        this.prodq = content.prodq;
        this.videoquality = content.videoquality;
        this.context = content.context;
        this.contentrating = content.contentrating;
        this.userrating = content.userrating;
        this.qagmediarating = content.qagmediarating;
        this.keywords = content.keywords;
        this.livestream = content.livestream;
        this.sourcerelationship = content.sourcerelationship;
        this.len = content.len;
        this.language = content.language;
        this.embeddable = content.embeddable;

        this.cat = content.cat != null ? new ArrayList<>(content.cat) : null;
        if (content.data != null) {
            this.data = new ArrayList<>();
            for (com.iflytek.traffic.protocol.rtb25.request.Data data1 : content.data) {
                this.data.add(new Data(data1));
            }
        }
    }

}
