package com.iflytek.traffic.session.request;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.util.Objects;

@Data
public class Device {

    private String ua;

    private Geo geo;

    private Integer dnt;

    private Integer lmt;

    private String ip;

    private String ipv4;

    private String ipv6;

    // public int i_operator; //0-未知, 1-移动, 2-联通, 3-电信 dnf->cr
    private Carrier carrier = Carrier.UNKNOWN;

    private Integer devicetypeOrigin;

    private String make;

    private String model;

    private Os os = Os.UNKNOWN;

    /* rtb25字段中的原始os值 */
    private String osOrigin;

    private String osv;

    private String hwv;

    private Integer h;

    private Integer w;

    private Integer ppi;

    private Float pxratio;

    private Integer js;

    private Integer geofetch;

    private String flashver;

    private String language;

    private String carrierOrigin;

    private String mccmnc;

    private Integer connectiontype;

    private String ifa;

    private String didsha1;

    private String didmd5;

    private String dpidsha1;

    private String dpidmd5;

    private String macsha1;

    private String macmd5;

    private Network network = Network.UNKNOWN;

    private DeviceType deviceType = DeviceType.UNKNOWN;

    private RegionInfo regionInfo = new RegionInfo();

    private Ext ext;

    public Device(){}

    @Data
    public static class Ext {
        private String idfamd5;
    }

    public Device(com.iflytek.traffic.protocol.rtb25.request.Device device) {
        this.ua = device.ua;
        this.geo = device.geo != null ? new Geo(device.geo) : new Geo();
        this.dnt = device.dnt;
        this.lmt = device.lmt;
        this.ip = device.ip;
        this.ipv6 = device.ipv6;
        this.devicetypeOrigin = device.devicetype;
        if (device.devicetype != null) {
            switch (device.devicetype) {
                case 1:
                    this.deviceType = DeviceType.PAD;
                    break;
                case 2:
                    this.deviceType = DeviceType.PC;
                    break;
                case 3:
                    this.deviceType = DeviceType.TV;
                    break;
                case 4:
                    this.deviceType = DeviceType.PHONE;
                    break;
                case 5:
                    this.deviceType = DeviceType.PAD;
                    break;
                case 7:
                    this.deviceType = DeviceType.TV;
                    break;
                default:
                    this.deviceType = DeviceType.UNKNOWN;
                    break;
            }
        }
        this.make = device.make;
        this.model = device.model;
        if (device.os != null) {
            String os = device.os.toLowerCase().trim();
            if (os.contains("android")) {
                this.os = Os.ANDROID;
            } else if (os.contains("ios")) {
                this.os = Os.IOS;
            } else if (os.contains("windows")) {
                this.os = Os.WINDOWS;
            }
        }
        this.osOrigin = device.os;
        this.osv = device.osv;
        this.hwv = device.hwv;
        this.h = device.h;
        this.w = device.w;
        this.ppi = device.ppi;
        this.pxratio = device.pxratio;
        this.js = device.js;
        this.geofetch = device.geofetch;
        this.flashver = device.flashver;
        this.language = device.language;
        this.carrierOrigin = device.carrier;
        this.mccmnc = device.mccmnc;
        if (StrUtil.isNotBlank(device.mccmnc)) {
            String[] parts = device.mccmnc.split("-"); // todo
            if (parts.length == 2) {
                String mcc = parts[0];
                String mnc = parts[1];
                if (Objects.equals(mcc, "460")) {
                    switch (mnc) {
                        case "00":
                        case "02":
                        case "07":
                        case "04":
                        case "08":
                            this.carrier = Carrier.MOBILE;
                            break;
                        case "01":
                        case "06":
                        case "09":
                            this.carrier = Carrier.UNICOM;
                            break;
                        case "03":
                        case "05":
                        case "11":
                            this.carrier = Carrier.TELECOM;
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        this.connectiontype = device.connectiontype;
        if (device.connectiontype != null) {
            switch (device.connectiontype) {
                case 0:
                    this.network = Network.UNKNOWN;
                    break;
                case 1:
                    this.network = Network.ETHERNET;
                    break;
                case 2:
                    this.network = Network.WIFI;
                    break;
                case 3:
                    this.network = Network.MOBUNKNOWN;
                    break;
                case 4:
                    this.network = Network.M2G;
                    break;
                case 5:
                    this.network = Network.M3G;
                    break;
                case 6:
                    this.network = Network.M4G;
                    break;
                default:
                    this.network = Network.UNKNOWN;
                    break;
            }
        }
        this.ifa = device.ifa;
        this.didsha1 = device.didsha1;
        this.didmd5 = device.didmd5;
        this.dpidsha1 = device.dpidsha1;
        this.dpidmd5 = device.dpidmd5;
        this.macsha1 = device.macsha1;
        this.macmd5 = device.macmd5;
    }

    public enum Os {
        UNKNOWN(404), IOS(1), ANDROID(2), WINDOWS(3);

        private Os(int value) {
            this.value = value;
        }

        private final int value;

        public int getValue() {
            return this.value;
        }
    }

    public enum Carrier {
        UNKNOWN(404, "unknown"), MOBILE(1, "china mobile"), UNICOM(2, "china unicom"), TELECOM(3, "china telecom");

        private Carrier(int value, String name) {
            this.value = value;
            this.name = name;
        }

        private final int value;

        private final String name;

        public int getValue() {
            return this.value;
        }

        public String getName() {
            return this.name;
        }
    }

    public enum Network {
        UNKNOWN(404), WIFI(1), M2G(2), M3G(3), M4G(4), M5G(5), ETHERNET(6), MOBUNKNOWN(7), NONETWORK(-1);

        private Network(int value) {
            this.value = value;
        }

        private final int value;

        public int getValue() {
            return this.value;
        }
    }

    public enum DeviceType {
        UNKNOWN(404), PHONE(1), PC(2), TV(3), PAD(4);

        private DeviceType(int value) {
            this.value = value;
        }

        private final int value;

        public int getValue() {
            return this.value;
        }
    }

    @Data
    public static class RegionInfo {
        private Long countryId;
        private Long region;
        private String cityName;
        private String provinceName;
        private String countryName;

        private String countryCode;
        private String zipCode;
        private String ip2locationRegion;
        private String timezone;
    }
}
