package com.iflytek.traffic.session.request;

import java.util.ArrayList;
import java.util.List;
@lombok.Data
public class Data {

    private String id;

    private String name;

    private List<String> keywords;
    private List<Segment> segment;

    public Data(){}

    public Data(com.iflytek.traffic.protocol.rtb25.request.Data data) {
        this.id = data.id;
        this.name = data.name;

        this.keywords = data.keywords != null ? new ArrayList<>(data.keywords) : null;
        if (data.segment != null) {
            this.segment = new ArrayList<>();
            for (com.iflytek.traffic.protocol.rtb25.request.Segment segment1 : data.segment) {
                this.segment.add(new Segment(segment1));
            }
        }
    }

}
