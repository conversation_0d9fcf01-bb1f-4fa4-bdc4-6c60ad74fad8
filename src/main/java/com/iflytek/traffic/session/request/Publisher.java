package com.iflytek.traffic.session.request;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Publisher {

    private String id;

    private String name;

    private List<String> cat;

    private String domain;

    public Publisher(){}

    public Publisher(com.iflytek.traffic.protocol.rtb25.request.Publisher publisher) {
        this.id = publisher.id;
        this.name = publisher.name;
        this.domain = publisher.domain;

        this.cat = publisher.cat != null ? new ArrayList<>(publisher.cat) : null;
    }

}
