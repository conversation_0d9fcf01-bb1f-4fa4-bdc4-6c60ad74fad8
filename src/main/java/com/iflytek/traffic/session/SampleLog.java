package com.iflytek.traffic.session;

import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.ssp.SspEp;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class SampleLog {

    private String sessionId;

    private Long sessionStartTime;

    private SspEp sspEp;

    private Object mediaReq;

    private String mediaReqStr;

    private Object mediaResp;

    private Map<Integer, DspEpObj> dspEpObj;

    private Map<Integer, Object> dspReq = new HashMap<>();

    private Map<Integer, Object> dspResp = new HashMap<>();

    private Map<Integer, String> dspRespStr = new HashMap<>();

    private Integer winDspId;

    private Integer winDspEpId;


}
