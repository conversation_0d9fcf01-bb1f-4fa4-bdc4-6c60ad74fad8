package com.iflytek.traffic.search.verify;



import com.iflytek.traffic.session.SessionContext;

import java.util.Iterator;
import java.util.List;

/**
 * Created by yjs on 2018/3/13.
 */
public abstract class BasicVerifier<T> {

	public BasicVerifier() {}

	public void filter(List<T> objList, SessionContext sessionContext) {
		verifyInit(objList, sessionContext);
		for (Iterator<T> it = objList.iterator(); it.hasNext(); ) {
			T t = it.next();
			if (!this.verify(t, sessionContext)) {
				it.remove();
			}
		}

	}

	/**
	 * 在调用verify之前针对订单列表进行预处理
	 * @param objList
	 */
	protected void verifyInit(List<T> objList, SessionContext sessionContext){
		return;
	}

	protected abstract boolean verify(T t, SessionContext sessionContext);
}
