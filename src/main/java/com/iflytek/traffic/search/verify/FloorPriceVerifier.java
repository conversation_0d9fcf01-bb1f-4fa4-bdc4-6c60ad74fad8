package com.iflytek.traffic.search.verify;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.iflytek.traffic.data.entity.FloorPriceDirect;
import com.iflytek.traffic.data.provider.DspDataProvider;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.dsp.DspEpSupportWrapper;
import com.iflytek.traffic.protocol.ProtocolParser;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.request.UnifiedRequest;
import com.iflytek.traffic.ssp.SspEp;
import com.iflytek.traffic.util.SpringContextHelper;
import com.iflytek.traffic.util.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class FloorPriceVerifier extends DspEpSupportVerifier {

    @Override
    protected boolean verify(DspEpSupportWrapper dspEpSupportWrapper, SessionContext sessionContext) {
        // Implement the verification logic for floor prices here
        // This is a placeholder implementation
        return true; // Return true if the verification passes, false otherwise
    }

    @Override
    protected void verifyInit(List<DspEpSupportWrapper> objList, SessionContext sessionContext) {
        UnifiedRequest req = sessionContext.getUnifiedRequest();
        Long region = req.getDevice() != null && req.getDevice().getRegionInfo() != null
                && req.getDevice().getRegionInfo().getRegion() != null ? req.getDevice().getRegionInfo().getRegion() : 0L;
        Set<Integer> adType = new HashSet<>();
        List<TagAndPrice<String, Double>> floorPrice = new ArrayList<>();
        DspDataProvider dspDataProvider = SpringContextHelper.getBean(DspDataProvider.class);
        if (MapUtil.isNotEmpty(req.getImps())) {
            req.getImps().values().forEach(imp -> {
                if (imp == null) {
                    return;
                }
                adType.add(imp.getAdType().getValue());
                floorPrice.add(new TagAndPrice<String, Double>(imp.getTagId(), imp.getBidfloor() * 1.0D / Constants.PRICE_MULTIPLY_MILLIONS));
            });
        }
        String adxAppId;
        if (req.getApp() != null) {
            adxAppId = req.getApp().getId();
        } else {
            adxAppId = null;
        }
        SspEp sspEp = sessionContext.getSspEp();
        Iterator<DspEpSupportWrapper> iterator = objList.iterator();
        while (iterator.hasNext()) {
            DspEpSupportWrapper wrapper = iterator.next();
            if (CollUtil.isEmpty(wrapper.getDspEpSupport().getFpDirects())) {
                continue;
            }
            // 寻找匹配地域和广告形式的配置
            Optional<FloorPriceDirect> direct = wrapper.getDspEpSupport().getFpDirects().stream()
                    .filter(i -> {
                        if (i.getSlotType() == null) {
                            // 如果没有指定广告形式，则匹配地域
                            return region.equals(i.getCountryId());
                        } else if (i.getCountryId() == null) {
                            // 如果没有指定地域, 看广告形式是否匹配
                            return adType.contains(i.getSlotType());
                        } else {
                            return adType.contains(i.getSlotType()) && region.equals(i.getCountryId());
                        }
                    }).findFirst();
            if (direct.isEmpty()) {
                direct = wrapper.getDspEpSupport().getFpDirects().stream()
                        .filter(i -> i.getSlotType() == null && i.getCountryId() == null).findFirst();
            }
            if (direct.isEmpty()) {
                // 没有匹配的配置不过滤
                continue;
            }
            FloorPriceDirect fpDirect = direct.get();
            DspEpObj dspEpObj = dspDataProvider.getDspEpObj(wrapper.getDspEpSupport().getDspEpId());
            ProtocolParser parser = sessionContext.getProtocolParser(dspEpObj.getProtocol());
            boolean match = floorPrice.stream().map(fp -> {
                String tagId = fp.tagId();
                Double price = fp.price();
                float profit = parser.getDspProfit(dspEpObj.getDspId(), dspEpObj.getDspEpId(), tagId, adxAppId, sspEp.getSspId(), sspEp.getSspEpId());
                return price / (1 - profit);
            }).anyMatch(p -> p <= fpDirect.getMaxPrice() && p >= fpDirect.getMinPrice());
            if (!match) {
                // 如果没有匹配的价格，则移除该dsp ep支持
                log.info("DspEpSupport {} not match floor price, remove.", wrapper.getDspEpSupport().getId());
                iterator.remove();
            }
        }
    }

    record TagAndPrice<String, Double>(String tagId, Double price) {
    }
}
