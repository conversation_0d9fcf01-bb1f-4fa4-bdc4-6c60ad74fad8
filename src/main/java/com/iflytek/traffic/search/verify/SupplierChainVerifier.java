package com.iflytek.traffic.search.verify;

import cn.hutool.core.collection.CollUtil;
import com.iflytek.traffic.data.entity.SupplierChainDirect;
import com.iflytek.traffic.dsp.DspEpSupportWrapper;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.request.Source;
import com.iflytek.traffic.session.request.UnifiedRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class SupplierChainVerifier extends DspEpSupportVerifier {

    @Value("${schain.max.layer:7}")
    private int maxLayer;

    @Override
    protected boolean verify(DspEpSupportWrapper dspEpSupportWrapper, SessionContext sessionContext) {
        if (dspEpSupportWrapper.getDspEpSupport().getSupplierChainDirect() == null) {
            return true;
        }
        UnifiedRequest request = sessionContext.getUnifiedRequest();
        if (request.getSource() == null || request.getSource().getExt() == null
                || request.getSource().getExt().getSchain() == null
                || CollUtil.isEmpty(request.getSource().getExt().getSchain().getNodes())) {
            // 没有supplier chain, 且定向supplier chain， 一律不投放
            log.debug("{} request no supplier chain.", sessionContext.getSspEp().getSspName());
            return false;
        }
        boolean sspMatch = false;
        boolean layerMatch = false;
        SupplierChainDirect direct = dspEpSupportWrapper.getDspEpSupport().getSupplierChainDirect();
        List<Source.Node> nodes = request.getSource().getExt().getSchain().getNodes();
        if (CollUtil.isEmpty(direct.getSsps())) {
            log.debug("support {} no supplier config.", dspEpSupportWrapper.getDspEpSupport().getId());
            sspMatch = true;
        } else {
            if (direct.getIsFirstChain() != null && direct.getIsFirstChain() == 1) {
                // 如果是首链，直接匹配第一个
                log.debug("support {} supplier config is first chain.", dspEpSupportWrapper.getDspEpSupport().getId());
                Source.Node first = nodes.get(0);
                sspMatch = direct.getSsps().contains(first.getAsi());
            } else {
                // 不是首链，任意一个匹配即可
                for (Source.Node node : nodes) {
                    if (direct.getSsps().contains(node.getAsi())) {
                        sspMatch = true;
                        break;
                    }
                }
            }
        }
        if (CollUtil.isEmpty(direct.getSupplierChains())) {
            log.debug("support {} no layer config.", dspEpSupportWrapper.getDspEpSupport().getId());
            layerMatch = true;
        } else {
            int layer = nodes.size();
            if (nodes.size() > maxLayer) {
                layer = 100;
            }
            layerMatch = direct.getSupplierChains().contains(layer);
        }
        log.debug("support {} sspMatch {}, layerMatch {}, include {}", dspEpSupportWrapper.getDspEpSupport().getId(), sspMatch, layerMatch, direct.getInclude());
        return (direct.getInclude() == 1) == (sspMatch && layerMatch);
    }
}
