package com.iflytek.traffic.search.index;

import com.iflytek.traffic.session.SessionContext;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public abstract class BaseIndex<T, R> {

    Map<Integer, T> id2ObjMap = new ConcurrentHashMap<>();
    Map<Long, Set<Integer>> incj2IdMap = new ConcurrentHashMap<>();
    Map<Long, Set<Integer>> outcj2IdMap = new ConcurrentHashMap<>();

    public abstract void add(T t);

    public void del(Integer id) {
        this.id2ObjMap.remove(id);
        this.incj2IdMap.values().forEach(set -> set.remove(id));
        this.outcj2IdMap.values().forEach(set -> set.remove(id));
    }

    public void delAll() {
        this.id2ObjMap.clear();
        this.incj2IdMap.clear();
        this.outcj2IdMap.clear();
    }

    public abstract List<R> pickAllObj(List<Long> reqDnf, List<Long> impDnf, SessionContext sessionContext);

}
