package com.iflytek.traffic.dsp;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DspEpObj {

    private Integer dspId;

    private Integer dspEpId;

    private String name;

    private String prefix;

    private Integer qps;

    private Integer timeout;

    private Long tmax;

    private String path;

    /**
     * 结算类型：1:adm；2:burl
     */
    private Integer settlementType;

    private Integer isGzip;

    // 曝光有效期，单位：秒
    private Integer impTtl;

    private String protocol;

    public String genQpsCtrlKey() {
        return "DSP_" + dspId + "_EP_" + dspEpId + "_QPS_CTRL";
    }

}
