package com.iflytek.traffic.dsp;

import com.iflytek.traffic.data.entity.FloorPriceDirect;
import com.iflytek.traffic.data.entity.SupplierChainDirect;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class DspEpSupport {

    // 唯一标识圈量策略与dsp ep关联关系
    private Integer id;

    // 圈量策略ID
    private Integer dspEpSupportId;

    // 比例
    private int ratio = 100;

    private Integer dspId;

    private String dspName;

    private Integer dspEpId;

    // 定向类别个数
    private Integer cjHeadNum;

    // 定向
    private List<Long> in;

    // 排除
    private List<Long> out;

    // 底价定向
    private List<FloorPriceDirect> fpDirects;

    // supplier chain 定向
    private SupplierChainDirect supplierChainDirect;

}
