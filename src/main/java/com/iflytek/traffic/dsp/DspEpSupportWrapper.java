package com.iflytek.traffic.dsp;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DspEpSupportWrapper {

    private DspEpSupport dspEpSupport;

    private Integer dspId;

    private Integer dspEpId;

    private int weight;

    public DspEpSupportWrapper(DspEpSupport dspEpSupport) {
        this.dspEpSupport = dspEpSupport;
        this.dspId = dspEpSupport.getDspId();
        this.dspEpId = dspEpSupport.getDspEpId();
        this.weight = dspEpSupport.getRatio();
    }

}
