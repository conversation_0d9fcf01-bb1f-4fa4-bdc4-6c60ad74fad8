package com.iflytek.traffic.protocol.rtb25.request;

import java.util.ArrayList;
import java.util.List;

public class App {

    public String id;

    public String name;

    public String bundle;

    public String domain;

    public String storeurl;

    public List<String> cat;

    public List<String> sectioncat;

    public List<String> pagecat;

    public String ver;

    public Integer privacypolicy;

    public Integer paid;

    public Publisher publisher;

    public Content content;

    public String keywords;

    public App() {}

    public App(com.iflytek.traffic.session.request.App app) {
        this.id = app.getId();
        this.name = app.getName();
        this.bundle = app.getBundle();
        this.domain = app.getDomain();
        this.storeurl = app.getStoreurl();
        this.ver = app.getVer();
        this.privacypolicy = app.getPrivacypolicy();
        this.paid = app.getPaid();
        this.keywords = app.getKeywords();

        this.cat = app.getCat() != null ? new ArrayList<>(app.getCat()) : null;
        this.sectioncat = app.getSectioncat() != null ? new ArrayList<>(app.getSectioncat()) : null;
        this.pagecat = app.getPagecat() != null ? new ArrayList<>(app.getPagecat()) : null;

        this.publisher = app.getPublisher() != null ? new Publisher(app.getPublisher()) : null;
        this.content = app.getContent() != null ? new Content(app.getContent()) : null;
    }

}
