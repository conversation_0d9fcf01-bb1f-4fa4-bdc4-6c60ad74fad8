package com.iflytek.traffic.protocol.rtb25.request;

import java.util.ArrayList;
import java.util.List;

public class Producer {

    public String id;

    public String name;

    public List<String> cat;

    public String domain;

    public Producer() {}

    public Producer(com.iflytek.traffic.session.request.Producer p) {
        this.id = p.getId();
        this.name = p.getName();
        this.cat = p.getCat() != null ? new ArrayList<>(p.getCat()) : null;
        this.domain = p.getDomain();
    }

}
