package com.iflytek.traffic.protocol.rtb25.request;

import java.util.ArrayList;
import java.util.List;

public class Data {

    public String id;

    public String name;

    public List<String> keywords;

    public List<Segment> segment;

    public Data(){}

    public Data(com.iflytek.traffic.session.request.Data data) {
        this.id = data.getId();
        this.name = data.getName();

        // 防御性拷贝集合
        this.keywords = data.getKeywords() != null ? new ArrayList<>(data.getKeywords()) : null;

        // 深拷贝嵌套对象
        if (data.getSegment() != null) {
            this.segment = new ArrayList<>();
            for (com.iflytek.traffic.session.request.Segment segment : data.getSegment()) {
                this.segment.add(new Segment(segment));
            }
        }
    }

}
