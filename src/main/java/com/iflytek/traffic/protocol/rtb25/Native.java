package com.iflytek.traffic.protocol.rtb25;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class Native {

    public String request;

    public String ver;

    public static class Request {

        @JSONField(name = "native")
        public NativeRequest native1;

    }

    public static class NativeRequest {

        public String ver;

        public List<Asset> assets;

        public static class Asset {

            public Integer id;

            public Integer required;

            public Title title;

            public Image img;

            public Video video;

            public Data data;

        }

        public static class Title {

            public Integer len;

        }

        public static class Image {
            // 1-icon；2-logo；3-img
            public Integer type;

            public Integer w;

            public Integer wmin;

            public Integer h;

            public Integer hmin;

            public List<String> mimes;

        }

        public static class Video {

            public List<String> mimes;

            public Integer minduration;

            public Integer maxduration;

            public List<Integer> protocols;

        }

        public static class Data {

            public Integer type;

            public Integer len;

        }

    }

    public static class NativeResponse {

        public String ver;

        public List<Asset> assets;

        public Link link;

        public List<String> imptrackers;

        public static class Asset {

            public Integer id;

            public Integer required;

            public Title title;

            public Image img;

            public Video video;

            public Data data;

            public Link link;

        }

        public static class Title {

            public String text;

        }

        public static class Image {

            public String url;

            public Integer w;

            public Integer h;

        }

        public static class Video {

            public String vasttag;

        }

        public static class Data {

            public String label;

            public String value;

        }

        public static class Link {

            public String url;

            public List<String> clicktrackers;

        }

    }

}
