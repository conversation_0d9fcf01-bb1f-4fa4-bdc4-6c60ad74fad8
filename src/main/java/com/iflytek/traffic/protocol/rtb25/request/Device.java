package com.iflytek.traffic.protocol.rtb25.request;

public class Device {

    public String ua;

    public Geo geo;

    public Integer dnt;

    public Integer lmt;

    public String ip;

    public String ipv6;

    public Integer devicetype;

    public String make;

    public String model;

    public String os;

    public String osv;

    public String hwv;

    public Integer h;

    public Integer w;

    public Integer ppi;

    public Float pxratio;

    public Integer js;

    public Integer geofetch;

    public String flashver;

    public String language;

    public String carrier;

    public String mccmnc;

    public Integer connectiontype;

    public String ifa;

    public String didsha1;

    public String didmd5;

    public String dpidsha1;

    public String dpidmd5;

    public String macsha1;

    public String macmd5;

    public Ext ext;

    public static class Ext {
        public String idfamd5;
    }

    public Device() {}

    public Device(com.iflytek.traffic.session.request.Device device) {
        this.ua = device.getUa();
        this.geo = device.getGeo() != null ? new Geo(device.getGeo()) : null;
        this.dnt = device.getDnt();
        this.lmt = device.getLmt();
        this.ip = device.getIp();
        this.ipv6 = device.getIpv6();
        this.devicetype = device.getDevicetypeOrigin();
        this.make = device.getMake();
        this.model = device.getModel();
        this.os = device.getOsOrigin();
        this.osv = device.getOsv();
        this.hwv = device.getHwv();
        this.h = device.getH();
        this.w = device.getW();
        this.ppi = device.getPpi();
        this.pxratio = device.getPxratio();
        this.js = device.getJs();
        this.geofetch = device.getGeofetch();
        this.flashver = device.getFlashver();
        this.language = device.getLanguage();
        this.carrier = device.getCarrierOrigin();
        this.mccmnc = device.getMccmnc();
        this.connectiontype = device.getConnectiontype();
        this.ifa = device.getIfa();
        this.didsha1 = device.getDidsha1();
        this.didmd5 = device.getDidmd5();
        this.dpidsha1 = device.getDpidsha1();
        this.dpidmd5 = device.getDpidmd5();
        this.macsha1 = device.getMacsha1();
        this.macmd5 = device.getMacmd5();

        if (device.getExt() != null) {
            Ext ext = new Ext();
            ext.idfamd5 = device.getExt().getIdfamd5();
            this.ext = ext;
        }
    }

}
