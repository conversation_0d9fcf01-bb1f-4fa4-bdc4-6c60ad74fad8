package com.iflytek.traffic.protocol.rtb25.request;

import java.util.List;

public class Rtb25Request {

    public String id;

    public List<Imp> imp;

    public Site site;

    public App app;

    public Device device;

    public User user;

    public Integer test;

    public List<String> bcat;

    public List<String> badv;

    public List<String> bapp;

    public Integer at;

    public Integer tmax;

    public List<String> wseat;

    public List<String> bseat;

    public Integer allimps;

    public List<String> cur;

    public List<String> wlang;

    public Source source;

    public Regs regs;
}
