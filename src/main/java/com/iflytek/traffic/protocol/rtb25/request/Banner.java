package com.iflytek.traffic.protocol.rtb25.request;

import java.util.ArrayList;
import java.util.List;

public class Banner {

    public List<Format> format;

    public Integer w;

    public Integer h;

    public Integer wmax;

    public Integer hmax;

    public Integer wmin;

    public Integer hmin;

    public List<Integer> btype;

    public List<Integer> battr;

    public Integer pos;

    public List<String> mimes;

    public Integer topframe;

    public List<Integer> expdir;

    public List<Integer> api;

    public String id;

    public Integer vcm;

    public Banner() {}

    public Banner(com.iflytek.traffic.session.request.Banner banner) {
        this.w = banner.getW();
        this.h = banner.getH();
        this.wmax = banner.getWmax();
        this.hmax = banner.getHmax();
        this.wmin = banner.getWmin();
        this.hmin = banner.getHmin();
        this.pos = banner.getPos();
        this.topframe = banner.getTopframe();
        this.id = banner.getId();
        this.vcm = banner.getVcm();

        this.btype = banner.getBtype() != null ? new ArrayList<>(banner.getBtype()) : null;
        this.battr = banner.getBattr() != null ? new ArrayList<>(banner.getBattr()) : null;
        this.mimes = banner.getMimes() != null ? new ArrayList<>(banner.getMimes()) : null;
        this.expdir = banner.getExpdir() != null ? new ArrayList<>(banner.getExpdir()) : null;
        this.api = banner.getApi() != null ? new ArrayList<>(banner.getApi()) : null;

        if (banner.getFormat() != null) {
            this.format = new ArrayList<>();
            for (com.iflytek.traffic.session.request.Format format : banner.getFormat()) {
                this.format.add(new Format(format));
            }
        }

    }

}
