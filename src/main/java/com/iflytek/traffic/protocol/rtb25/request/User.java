package com.iflytek.traffic.protocol.rtb25.request;

import java.util.List;
import java.util.stream.Collectors;

public class User {

    public String id;

    public String buyeruid;

    public Integer yob;

    public String gender;

    public String keywords;

    public String customdata;

    public Geo geo;

    public List<Data> data;

    public User(){}

    public User(com.iflytek.traffic.session.request.User u) {
        this.id = u.getId();
        this.buyeruid = u.getBuyeruid();
        this.yob = u.getYob();
        this.gender = u.getGender();
        this.keywords = u.getKeywords();
        this.customdata = u.getCustomdata();
        this.geo = u.getGeo() != null ? new Geo(u.getGeo()) : null;
        this.data = u.getData() != null ?
                u.getData().stream().map(Data::new).collect(Collectors.toList()) : null;
    }

}
