package com.iflytek.traffic.protocol.rtb25.response;

import com.iflytek.traffic.util.constant.Constants;

import java.util.ArrayList;
import java.util.List;

public class Bid {

    public String id;

    public String impid;

    public Float price;

    public String nurl;

    public String burl;

    public String lurl;

    public String adm;

    public String adid;

    public List<String> adomain;

    public String bundle;

    public String iurl;

    public String cid;

    public String crid;

    public String tactic;

    public List<String> cat;

    public List<Integer> attr;

    public Integer api;

    public Integer protocol;

    public Integer qagmediarating;

    public String language;

    public String dealid;

    public Integer w;

    public Integer h;

    public Integer wratio;

    public Integer hratio;

    public Integer exp;

    public Ext ext;

    public static class Ext {
        public Integer opentype;
        //The video format needs to return the endCard URL
        public String endcard_utl;

        public String deeplink;

        public String fallback;

        public String storeurl;

        public String crtype;
        // 小米侧 app安装监测
        public String insurl;
        // xiaomi 0 represents a new user acquisition ad and a value of 1 represents a user retention ad.
        public Integer landingtype;

        public Ext() {}

        public Ext(com.iflytek.traffic.session.response.Bid.Ext ext) {
            this.opentype = ext.opentype;
            this.endcard_utl = ext.endcard_utl;
            this.deeplink = ext.deeplink;
            this.fallback = ext.fallback;
            this.storeurl = ext.storeurl;
            this.crtype = ext.crtype;
        }
    }

    public static class Native {
        public String ver = "1.1";
        public ArrayList<Asset> assets;
        public Link link;
        public ArrayList<String> imptrackers;

        public static class Asset {
            public int id;
            public int required;
            public Title title;
            public Video video;
            public Image img;
            public Data data;

            public static class Title {
                public String text;
            }

            public static class Image {
                public String url;
                public Integer w;
                public Integer h;
                public Integer type;
            }

            public static class Video {
                //vast xml
                public String vasttag;
            }

            public static class Data {
                public String label;
                public String value;
                public Integer type;
            }
        }


        public static class Link {
            public String url;
            public ArrayList<String> clicktrackers;
            public String fallback;
        }
    }


    public Bid() {}


    public Bid(com.iflytek.traffic.session.response.Bid bid, Integer settlementType) {
        this.id = bid.getBidId();
        this.impid = bid.getImpId();
        if (bid.getPrice() != null) {
            this.price = (float)(bid.getPrice() *1.0/ Constants.PRICE_MULTIPLY_MILLIONS);
        }
        this.nurl = bid.getNurl();
        if (settlementType == Constants.SETTLEMENTTYPE_BURL) {
            this.burl = bid.getBurl();
        }
        // 暂时不填lurl
//        this.lurl = bid.getLurl();
        this.adm = bid.getAdm();
        this.adid = bid.getAdid();
        this.adomain = bid.getAdomain() != null ? new ArrayList<>(bid.getAdomain()) : null;
        this.bundle = bid.getBundle();
        this.iurl = bid.getIurl();
        this.cid = bid.getCid();
        this.crid = bid.getCrid();
        this.tactic = bid.getTactic();
        this.cat = bid.getCat() != null ? new ArrayList<>(bid.getCat()) : null;
        this.attr = bid.getAttr() != null ? new ArrayList<>(bid.getAttr()) : null;
        this.api = bid.getApi();
        this.protocol = bid.getProtocol();
        this.qagmediarating = bid.getQagmediarating();
        this.language = bid.getLanguage();
        this.dealid = bid.getDealId();
        this.w = bid.getW();
        this.h = bid.getH();
        this.wratio = bid.getWratio();
        this.hratio = bid.getHratio();
        this.exp = bid.getExp();
        if (bid.getExt() != null) {
            this.ext = new Ext(bid.getExt());
        }
    }

}
