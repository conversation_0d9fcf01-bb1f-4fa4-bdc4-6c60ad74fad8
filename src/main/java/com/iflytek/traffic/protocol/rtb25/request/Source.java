package com.iflytek.traffic.protocol.rtb25.request;

import java.util.List;
import java.util.stream.Collectors;

public class Source {

    public Integer fd;

    public String tid;

    public String pchain;

    public SourceExt ext;

    public SChain schain;

    public Source() {}

    public static class SourceExt {

        public SChain schain;

        public SourceExt() {}

        public SourceExt(com.iflytek.traffic.session.request.Source.SourceExt ext) {
            this.schain = ext.getSchain() != null ? new SChain(ext.getSchain()) : null;
        }

    }

    public static class SChain {

        public String ver;

        public List<Node> nodes;

        public Integer complete;

        public SChain(){}

        public SChain(com.iflytek.traffic.session.request.Source.SChain schain) {
            this.ver = schain.getVer();
            this.nodes = schain.getNodes() != null ?
                    schain.getNodes().stream().map(Node::new).collect(Collectors.toList()) : null;
            this.complete = schain.getComplete();
        }
    }

    public static class Node {

        public String asi;

        public Integer hp;

        public String sid;

        public String rid;

        public Node(){}

        public Node(com.iflytek.traffic.session.request.Source.Node node) {
            this.asi = node.getAsi();
            this.hp = node.getHp();
            this.sid = node.getSid();
            this.rid = node.getRid();
        }

    }

    public Source(com.iflytek.traffic.session.request.Source s) {
        this.fd = s.getFd();
        this.tid = s.getTid();
        this.pchain = s.getPchain();
        this.ext = s.getExt() != null ? new SourceExt(s.getExt()) : null;
    }

}
