package com.iflytek.traffic.protocol.rtb25.request;

import com.iflytek.traffic.util.constant.Constants;

import java.util.ArrayList;
import java.util.List;

public class Deal {

    public String id;

    public Float bidfloor = 0.0f;

    public String bidfloorcur = "USD";

    public Integer at;

    public List<String> wseat;

    public List<String> wadomain;

    public Deal() {}

    public Deal(com.iflytek.traffic.session.request.Deal deal) {
        this.id = deal.getId();
        this.bidfloor = (float) (deal.getBidfloor() / Constants.PRICE_MULTIPLY_MILLIONS);
        this.bidfloorcur = deal.getBidfloorcur();
        this.at = deal.getAt();

        this.wseat = deal.getWseat() != null ? new ArrayList<>(deal.getWseat()) : null;
        this.wadomain = deal.getWadomain() != null ? new ArrayList<>(deal.getWadomain()) : null;
    }

}
