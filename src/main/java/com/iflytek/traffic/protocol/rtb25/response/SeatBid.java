package com.iflytek.traffic.protocol.rtb25.response;

import java.util.List;
import java.util.stream.Collectors;

public class SeatBid {

    public List<Bid> bid;

    public String seat;

    public Integer group = 0;

    public SeatBid() {}

    public SeatBid(com.iflytek.traffic.session.response.SeatBid seatBid, Integer settlementType) {
        this.seat = seatBid.getSeat();
        this.group = seatBid.getGroup();
        this.bid = seatBid.getBids() != null ?
                seatBid.getBids().stream()
                        .map(a->new Bid(a, settlementType))
                        .collect(Collectors.toList()) : null;
    }

}
