package com.iflytek.traffic.protocol.rtb25.request;

import java.util.ArrayList;
import java.util.List;

public class Audio {

    public List<String> mimes;

    public Integer minduration;

    public Integer maxduration;

    public List<Integer> protocols;

    public Integer startdelay;

    public Integer sequence;

    public List<Integer> battr;

    public Integer maxextended;

    public Integer minbitrate;

    public Integer maxbitrate;

    public List<Integer> delivery;

    public List<Banner> companionad;

    public List<Integer> api;

    public List<Integer> companiontype;

    public Integer maxseq;

    public Integer feed;

    public Integer stitched;

    public Integer nvol;

    public Audio() {}

    public Audio(com.iflytek.traffic.session.request.Audio audio) {
        // 基本类型直接赋值
        this.minduration = audio.getMinduration();
        this.maxduration = audio.getMaxduration();
        this.startdelay = audio.getStartdelay();
        this.sequence = audio.getSequence();
        this.maxextended = audio.getMaxextended();
        this.minbitrate = audio.getMinbitrate();
        this.maxbitrate = audio.getMaxbitrate();
        this.maxseq = audio.getMaxseq();
        this.feed = audio.getFeed();
        this.stitched = audio.getStitched();
        this.nvol = audio.getNvol();

        // 防御性拷贝集合
        this.mimes = audio.getMimes() != null ? new ArrayList<>(audio.getMimes()) : null;
        this.protocols = audio.getProtocols() != null ? new ArrayList<>(audio.getProtocols()) : null;
        this.battr = audio.getBattr() != null ? new ArrayList<>(audio.getBattr()) : null;
        this.delivery = audio.getDelivery() != null ? new ArrayList<>(audio.getDelivery()) : null;
        this.api = audio.getApi() != null ? new ArrayList<>(audio.getApi()) : null;
        this.companiontype = audio.getCompaniontype() != null ? new ArrayList<>(audio.getCompaniontype()) : null;

        // 深拷贝嵌套对象
        if (audio.getCompanionad() != null) {
            this.companionad = new ArrayList<>();
            for (com.iflytek.traffic.session.request.Banner banner : audio.getCompanionad()) {
                this.companionad.add(new Banner(banner));
            }
        }
    }

}
