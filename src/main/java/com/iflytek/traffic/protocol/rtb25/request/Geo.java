package com.iflytek.traffic.protocol.rtb25.request;

public class Geo {

    public Float lat;

    public Float lon;

    public Integer type;

    public Integer accuracy;

    public Integer lastfix;

    public Integer ipservice;

    public String country;

    public String region;

    public String regionfips104;

    public String metro;

    public String city;

    public String zip;

    public Integer utcoffset;

    public Geo(){}

    public Geo(com.iflytek.traffic.session.request.Geo g) {
        this.lat = g.getLat();
        this.lon = g.getLon();
        this.type = g.getType();
        this.accuracy = g.getAccuracy();
        this.lastfix = g.getLastfix();
        this.ipservice = g.getIpservice();
        this.country = g.getCountry();
        this.region = g.getRegion();
        this.regionfips104 = g.getRegionfips104();
        this.metro = g.getMetro();
        this.city = g.getCity();
        this.zip = g.getZip();
        this.utcoffset = g.getUtcoffset();
    }

}
