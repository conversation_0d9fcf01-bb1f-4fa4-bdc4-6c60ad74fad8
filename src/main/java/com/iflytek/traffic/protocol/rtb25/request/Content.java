package com.iflytek.traffic.protocol.rtb25.request;

import java.util.ArrayList;
import java.util.List;

public class Content {

    public String id;

    public Integer episode;

    public String title;

    public String series;

    public String season;

    public String artist;

    public String genre;

    public String album;

    public String isrc;

    public Producer producer;

    public String url;

    public List<String> cat;

    public Integer prodq;

    public Integer videoquality;

    public Integer context;

    public String contentrating;

    public String userrating;

    public Integer qagmediarating;

    public String keywords;

    public Integer livestream;

    public Integer sourcerelationship;

    public Integer len;

    public String language;

    public Integer embeddable;

    public List<Data> data;

    public Content(){}

    public Content(com.iflytek.traffic.session.request.Content content) {
        this.id = content.getId();
        this.episode = content.getEpisode();
        this.title = content.getTitle();
        this.series = content.getSeries();
        this.season = content.getSeason();
        this.artist = content.getArtist();
        this.genre = content.getGenre();
        this.album = content.getAlbum();
        this.isrc = content.getIsrc();
        this.url = content.getUrl();
        this.prodq = content.getProdq();
        this.videoquality = content.getVideoquality();
        this.context = content.getContext();
        this.contentrating = content.getContentrating();
        this.userrating = content.getUserrating();
        this.qagmediarating = content.getQagmediarating();
        this.keywords = content.getKeywords();
        this.livestream = content.getLivestream();
        this.sourcerelationship = content.getSourcerelationship();
        this.len = content.getLen();
        this.language = content.getLanguage();
        this.embeddable = content.getEmbeddable();

        this.cat = content.getCat() != null ? new ArrayList<>(content.getCat()) : null;

        if (content.getProducer() != null) {
            this.producer = new Producer(content.getProducer());
        }

        if (content.getData() != null) {
            this.data = new ArrayList<>();
            for (com.iflytek.traffic.session.request.Data data : content.getData()) {
                this.data.add(new Data(data));
            }
        }
    }

}
