package com.iflytek.traffic.protocol.rtb25;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.iflytek.traffic.data.provider.FusionProvider;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.protocol.Protocol;
import com.iflytek.traffic.protocol.ProtocolParser;
import com.iflytek.traffic.protocol.rtb25.request.Imp;
import com.iflytek.traffic.protocol.rtb25.request.Rtb25Request;
import com.iflytek.traffic.protocol.rtb25.response.Rtb25Response;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.request.*;
import com.iflytek.traffic.session.response.Bid;
import com.iflytek.traffic.session.response.SeatBid;
import com.iflytek.traffic.session.response.UnifiedResponse;
import com.iflytek.traffic.ssp.SspEp;
import com.iflytek.traffic.util.Util;
import com.iflytek.traffic.util.constant.Constants;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Protocol(name = "rtb25")
public class Rtb25Parser extends ProtocolParser<Rtb25Request, Rtb25Response> {

    public UnifiedRequest reqBody2UnifiedReq(byte[] body, SspEp sspEp) throws Exception {
        UnifiedRequest unifiedRequest = new UnifiedRequest();
        Rtb25Request bidrequest;
        try {
            bidrequest = JSON.parseObject(body, Rtb25Request.class);
            if (log.isDebugEnabled()) {
                log.debug("ssp:{}, ep:{}, recv req:{}", sspEp.getSspName(), sspEp.getSspEpId(), JSON.toJSONString(bidrequest));
            }
        } catch (Exception e) {
            log.warn("ssp:{}, ep:{}, parse err:{}", sspEp.getSspName(), sspEp.getSspEpId(), Util.genStackInfo(e), e);
            unifiedRequest.setNeedForward(false);
            return unifiedRequest;
        }

        unifiedRequest.setMediaReqId(bidrequest.id);
        Map<String, Impression> imps = reqBody2UnifiedReqImp(bidrequest.imp);
        if (!imps.isEmpty()) {
            unifiedRequest.setImps(imps);
        }

        unifiedRequest.setSite(reqBody2UnifiedReqSite(bidrequest.site));
        unifiedRequest.setApp(reqBody2UnifiedReqApp(bidrequest.app));
        unifiedRequest.setDevice(reqBody2UnifiedReqDevice(bidrequest.device));
        unifiedRequest.setUser(reqBody2UnifiedReqUser(bidrequest.user));
        unifiedRequest.setTest(bidrequest.test);
        unifiedRequest.setAt(bidrequest.at);
        unifiedRequest.setTmax(bidrequest.tmax);
        unifiedRequest.setAllimps(bidrequest.allimps);
        unifiedRequest.setSource(reqBody2UnifiedReqSource(bidrequest.source));
        unifiedRequest.setRegs(reqBody2UnifiedReqRegs(bidrequest.regs));

        if (bidrequest.bcat != null) {
            unifiedRequest.setBcat(new ArrayList<>(bidrequest.bcat));
        }
        if (bidrequest.badv != null) {
            unifiedRequest.setBadv(new ArrayList<>(bidrequest.badv));
        }
        if (bidrequest.bapp != null) {
            unifiedRequest.setBapp(new ArrayList<>(bidrequest.bapp));
        }
        if (bidrequest.wseat != null) {
            unifiedRequest.setWseat(new ArrayList<>(bidrequest.wseat));
        }
        if (bidrequest.bseat != null) {
            unifiedRequest.setBseat(new ArrayList<>(bidrequest.bseat));
        }
        if (bidrequest.cur != null) {
            unifiedRequest.setCur(new ArrayList<>(bidrequest.cur));
        }
        if (bidrequest.wlang != null) {
            unifiedRequest.setWlang(new ArrayList<>(bidrequest.wlang));
        }

        return unifiedRequest;
    }

    @Override
    public Rtb25Request unifiedReq2ProtocolReq(SessionContext sessionContext, UnifiedRequest unifiedReq, DspEpObj dspEpObj) throws Exception {
        Rtb25Request rtb25Request = new Rtb25Request();
        rtb25Request.id = unifiedReq.getInnerReqId();
        rtb25Request.test = unifiedReq.getTest();
        rtb25Request.at = unifiedReq.getAt();
        rtb25Request.tmax = dspEpObj.getTmax().intValue();
        rtb25Request.allimps = unifiedReq.getAllimps();

        String adxAppId = null;
        if (unifiedReq.getApp() != null) {
            adxAppId = unifiedReq.getApp().getId();
        }
        rtb25Request.imp = unifiedReq2ProtocolReqImp(unifiedReq.getImps(), dspEpObj.getDspId(), dspEpObj.getDspEpId(), adxAppId, sessionContext.getSspEp().getSspId(),
                sessionContext.getSspEp().getSspEpId());
        rtb25Request.site = unifiedReq2ProtocolReqSite(unifiedReq.getSite());
        rtb25Request.app = unifiedReq2ProtocolReqApp(unifiedReq.getApp());
        rtb25Request.device = unifiedReq2ProtocolReqDevice(unifiedReq.getDevice());
        rtb25Request.user = unifiedReq2ProtocolReqUser(unifiedReq.getUser());
        rtb25Request.source = unifiedReq2ProtocolReqSource(unifiedReq.getSource(), rtb25Request.id);
        rtb25Request.regs = unifiedReq2ProtocolReqRegs(unifiedReq.getRegs());

        rtb25Request.bcat = unifiedReq.getBcat() != null ?
                new ArrayList<>(unifiedReq.getBcat()) : null;
        rtb25Request.badv = unifiedReq.getBadv() != null ?
                new ArrayList<>(unifiedReq.getBadv()) : null;
        rtb25Request.bapp = unifiedReq.getBapp() != null ?
                new ArrayList<>(unifiedReq.getBapp()) : null;
        rtb25Request.wseat = unifiedReq.getWseat() != null ?
                new ArrayList<>(unifiedReq.getWseat()) : null;
        rtb25Request.bseat = unifiedReq.getBseat() != null ?
                new ArrayList<>(unifiedReq.getBseat()) : null;
        rtb25Request.cur = unifiedReq.getCur() != null ?
                new ArrayList<>(unifiedReq.getCur()) : null;
        rtb25Request.wlang = unifiedReq.getWlang() != null ?
                new ArrayList<>(unifiedReq.getWlang()) : null;

        log.info("dsp name:{}, id: {}, ep: {}, send request:{}", dspEpObj.getName(), dspEpObj.getDspId(), dspEpObj.getDspEpId(), JSON.toJSON(rtb25Request).toString());
        return rtb25Request;
    }

    @Override
    public void trafficFusion(SessionContext sessionContext, Rtb25Request rtb25Request, DspEpObj dspEpObj) {
        if (MapUtil.isEmpty(sessionContext.getDspFusionTarget()) || !sessionContext.getDspFusionTarget().containsKey(dspEpObj.getDspId())) {
            return;
        }
        Map<String, FusionProvider.TargetTraffic> impTargetTraffic = sessionContext.getDspFusionTarget().get(dspEpObj.getDspId());
        if (MapUtil.isEmpty(impTargetTraffic)) {
            return;
        }
        for (Imp imp : rtb25Request.imp) {
            FusionProvider.TargetTraffic traffic = impTargetTraffic.get(imp.id);
            if (traffic == null) {
                continue;
            }
            if (StrUtil.isNotBlank(traffic.getTarAppId())) {
                rtb25Request.app.id = traffic.getTarAppId();
            }
            if (StrUtil.isNotBlank(traffic.getTarPkg())) {
                rtb25Request.app.bundle = traffic.getTarPkg();
            }
            if (StrUtil.isNotBlank(traffic.getTarTagId())) {
                imp.tagid = traffic.getTarTagId();
            }
        }
        log.info("dsp name:{}, id: {}, ep: {}, after fusion request:{}", dspEpObj.getName(), dspEpObj.getDspId(), dspEpObj.getDspEpId(), JSON.toJSONString(rtb25Request));
    }

    @Override
    public Rtb25Response unifiedResp2ProtocolResp(SessionContext sessionContext, UnifiedResponse unifiedResp) throws Exception {
        Rtb25Response rtb25Response = new Rtb25Response();
        rtb25Response.id = sessionContext.getUnifiedRequest().getMediaReqId();
        rtb25Response.bidid = unifiedResp.getBidId();
        rtb25Response.cur = unifiedResp.getCur();
        rtb25Response.customdata = unifiedResp.getCustomdata();
        rtb25Response.nbr = unifiedResp.getNbr();
        Integer settlementType = sessionContext.getSspEp().getSettlementType();
        rtb25Response.seatbid = unifiedResp.getSeatbids() != null ?
                unifiedResp.getSeatbids().stream()
                        .map(a -> new com.iflytek.traffic.protocol.rtb25.response.SeatBid(a, settlementType))
                        .collect(Collectors.toList()) : null;
        if (CollUtil.isNotEmpty(rtb25Response.seatbid) &&
                CollUtil.isNotEmpty(rtb25Response.seatbid.get(0).bid)) {
            sessionContext.setBidDealId(rtb25Response.seatbid.get(0).bid.get(0).dealid);
        }
        return rtb25Response;
    }

    @Override
    public UnifiedResponse respBody2UnifiedResp(int code, byte[] body, DspEpObj dspEpObj) throws Exception {
        UnifiedResponse unifiedResponse = new UnifiedResponse();
        unifiedResponse.setDspEpObj(dspEpObj);
        unifiedResponse.setCode(code);
        if (code == 204) {
            unifiedResponse.setFill(false);
            return unifiedResponse;
        }
        Rtb25Response rtb25Response;
        try {
            if (log.isInfoEnabled()) {
                log.info("dsp name:{}, id: {}, ep: {}, resp:{}", dspEpObj.getName(), dspEpObj.getDspId(), dspEpObj.getDspEpId(), StrUtil.str(body, "UTF-8"));
            }
            rtb25Response = JSON.parseObject(body, Rtb25Response.class);
            if (rtb25Response == null) {
                log.error("response parse null. dsp name:{}, id: {}, ep: {}, resp:{}", dspEpObj.getName(), dspEpObj.getDspId(), dspEpObj.getDspEpId(), StrUtil.str(body, "UTF-8"));
                unifiedResponse.setFill(false);
                return unifiedResponse;
            }
        } catch (Exception e) {
            log.warn("dsp:{} parse response error:{}", dspEpObj.getName(), e.getMessage(), e);
            log.warn("dsp:{} parse response error body:{}", dspEpObj.getName(), StrUtil.str(body, "UTF-8"));
            unifiedResponse.setParseError(true);
            return unifiedResponse;
        }
        unifiedResponse.setId(rtb25Response.id);
        unifiedResponse.setCur(rtb25Response.cur);
        unifiedResponse.setCustomdata(rtb25Response.customdata);
        unifiedResponse.setNbr(rtb25Response.nbr);
        unifiedResponse.setBidId(rtb25Response.bidid);
        if (rtb25Response.seatbid != null && !rtb25Response.seatbid.isEmpty()) {
            List<SeatBid> seatbids = new ArrayList<>();
            for (com.iflytek.traffic.protocol.rtb25.response.SeatBid seatBid : rtb25Response.seatbid) {
                SeatBid sb = new SeatBid(seatBid);
                seatbids.add(sb);
            }
            unifiedResponse.setSeatbids(seatbids);
        }

        return unifiedResponse;
    }

    private Map<String, Impression> reqBody2UnifiedReqImp(List<Imp> impList) {
        Map<String, Impression> imps = new HashMap<>();
        // 转化imp信息
        if (impList != null && !impList.isEmpty()) {
            for (Imp imp : impList) {
                Impression i = new Impression(imp);
                if (i.getImpId() == null) {
                    i.setImpId(UUID.randomUUID().toString());
                }
                imps.put(i.getImpId(), i);
            }
        }
        return imps;
    }

    private Site reqBody2UnifiedReqSite(com.iflytek.traffic.protocol.rtb25.request.Site site) {
        if (site != null) {
            return new Site(site);
        }
        return null;
    }

    private App reqBody2UnifiedReqApp(com.iflytek.traffic.protocol.rtb25.request.App app) {
        if (app != null) {
            return new App(app);
        }
        return null;
    }

    private Device reqBody2UnifiedReqDevice(com.iflytek.traffic.protocol.rtb25.request.Device device) {
        if (device != null) {
            return new Device(device);
        }
        return null;
    }

    private User reqBody2UnifiedReqUser(com.iflytek.traffic.protocol.rtb25.request.User user) {
        if (user != null) {
            return new User(user);
        }
        return null;
    }

    private Source reqBody2UnifiedReqSource(com.iflytek.traffic.protocol.rtb25.request.Source source) {
        if (source != null) {
            return new Source(source);
        }
        return null;
    }

    private Regs reqBody2UnifiedReqRegs(com.iflytek.traffic.protocol.rtb25.request.Regs regs) {
        if (regs != null) {
            return new Regs(regs);
        }
        return null;
    }

    private List<Imp> unifiedReq2ProtocolReqImp(Map<String, Impression> imps, Integer dspId, Integer dspEpId,
                                                String adxAppId, Integer sspId, Integer sspEpId) {
        if (imps == null || imps.isEmpty()) {
            return null;
        }
        List<Imp> impList = new ArrayList<>();
        for (Impression i : imps.values()) {
            Imp imp = new Imp(i);
            String adxSlotId = i.getTagId();
            float profit = getDspProfit(dspId, dspEpId, adxSlotId, adxAppId, sspId, sspEpId);
            imp.bidfloor = imp.bidfloor / (1 - profit);
            i.getDspFloorPrice().put(dspEpId, (long)(imp.bidfloor * Constants.PRICE_MULTIPLY_MILLIONS));
            i.getDspProfitRatio().put(dspEpId, profit);
            impList.add(imp);
        }
        return impList;
    }

    private com.iflytek.traffic.protocol.rtb25.request.Site unifiedReq2ProtocolReqSite(Site site) {
        if (site != null) {
            return new com.iflytek.traffic.protocol.rtb25.request.Site(site);
        }
        return null;
    }

    private com.iflytek.traffic.protocol.rtb25.request.App unifiedReq2ProtocolReqApp(App app) {
        if (app != null) {
            return new com.iflytek.traffic.protocol.rtb25.request.App(app);
        }
        return null;
    }

    private com.iflytek.traffic.protocol.rtb25.request.Device unifiedReq2ProtocolReqDevice(Device device) {
        if (device != null) {
            return new com.iflytek.traffic.protocol.rtb25.request.Device(device);
        }
        return null;
    }

    private com.iflytek.traffic.protocol.rtb25.request.User unifiedReq2ProtocolReqUser(User user) {
        if (user != null) {
            return new com.iflytek.traffic.protocol.rtb25.request.User(user);
        }
        return null;
    }

    private com.iflytek.traffic.protocol.rtb25.request.Source unifiedReq2ProtocolReqSource(Source source, String reqId) {
        if (source != null) {
            com.iflytek.traffic.protocol.rtb25.request.Source rtbSource = new com.iflytek.traffic.protocol.rtb25.request.Source(source);
            if (rtbSource.ext != null && rtbSource.ext.schain != null) {
                List<com.iflytek.traffic.protocol.rtb25.request.Source.Node> nodes = rtbSource.ext.schain.nodes;
                if (nodes != null && !nodes.isEmpty()) {
                    com.iflytek.traffic.protocol.rtb25.request.Source.Node node = new com.iflytek.traffic.protocol.rtb25.request.Source.Node();
                    node.hp = 1;
                    node.rid = reqId;
                    node.asi = "growone.sg";
                    node.sid = "22ba586bca7ae684c8e5aedb73a3125c";
                    nodes.add(node);
                }
            }
            return rtbSource;
        }
        return null;
    }

    private com.iflytek.traffic.protocol.rtb25.request.Regs unifiedReq2ProtocolReqRegs(Regs regs) {
        if (regs != null) {
            return new com.iflytek.traffic.protocol.rtb25.request.Regs(regs);
        }
        return null;
    }

    public Class<Rtb25Request> getTypeT() {
        return Rtb25Request.class;
    }

    public Class<Rtb25Response> getTypeR() {
        return Rtb25Response.class;
    }
}
