package com.iflytek.traffic.protocol.rtb25.request;

import java.util.ArrayList;
import java.util.List;

public class Site {

    public String id;

    public String name;

    public String domain;

    public List<String> cat;

    public List<String> sectioncat;

    public List<String> pagecat;

    public String page;

    public String ref;

    public String search;

    public Integer mobile;

    public Integer privacypolicy;

    public Publisher publisher;

    public Content content;

    public String keywords;

    public Site(){}

    public Site(com.iflytek.traffic.session.request.Site s) {
        this.id = s.getId();
        this.name = s.getName();
        this.domain = s.getDomain();
        this.cat = s.getCat() != null ? new ArrayList<>(s.getCat()) : null;
        this.sectioncat = s.getSectioncat() != null ? new ArrayList<>(s.getSectioncat()) : null;
        this.pagecat = s.getPagecat() != null ? new ArrayList<>(s.getPagecat()) : null;
        this.page = s.getPage();
        this.ref = s.getRef();
        this.search = s.getSearch();
        this.mobile = s.getMobile();
        this.privacypolicy = s.getPrivacypolicy();
        this.publisher = s.getPublisher() != null ? new Publisher(s.getPublisher()) : null;
        this.content = s.getContent() != null ? new Content(s.getContent()) : null;
        this.keywords = s.getKeywords();
    }

}
