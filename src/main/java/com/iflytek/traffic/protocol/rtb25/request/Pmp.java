package com.iflytek.traffic.protocol.rtb25.request;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;
import java.util.stream.Collectors;

public class Pmp {

    @JSONField(name = "private_auction")
    public Integer privateauction = 0;

    public List<Deal> deals;

    public Pmp(){}

    public Pmp(com.iflytek.traffic.session.request.Pmp p) {
        this.privateauction = p.getPrivateauction();
        this.deals = p.getDeals() != null ?
                p.getDeals().stream().map(Deal::new).collect(Collectors.toList()) : null;
    }
}
