package com.iflytek.traffic.protocol.rtb25.request;

public class Regs {

    public Integer coppa;

    public Ext ext;

    public Regs(){}

    public Regs(com.iflytek.traffic.session.request.Regs r) {
        this.coppa = r.getCoppa();

        if (r.getExt() != null) {
            this.ext = new Ext();
            this.ext.gdpr = r.getExt().getGdpr();
        }
    }

    public static class Ext{

        public Integer gdpr;
    }

}
