package com.iflytek.traffic.protocol.rtb25.request;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class Video {

    public List<String> mimes;

    public Integer minduration;

    public Integer maxduration;

    public List<Integer> protocols;

    public Integer protocol;

    public Integer w;

    public Integer h;

    public Integer startdelay;

    public Integer placement;

    public Integer linearity;

    public Integer skip;

    public Integer skipmin;

    public Integer skipafter;

    public Integer sequence;

    public List<Integer> battr;

    public Integer maxextended;

    public Integer minbitrate;

    public Integer maxbitrate;

    public Integer boxingallowed;

    public List<Integer> playbackmethod;

    public Integer playbackend;

    public List<Integer> delivery;

    public Integer pos;

    public List<Banner> companionad;

    public List<Integer> api;

    public List<Integer> companiontype;

    public VideoExt ext;

    public Video(){}

    public static class VideoExt {

        public Integer rewarded;
        public VideoExt(){}

    }

    public Video(com.iflytek.traffic.session.request.Video v) {
        this.mimes = v.getMimes() != null ? new ArrayList<>(v.getMimes()) : null;
        this.minduration = v.getMinduration();
        this.maxduration = v.getMaxduration();
        this.protocols = v.getProtocols() != null ? new ArrayList<>(v.getProtocols()) : null;
        this.protocol = v.getProtocol();
        this.w = v.getW();
        this.h = v.getH();
        this.startdelay = v.getStartdelay();
        this.placement = v.getPlacement();
        this.linearity = v.getLinearity();
        this.skip = v.getSkip();
        this.skipmin = v.getSkipmin();
        this.skipafter = v.getSkipafter();
        this.sequence = v.getSequence();
        this.battr = v.getBattr() != null ? new ArrayList<>(v.getBattr()) : null;
        this.maxextended = v.getMaxextended();
        this.minbitrate = v.getMinbitrate();
        this.maxbitrate = v.getMaxbitrate();
        this.boxingallowed = v.getBoxingallowed();
        this.playbackmethod = v.getPlaybackmethod() != null ? new ArrayList<>(v.getPlaybackmethod()) : null;
        this.playbackend = v.getPlaybackend();
        this.delivery = v.getDelivery() != null ? new ArrayList<>(v.getDelivery()) : null;
        this.pos = v.getPos();
        this.companionad = v.getCompanionad() != null ?
                v.getCompanionad().stream().map(Banner::new).collect(Collectors.toList()) : null;
        this.api = v.getApi() != null ? new ArrayList<>(v.getApi()) : null;
        this.companiontype = v.getCompaniontype() != null ? new ArrayList<>(v.getCompaniontype()) : null;
        if (v.getExt() != null) {
            this.ext = new VideoExt();
            this.ext.rewarded = v.getExt().getRewarded();
        }
    }


}
