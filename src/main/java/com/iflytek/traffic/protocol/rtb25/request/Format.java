package com.iflytek.traffic.protocol.rtb25.request;

public class Format {

    public Integer w;

    public Integer h;

    public Integer wratio;

    public Integer hratio;

    public Integer wmin;

    public Format(){}

    public Format(com.iflytek.traffic.session.request.Format f) {
        this.w = f.getW();
        this.h = f.getH();
        this.wratio = f.getWratio();
        this.hratio = f.getHratio();
        this.wmin = f.getWmin();
    }

}
