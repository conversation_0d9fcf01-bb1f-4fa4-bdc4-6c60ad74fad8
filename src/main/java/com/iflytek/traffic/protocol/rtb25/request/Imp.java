package com.iflytek.traffic.protocol.rtb25.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.iflytek.traffic.protocol.rtb25.Native;
import com.iflytek.traffic.session.request.Impression;
import com.iflytek.traffic.util.constant.Constants;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class Imp {

    public String id;

    public List<Metric> metric;

    public Video video;

    public Banner banner;

    @JSONField(name = "native")
    public Native native1;

    public Audio audio;

    public Pmp pmp;

    public Integer instl = 0;

    public Float bidfloor = 0.0f;

    public Integer secure;

    public String bidfloorcur = "USD";

    public String displaymanager;

    public String displaymanagerver;

    public String tagid;

    public Integer clickbrowser;

    public List<String> iframebuster;

    public Integer exp;

    public Ext ext;

    public static class Ext{
        public Integer reward;

        public Integer rewarded;

        public Integer deeplink;

        public Integer fallback;

        public List<String> packageList;

        public Integer adType;

        public Ext(){}

        public Ext(Impression.Ext ext) {
            reward = ext.getReward();
            deeplink = ext.getDeeplink();
            fallback = ext.getFallback();
            packageList = ext.getPackageList();
            adType = ext.getAdtype();
        }
    }

    public Imp() {}

    public Imp(Impression i) {
        this.id = i.getImpId();
        this.metric = i.getMetric() != null ?
                i.getMetric().stream().map(Metric::new).collect(Collectors.toList()) : null;
        this.video = i.getVideo() != null ? new Video(i.getVideo()) : null;
        this.banner = i.getBanner() != null ? new Banner(i.getBanner()) : null;
        this.native1 = i.getNative1();
        this.audio = i.getAudio() != null ? new Audio(i.getAudio()) : null;
        this.pmp = i.getPmp() != null ? new Pmp(i.getPmp()) : null;
        this.instl = i.getInstl();
        if (i.getBidfloor() != null) {
            this.bidfloor = (float) ((double)i.getBidfloor() / Constants.PRICE_MULTIPLY_MILLIONS);
        }
        this.secure = i.getSecure();
        this.bidfloorcur = i.getBidfloorcur();
        this.displaymanager = i.getDisplaymanager();
        this.displaymanagerver = i.getDisplaymanagerver();
        this.tagid = i.getTagId();
        this.clickbrowser = i.getClickbrowser();
        this.iframebuster = i.getIframebuster() != null ? new ArrayList<>(i.getIframebuster()) : null;
        this.exp = i.getExp();
        this.ext = i.getExt() != null ? new Ext(i.getExt()) : null;
    }

}
