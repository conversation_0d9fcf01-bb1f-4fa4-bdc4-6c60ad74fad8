package com.iflytek.traffic.protocol;

import com.iflytek.traffic.util.SpringContextHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
@DependsOn("springContextHelper")
public class AllProtocolConfig implements InitializingBean {

    private final Map<String, ProtocolParser> protocolParserMap = new HashMap<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        initializeAdapterBeanMap();
    }

    private void initializeAdapterBeanMap() {
        Map<String, Object> beansWithAnnotation = SpringContextHelper.getBeansWithAnnotation(Protocol.class);

        for (String beanName : beansWithAnnotation.keySet()) {
            ProtocolParser protocolParser = SpringContextHelper.getBean(beanName, ProtocolParser.class);
            Protocol annotation = SpringContextHelper.findAnnotationOnBean(beanName, Protocol.class);
            String protocolName = annotation.name();
            protocolParserMap.put(protocolName, protocolParser);
        }
    }

    public ProtocolParser getProtocolParser(String name) {
        return protocolParserMap.get(name);
    }

}
