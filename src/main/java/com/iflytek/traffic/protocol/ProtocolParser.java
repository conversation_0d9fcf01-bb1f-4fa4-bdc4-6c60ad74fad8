package com.iflytek.traffic.protocol;

import com.iflytek.traffic.data.entity.SspSlotInfo;
import com.iflytek.traffic.data.provider.ProfitProvider;
import com.iflytek.traffic.data.provider.SspSlotProvider;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.request.UnifiedRequest;
import com.iflytek.traffic.session.response.UnifiedResponse;
import com.iflytek.traffic.ssp.SspEp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * @date 2025/05/08 09:30
 * @description 协议解析，T：请求对象，R：响应对象
 */
@Slf4j
public abstract class ProtocolParser<T, R> {

    @Value("${dsp.profit.default:0.0}")
    private float defaultProfit;

    @Autowired
    private ProfitProvider profitProvider;

    @Autowired
    private SspSlotProvider sspSlotProvider;

    /**
     * 外部请求body转内部请求对象，用于解析下游SSP请求
     *
     * @param body 外部请求body
     *  @return 内部请求
     */
    public abstract UnifiedRequest reqBody2UnifiedReq(byte[] body, SspEp sspEp) throws Exception;

    /**
     * 内部请求转外部请求对象，用于请求上游DSP
     *
     * @param unifiedReq 内部请求
     * @return 外部请求对象
     */
    public abstract T unifiedReq2ProtocolReq(SessionContext sessionContext, UnifiedRequest unifiedReq, DspEpObj dspEpObj) throws Exception;

    public abstract void trafficFusion(SessionContext sessionContext, T t, DspEpObj dspEpObj);

    /**
     * 内部响应转外部响应对象，用于响应下游SSP
     *
     * @param unifiedResp 内部响应
     * @return 外部响应对象
     */
    public abstract R unifiedResp2ProtocolResp(SessionContext sessionContext, UnifiedResponse unifiedResp) throws Exception;

    /**
     * 外部响应body转内部响应对象，用于解析上游DSP响应
     *
     * @param body 外部响应body
     * @return 内部响应对象
     */
    public abstract UnifiedResponse respBody2UnifiedResp(int code, byte[] body, DspEpObj dspEpObj) throws Exception;

    public abstract Class<T> getTypeT();

    public abstract Class<R> getTypeR();

    public float getDspProfit(Integer dspId, Integer dspEpId, String adxSlotId, String adxAppId, Integer sspId, Integer sspEpId) {
        float profit = defaultProfit;
        try {
            profit = profitProvider.getProfitRatio(dspId, dspEpId, adxSlotId, adxAppId, sspId, sspEpId);
            if (profit > 0.99F) {
                profit = 0.99F;
            }
            if (profit < 0.0F) {
                profit = 0.0F;
            }
            if (log.isDebugEnabled()) {
                log.debug("profit = {}, dspId = {}, dspEpId = {}, sspId = {}, sspEpId = {}",
                        profit, dspId, dspEpId, sspId, sspEpId);
            }
        } catch (Exception e) {
            // 日志降级
            log.info("get profit error: {}", e.getMessage());
        }
        return profit;
    }

    public SspSlotInfo getSspSlotInfo(int sspId, String tagId) {
        return sspSlotProvider.getSspSlotInfoByTag(sspId, tagId);
    }
}
