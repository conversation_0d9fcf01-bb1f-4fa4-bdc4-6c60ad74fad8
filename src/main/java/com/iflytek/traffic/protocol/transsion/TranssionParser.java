package com.iflytek.traffic.protocol.transsion;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.iflytek.traffic.data.provider.FusionProvider;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.protocol.Protocol;
import com.iflytek.traffic.protocol.ProtocolParser;
import com.iflytek.traffic.protocol.rtb25.Native;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.asset.Asset;
import com.iflytek.traffic.session.request.*;
import com.iflytek.traffic.session.response.Bid;
import com.iflytek.traffic.session.response.SeatBid;
import com.iflytek.traffic.session.response.UnifiedResponse;
import com.iflytek.traffic.ssp.SspEp;
import com.iflytek.traffic.util.Util;
import com.iflytek.traffic.util.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @datetime 2025/6/24 11:11
 */
@Slf4j
@Protocol(name = "transsion")
@Component
public class TranssionParser extends ProtocolParser<TranssionRequest, TranssionResponse> {


	@Override
	public UnifiedRequest reqBody2UnifiedReq(byte[] body, SspEp sspEp) throws Exception {
		UnifiedRequest unifiedRequest = new UnifiedRequest();
		TranssionRequest bidrequest;
		try {
			bidrequest = JSON.parseObject(body, TranssionRequest.class);
			if (log.isInfoEnabled()) {
				log.info("ssp:{}, ep:{}, recv req:{}", sspEp.getSspName(), sspEp.getSspEpId(), JSON.toJSONString(bidrequest));
			}
		} catch (Exception e) {
			log.warn("ssp:{}, ep:{}, parse err:{}", sspEp.getSspName(), sspEp.getSspEpId(), Util.genStackInfo(e), e);
			unifiedRequest.setNeedForward(false);
			return unifiedRequest;
		}
		unifiedRequest.setMediaReqId(bidrequest.id);
		Map<String, Impression> imps = transsionImpList2UnifiedReqImpList(bidrequest.imp);
		if (!imps.isEmpty()) {
			unifiedRequest.setImps(imps);
		}
		unifiedRequest.setApp(transsionApp2UnifiedReqApp(bidrequest.app));
		unifiedRequest.setSite(transsionSite2UnifiedReqSite(bidrequest.site));
		unifiedRequest.setDevice(transsionDevice2UnifiedReqDevice(bidrequest.device));
		unifiedRequest.setUser(transsionUser2UnifiedReqUser(bidrequest.user));
		unifiedRequest.setRegs(transsionRegs2UnifiedReqRegs(bidrequest.regs));
		unifiedRequest.setSource(transsionSource2UnifiedReqSource(bidrequest.source)); // todo source单独抽出来
		unifiedRequest.setAt(bidrequest.at);
		unifiedRequest.setTmax(bidrequest.tmax);
		unifiedRequest.setBcat(bidrequest.bcat != null ? new ArrayList<>(bidrequest.bcat) : null);
		unifiedRequest.setBadv(bidrequest.badv != null ? new ArrayList<>(bidrequest.badv) : null);
		unifiedRequest.setBapp(bidrequest.bapp != null ? new ArrayList<>(bidrequest.bapp) : null);
		unifiedRequest.setCur(bidrequest.cur != null ? new ArrayList<>(bidrequest.cur) : null);
		unifiedRequest.setWlang(bidrequest.wlang != null ? new ArrayList<>(bidrequest.wlang) : null);

		return unifiedRequest;
	}

	@Override
	public TranssionRequest unifiedReq2ProtocolReq(SessionContext sessionContext, UnifiedRequest unifiedReq, DspEpObj dspEpObj) throws Exception {
		return null;
	}

	@Override
	public void trafficFusion(SessionContext sessionContext, TranssionRequest transsionRequest, DspEpObj dspEpObj) {
		if (MapUtil.isEmpty(sessionContext.getDspFusionTarget()) || !sessionContext.getDspFusionTarget().containsKey(dspEpObj.getDspId())) {
			return;
		}
		Map<String, FusionProvider.TargetTraffic> impTargetTraffic = sessionContext.getDspFusionTarget().get(dspEpObj.getDspId());
		if (MapUtil.isEmpty(impTargetTraffic)) {
			return;
		}
        for (TranssionRequest.Imp imp : transsionRequest.imp) {
			FusionProvider.TargetTraffic traffic = impTargetTraffic.get(imp.id);
			if (traffic == null) {
				continue;
			}
			if (StrUtil.isNotBlank(traffic.getTarAppId())) {
				transsionRequest.app.id = traffic.getTarAppId();
			}
			if (StrUtil.isNotBlank(traffic.getTarPkg())) {
				transsionRequest.app.bundle = traffic.getTarPkg();
			}
			if (StrUtil.isNotBlank(traffic.getTarTagId())) {
				imp.tagid = traffic.getTarTagId();
			}
		}
		log.info("dsp name:{}, id: {}, ep: {}, after fusion request:{}", dspEpObj.getName(), dspEpObj.getDspId(), dspEpObj.getDspEpId(), JSON.toJSONString(transsionRequest));
	}

	@Override
	public TranssionResponse unifiedResp2ProtocolResp(SessionContext sessionContext, UnifiedResponse unifiedResp) throws Exception {
		TranssionResponse transsionResponse = new TranssionResponse();
		transsionResponse.id = sessionContext.getUnifiedRequest().getMediaReqId();
		transsionResponse.bidid = unifiedResp.getBidId();
		transsionResponse.nbr = unifiedResp.getNbr();
		transsionResponse.cur = unifiedResp.getCur();
		transsionResponse.seatbid = unifiedRespSeatBidList2TranssionSeatBidList(unifiedResp.getSeatbids(), sessionContext);

		return transsionResponse;
	}

	@Override
	public UnifiedResponse respBody2UnifiedResp(int code, byte[] body, DspEpObj dspEpObj) throws Exception {
		return null;
	}

	@Override
	public Class<TranssionRequest> getTypeT() {
		return TranssionRequest.class;
	}

	@Override
	public Class<TranssionResponse> getTypeR() {
		return TranssionResponse.class;
	}

	private Map<String, Impression> transsionImpList2UnifiedReqImpList(ArrayList<TranssionRequest.Imp> impList) {
		Map<String, Impression> imps = new HashMap<>();
		// 转化imp信息
		if (impList != null && !impList.isEmpty()) {
            for (TranssionRequest.Imp imp : impList) {
				Impression i = transsionImp2UnifiedReqImp(imp);
				imps.put(i.getImpId(), i);
			}
		}
		return imps;
	}

    private Impression transsionImp2UnifiedReqImp(TranssionRequest.Imp imp) {
		if (imp == null) {
			return null;
		}
		Impression impression = new Impression();
		impression.setImpId(imp.id);

		impression.setBidfloor((long) (imp.bidfloor * Constants.PRICE_MULTIPLY_MILLIONS));
		impression.setBidfloorcur(imp.bidfloorcur);
		impression.setTagId(imp.tagid);

		impression.setInstl(imp.instl);
		impression.setSecure(imp.secure);
		impression.setDisplaymanager(imp.displaymanager);
		impression.setDisplaymanagerver(imp.displaymanagerver);

		if (imp.ext != null) {
			impression.setExt(transsionImpExt2UnifiedReqImpExt(imp.ext));
		}
		// 广告素材
		if (imp.banner != null) {
			if (impression.getAdType() == Asset.AdType.UNKNOWN) {
				impression.setAdType(Asset.AdType.SUPPER_BANNER);
			}
			impression.setBanner(transsionBanner2UnifiedReqBanner(imp.banner));
			impression.getAdUnitSize().add(imp.banner.w + "*" + imp.banner.h);
			impression.setRequestAdType(Asset.AdType.SUPPER_BANNER);
		}
		if (imp.native1 != null) {
			if (impression.getAdType() == Asset.AdType.UNKNOWN) {
				impression.setAdType(Asset.AdType.SUPPER_NATIVE);
				if (impression.getExt() != null && impression.getExt().getAdtype() != null && impression.getExt().getAdtype() == Constants.AD_TYPE_ICON) {
					impression.setAdType(Asset.AdType.SUPPER_ICON);
				}
			}
			impression.setNative1(transsionNative2UnifiedReqNative(imp.native1, impression));
			impression.setRequestAdType(Asset.AdType.SUPPER_NATIVE);
		}

		return impression;
	}

	private Impression.Ext transsionImpExt2UnifiedReqImpExt(TranssionRequest.Imp.Ext transsionExt) {
		if (transsionExt == null) {
			return null;
		}
		Impression.Ext ext = new Impression.Ext();
		if (transsionExt.codeType != null && transsionExt.codeType == 6) {
			ext.setAdtype(Constants.AD_TYPE_ICON);
		}

		return ext;
	}
	
	private Site transsionSite2UnifiedReqSite(TranssionRequest.Site transsionSite) {
		if (transsionSite == null) {
			return null;
		}
		Site site = new Site();
		site.setId(transsionSite.id);
		site.setName(transsionSite.name);
		site.setDomain(transsionSite.domain);
		site.setCat(transsionSite.cat != null ? new ArrayList<>(transsionSite.cat) : null);
		site.setMobile(transsionSite.mobile);
		return site;
	}

	private Banner transsionBanner2UnifiedReqBanner(TranssionRequest.Imp.Banner transsionBanner) {
		if (transsionBanner == null) {
			return null;
		}
		Banner banner = new Banner();

		if (transsionBanner.format != null) {
			banner.setFormat(new ArrayList<>());
            for (TranssionRequest.Imp.Banner.Format transsionFormat : transsionBanner.format) {
				Format format = transsionFormat2UnifiedReqFormat(transsionFormat);
				banner.getFormat().add(format);
			}
		}

		banner.setBattr(transsionBanner.battr != null ? new ArrayList<>(transsionBanner.battr) : null);
		banner.setMimes(transsionBanner.mimes != null ? new ArrayList<>(transsionBanner.mimes) : null);
		banner.setApi(transsionBanner.api != null ? new ArrayList<>(transsionBanner.api) : null);
		banner.setW(transsionBanner.w);
		banner.setH(transsionBanner.h);
		banner.setPos(transsionBanner.pos);
		banner.setId(transsionBanner.id);

		return banner;
	}

    private Format transsionFormat2UnifiedReqFormat(TranssionRequest.Imp.Banner.Format transsionFormat) {
		if (transsionFormat == null) {
			return null;
		}
		Format format = new Format();
		format.setW(transsionFormat.w);
		format.setH(transsionFormat.h);
		return format;
	}

    private Native transsionNative2UnifiedReqNative(TranssionRequest.Imp.Native transsionNative, Impression impression) {
		if (transsionNative == null) {
			return null;
		}

		Native native1 = new Native();
		native1.ver = transsionNative.ver;

		if (transsionNative.request != null) {
			Native.Request request = new Native.Request();
			request.native1 = convertRequestTranssionToUnified(transsionNative.request, impression);
			native1.request = JSON.toJSONString(request);
		}

		return native1;
	}

	private Native.NativeRequest convertRequestTranssionToUnified(String requestJson, Impression impression) {
		TranssionRequest.Imp.Native.Request aRequest = JSON.parseObject(requestJson, TranssionRequest.Imp.Native.Request.class);
        if (aRequest == null || aRequest.native1 == null) {
			return null;
		}

		TranssionRequest.Imp.Native.Request.RequestNative requestNative = aRequest.native1;

		Native.NativeRequest native1Request = new Native.NativeRequest();
		native1Request.ver = requestNative.ver;

		if (requestNative.assets != null) {
			native1Request.assets = requestNative.assets.stream()
					.map(this::convertAssettranssionToUnified)
					.collect(Collectors.toList());
		}

		int adSizeType = Constants.NATIVE_IMG_TYPE_IMG;
		if (impression.getAdType() == Asset.AdType.SUPPER_ICON || impression.getAdType() == Asset.AdType.SUPPER_PUSH) {
			adSizeType = Constants.NATIVE_IMG_TYPE_ICON;
		}
		for (Native.NativeRequest.Asset asset : native1Request.assets) {
			if (asset.img != null && asset.img.type == adSizeType) {
				int w=0,h=0;
				if (asset.img.w != null) {
					w = asset.img.w;
				} else if (asset.img.wmin != null) {
					w = asset.img.wmin;
				}
				if (asset.img.h != null) {
					h = asset.img.h;
				} else if (asset.img.hmin != null) {
					h = asset.img.hmin;
				}
				impression.getAdUnitSize().add(w + "*" + h);
				break;
			}
		}

		return native1Request;
	}

    private Native.NativeRequest.Asset convertAssettranssionToUnified(TranssionRequest.Imp.Native.Request.Asset aAsset) {
		if (aAsset == null) {
			return null;
		}
		Native.NativeRequest.Asset bAsset = new Native.NativeRequest.Asset();
		bAsset.id = aAsset.id;
		bAsset.required = aAsset.required;

		if (aAsset.title != null) {
			Native.NativeRequest.Title bTitle = new Native.NativeRequest.Title();
			bTitle.len = aAsset.title.len;
			bAsset.title = bTitle;
		}
		if (aAsset.img != null) {
			Native.NativeRequest.Image bImage = new Native.NativeRequest.Image();
			bImage.type = aAsset.img.type;
			bImage.w = aAsset.img.wmin;
			bImage.h = aAsset.img.hmin;
			bImage.mimes = aAsset.img.mimes != null ? new ArrayList<>(aAsset.img.mimes) : null;
			bAsset.img = bImage;
		}

		if (aAsset.data != null) {
			Native.NativeRequest.Data bData = new Native.NativeRequest.Data();
			bData.type = aAsset.data.type;
			bData.len = aAsset.data.len;
			bAsset.data = bData;
		}

		return bAsset;
	}

	private App transsionApp2UnifiedReqApp(TranssionRequest.App transsionApp) {
		if (transsionApp == null) {
			return null;
		}

		App unifiedApp = new App();

		unifiedApp.setId(transsionApp.id);
		unifiedApp.setName(transsionApp.name);
		unifiedApp.setBundle(transsionApp.bundle);
		unifiedApp.setVer(transsionApp.ver);
		unifiedApp.setStoreurl(transsionApp.storeurl);
		if (transsionApp.publisher != null) {
			unifiedApp.setPublisher(convertPublisherTranssionToUnified(transsionApp.publisher));
		}
		if (transsionApp.cat != null) {
			unifiedApp.setCat(new ArrayList<>(transsionApp.cat));
		}

		return unifiedApp;
	}

	private Publisher convertPublisherTranssionToUnified(TranssionRequest.Publisher transsionPublisher) {
		if (transsionPublisher == null) {
			return null;
		}
		Publisher publisher = new Publisher();

		publisher.setId(transsionPublisher.id);
		publisher.setName(transsionPublisher.name);
		publisher.setDomain(transsionPublisher.domain);

		return publisher;
	}

	private Device transsionDevice2UnifiedReqDevice(TranssionRequest.Device transsionDevice) {
		if (transsionDevice == null) {
			return null;
		}
		Device device = new Device();

		device.setUa(transsionDevice.ua);
		device.setIfa(transsionDevice.ifa);
		device.setDpidmd5(transsionDevice.dpidmd5);
		device.setIp(transsionDevice.ip);
		device.setIpv6(transsionDevice.ipv6);
		device.setMake(transsionDevice.make);
		device.setModel(transsionDevice.model);
		device.setDnt(transsionDevice.dnt);
		device.setLmt(transsionDevice.lmt);
		device.setLanguage(transsionDevice.language);
		device.setCarrierOrigin(transsionDevice.carrier);
		device.setOsOrigin(transsionDevice.os);
		device.setOsv(transsionDevice.osv);
		device.setHwv(transsionDevice.hwv);
		device.setW(transsionDevice.w);
		device.setH(transsionDevice.h);
		device.setMccmnc(transsionDevice.mccmnc);
		device.setPxratio(transsionDevice.pxratio);
		if (transsionDevice.os != null) {
			String os = transsionDevice.os.toLowerCase().trim();
			if (os.contains("android")) {
				device.setOs(Device.Os.ANDROID);
			} else if (os.contains("ios")) {
				device.setOs(Device.Os.IOS);
			} else if (os.contains("windows")) {
				device.setOs(Device.Os.WINDOWS);
			}
		}
		device.setDevicetypeOrigin(transsionDevice.devicetype);
		if (transsionDevice.devicetype != null) {
			switch (transsionDevice.devicetype) {
				case 1:
					device.setDeviceType(Device.DeviceType.PAD);
					break;
				case 2:
					device.setDeviceType(Device.DeviceType.PC);
					break;
				case 3:
					device.setDeviceType(Device.DeviceType.TV);
					break;
				case 4:
					device.setDeviceType(Device.DeviceType.PHONE);
					break;
				case 5:
					device.setDeviceType(Device.DeviceType.PAD);
					break;
				case 7:
					device.setDeviceType(Device.DeviceType.TV);
					break;
				default:
					device.setDeviceType(Device.DeviceType.UNKNOWN);
					break;
			}
		}

		device.setConnectiontype(transsionDevice.connectiontype);
		if (transsionDevice.connectiontype != null) {
			switch (transsionDevice.connectiontype) {
				case 0:
					device.setNetwork(Device.Network.UNKNOWN);
					break;
				case 1:
					device.setNetwork(Device.Network.ETHERNET);
					break;
				case 2:
					device.setNetwork(Device.Network.WIFI);
					break;
				case 3:
					device.setNetwork(Device.Network.MOBUNKNOWN);
					break;
				case 4:
					device.setNetwork(Device.Network.M2G);
					break;
				case 5:
					device.setNetwork(Device.Network.M3G);
					break;
				case 6:
					device.setNetwork(Device.Network.M4G);
					break;
				default:
					device.setNetwork(Device.Network.UNKNOWN);
					break;
			}
		}
		device.setGeo(transsionGeo2UnifiedReqGeo(transsionDevice.geo));

		if (transsionDevice.ext != null) {
			if (transsionDevice.ext.idfamd5 != null) {
				Device.Ext ext = new Device.Ext();
				ext.setIdfamd5(transsionDevice.ext.idfamd5);
				device.setExt(ext);
			}
			if (StrUtil.isEmpty(transsionDevice.ifa)) {
				if (device.getOs() == Device.Os.ANDROID && transsionDevice.ext.gaid != null) {
					device.setIfa(transsionDevice.ext.gaid);
				} else if (device.getOs() == Device.Os.IOS && transsionDevice.ext.idfa != null) {
					device.setIfa(transsionDevice.ext.idfa);
				}
			}
		}

		return device;
	}
	
	private Geo transsionGeo2UnifiedReqGeo(TranssionRequest.Geo transsionGeo) {
		if (transsionGeo == null) {
			return null;
		}
		Geo geo = new Geo();
		geo.setLat(transsionGeo.lat);
		geo.setLon(transsionGeo.lon);
		geo.setType(transsionGeo.type);
		geo.setCountry(transsionGeo.country);
		geo.setRegion(transsionGeo.region);
		geo.setCity(transsionGeo.city);
		return geo;
	}

	private User transsionUser2UnifiedReqUser(TranssionRequest.User transsionUser) {
		if (transsionUser == null) {
			return null;
		}
		User user = new User();
		user.setId(transsionUser.id);
		if (transsionUser.data != null) {
			user.setData(new ArrayList<>());
			for (TranssionRequest.User.Data data : transsionUser.data) {
				user.getData().add(transsionData2UnifiedReqData(data));
			}
		}
		if (transsionUser.geo != null) {
			user.setGeo(transsionGeo2UnifiedReqGeo(transsionUser.geo));
		}

		return user;
	}

	private Data transsionData2UnifiedReqData(TranssionRequest.User.Data transsionData) {
		if (transsionData == null) {
			return null;
		}
		Data data = new Data();
		data.setId(transsionData.id);
		data.setName(transsionData.name);
		if (transsionData.segment != null) {
			data.setSegment(new ArrayList<>());
			for (TranssionRequest.User.Data.Segment segment1 : transsionData.segment) {
				data.getSegment().add(transsionSegment2UnifiedReqSegment(segment1));
			}
		}
		return data;
	}

	private Segment transsionSegment2UnifiedReqSegment(TranssionRequest.User.Data.Segment transsionSegment) {
		if (transsionSegment == null) {
			return null;
		}
		Segment segment = new Segment();
		segment.setId(transsionSegment.id);
		segment.setName(transsionSegment.name);
		segment.setValue(transsionSegment.value);
		return segment;
	}

	private Regs transsionRegs2UnifiedReqRegs(TranssionRequest.Regs transsionRegs) {
		if (transsionRegs == null) {
			return null;
		}
		Regs regs = new Regs();
		regs.setCoppa(transsionRegs.coppa);
		if (transsionRegs.gdpr != null) {
			Regs.Ext ext = new Regs.Ext();
			ext.setGdpr(transsionRegs.gdpr);
			regs.setExt(ext);
		}
		
		return regs;
	}

	private Source transsionSource2UnifiedReqSource(TranssionRequest.Source transsionSource) {
		if (transsionSource == null) {
			return null;
		}

		Source source = new Source();
		source.setFd(transsionSource.fd);
		source.setTid(transsionSource.tid);
		source.setPchain(transsionSource.pchain);
		source.setExt(convertSourceExt(transsionSource.ext));

		return source;
	}

	private Source.SourceExt convertSourceExt(TranssionRequest.Source.SourceExt transsionSourceExt) {
		if (transsionSourceExt == null) {
			return null;
		}

		Source.SourceExt ext = new Source.SourceExt();
		ext.setSchain(convertSChain(transsionSourceExt.schain));

		return ext;
	}

	private Source.SChain convertSChain(TranssionRequest.Source.SChain transsionSChain) {
		if (transsionSChain == null) {
			return null;
		}

		Source.SChain schainB = new Source.SChain();
		schainB.setVer(transsionSChain.ver);
		schainB.setComplete(transsionSChain.complete);
		schainB.setNodes(convertNodes(transsionSChain.nodes));

		return schainB;
	}

	private List<Source.Node> convertNodes(List<TranssionRequest.Source.Node> transsionNodes) {
		if (transsionNodes == null) {
			return null;
		}

		List<Source.Node> nodes = new ArrayList<>();
		for (TranssionRequest.Source.Node node : transsionNodes) {
			nodes.add(convertNode(node));
		}

		return nodes;
	}

	private Source.Node convertNode(TranssionRequest.Source.Node transsionNode) {
		if (transsionNode == null) {
			return null;
		}

		Source.Node node = new Source.Node();
		node.setAsi(transsionNode.asi);
		node.setHp(transsionNode.hp);
		node.setSid(transsionNode.sid);
		node.setRid(transsionNode.rid);

		return node;
	}


	private ArrayList<TranssionResponse.SeatBid> unifiedRespSeatBidList2TranssionSeatBidList(List<SeatBid> seatBids, SessionContext sessionContext) {
		if (seatBids == null || seatBids.isEmpty()) {
			return null;
		}
		ArrayList<TranssionResponse.SeatBid> transsionSeatBids = new ArrayList<>();
		for (SeatBid seatBid : seatBids) {
			TranssionResponse.SeatBid transsionSeatBid = new TranssionResponse.SeatBid();
			transsionSeatBid.seat = seatBid.getSeat();
			if (seatBid.getBids() != null && !seatBid.getBids().isEmpty()) {
				transsionSeatBid.bid = new ArrayList<>();
				for (Bid bid : seatBid.getBids()) {
					TranssionResponse.SeatBid.Bid transsionBid = unifiedRespBid2transsionBid(bid, sessionContext);
					if (transsionBid != null) {
						transsionSeatBid.bid.add(transsionBid);
					}
				}
			}
			transsionSeatBids.add(transsionSeatBid);
		}
		return transsionSeatBids;
	}

	private TranssionResponse.SeatBid.Bid unifiedRespBid2transsionBid(Bid bid, SessionContext sessionContext) {
		if (bid == null) {
			return null;
		}
		TranssionResponse.SeatBid.Bid transsionBid = new TranssionResponse.SeatBid.Bid();
		transsionBid.id = bid.getBidId();
		transsionBid.impid = bid.getImpId();
		if (bid.getPrice() != null) {
			transsionBid.price = (float) (bid.getPrice() * 1.0 / Constants.PRICE_MULTIPLY_MILLIONS);
		}
		transsionBid.nurl = bid.getNurl();
		Integer settlementType = sessionContext.getSspEp().getSettlementType();
		if (settlementType == Constants.SETTLEMENTTYPE_BURL) {
			transsionBid.burl = bid.getBurl();
		}
		transsionBid.adm = bid.getAdm();
		transsionBid.w = bid.getW();
		transsionBid.h = bid.getH();
		transsionBid.adid = bid.getAdid();
		transsionBid.cid = bid.getCid();
		transsionBid.crid = bid.getCrid();
		transsionBid.api = bid.getApi();
		transsionBid.bundle = bid.getBundle();
		transsionBid.iurl = bid.getIurl();
		transsionBid.adomain = bid.getAdomain() != null ? new ArrayList<>(bid.getAdomain()) : null;
		transsionBid.cat = bid.getCat() != null ? new ArrayList<>(bid.getCat()) : null;
		transsionBid.attr = bid.getAttr() != null ? new ArrayList<>(bid.getAttr()) : null;

		if (bid.getExt() != null) {
			TranssionResponse.SeatBid.Bid.Ext transsionBidExt = new TranssionResponse.SeatBid.Bid.Ext();
			if (transsionBid.burl != null) {
				transsionBidExt.impression_tracking_url = new ArrayList<>();
				transsionBidExt.impression_tracking_url.add(transsionBid.burl);
			}
			transsionBidExt.deeplink = bid.getExt().deeplink;
			transsionBidExt.click_through_url = bid.getExt().fallback;
			transsionBid.ext = transsionBidExt;
		}

		return transsionBid;
	}

}
