package com.iflytek.traffic.protocol.transsion;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @datetime 2025/6/24 11:11
 */
public class TranssionResponse {
	public String id;
	public ArrayList<SeatBid> seatbid;

	public String bidid;
	public String cur;
	public Integer nbr;

	public static class SeatBid {
		public ArrayList<Bid> bid;
		public String seat;

		public static class Bid {
			public String id;
			public String impid;
			public float price;
			public String nurl;
			public String burl;
			public String adm;
			public String adid;
			public ArrayList<String> adomain;
			public String bundle;
			public String iurl;
			public String cid;
			public String crid;
			public ArrayList<String> cat;
			public ArrayList<Integer> attr;
			public Integer api;
			public Integer w;
			public Integer h;
			public Ext ext;

			public static class Ext {
				public ArrayList<String> impression_tracking_url;
				public ArrayList<String> click_tracking_url;
				public String click_through_url;
				public String deeplink;
			}
			public static class Native {
				public Integer ver = 1;
				public ArrayList<Asset> assets;
				public Link link;
				public ArrayList<String> imptrackers;

				public static class Asset {
					public int id;
					public int required;
					public Title title;
					public Image img;
					public Data data;

					public static class Title {
						public String text;
					}

					public static class Image {
						public String url;
						public Integer w;
						public Integer h;
					}

					public static class Data {
						public String label;
						public String value;
					}
				}


				public static class Link {
					public String url;
					public ArrayList<String> clicktrackers;
					public String fallback;
				}
			}
		}

	}


}
