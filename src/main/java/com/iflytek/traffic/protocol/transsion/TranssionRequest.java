package com.iflytek.traffic.protocol.transsion;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2025/6/24 11:10
 */
public class TranssionRequest {
	public String id;
	public ArrayList<Imp> imp;
	public Site site;

	public App app;

	public Device device;

	public User user;

	//1一价；2二价
	public int at = 2;

	//超时时间要求，包含来回网络时间
	public Integer tmax;

	public ArrayList<String> cur;

	//语言白名单 ISO-639-1-alpha-2
	public ArrayList<String> wlang;

	public ArrayList<String> bcat;
	public ArrayList<String> bapp;
	public ArrayList<String> badv;

	public Source source;

	public Regs regs;

	public static class Imp {
		public String id;

		public Banner banner;

		@JSONField(name = "native")
		public Native native1;

		//1 = the ad is interstitial or full screen, 0 = not interstitial
		public int instl;

		//Identifier for specific ad placement or ad tag that was used to initiate the auction. This can be useful for debugging of any issues, or for optimization by the buyer
		public String tagid;

		public float bidfloor;
		public String bidfloorcur = "USD";

		public int secure;

		public Ext ext;

		public String displaymanager;

		public String displaymanagerver;

		public static class Banner {
			public ArrayList<Format> format;
			public Integer w;
			public Integer h;
			public ArrayList<Integer> battr;
			public Integer pos;
			public List<String> mimes;
			public ArrayList<Integer> api;
			public String id;

			public static class Format {
				public Integer w;
				public Integer h;
			}
		}

		public static class Native {
			public String request;
			public String ver;
			public ArrayList<Integer> api;
			public ArrayList<Integer> battr;

			public static class Request {

				@JSONField(name = "native")
				public RequestNative native1;

				public static class RequestNative {
					public String ver;
					public Integer layout;
					public Integer plcmtcnt;
					public ArrayList<Asset> assets;
				}

				public static class Asset {
					public Integer id;
					public Integer required;
					public Title title;

					public static class Title {
						public Integer len;
					}

					public Image img;

					public static class Image {
						public Integer type;
						public Integer wmin;
						public Integer hmin;
						public ArrayList<String> mimes;
					}

					public Data data;

					public static class Data {
						//2 desc; 3 rating; 5 downloads; 12 cta
						public Integer type;
						public Integer len;
					}

				}
			}
		}

		public static class Ext{
			public Integer codeType;
		}

	}


	public static class Site {
		public String id;
		public String name;
		public String domain;
		public ArrayList<String> cat;
		//1 yes; 0 no
		public Integer mobile;
	}


	public static class App {
		public String ver;
		public String id;
		public String name;
		public String bundle;
		public String domain;
		public String storeurl;
		public List<String> cat;
		public Publisher publisher;
	}

	public static class Publisher {
		public String id;
		public String name;
		public String domain;
	}


	public static class Device {
		public String ua;
		public Geo geo;
		public Integer dnt;
		public Integer lmt;
		public String ip;
		public String ipv6;
		public Integer devicetype;
		public String make;
		public String model;
		public String os;
		public String osv;
		//hardware version, 5S for iphone 5S
		public String hwv;
		public Integer w;
		public Integer h;
		public String mccmnc;
		public Float pxratio;
		public String language;
		public String carrier;
		public Integer connectiontype;
		public String ifa;
		//android id md5
		public String dpidmd5;
		public Ext ext;

		public static class Ext {
			public String idfa;
			public String idfamd5;
			public String gaid;
		}

	}

	public static class Geo {
		public Float lat;
		public Float lon;
		public Integer type;
		public String country;
		public String region;
		public String city;
	}

	public static class User {
		public String id;
		public String buyerid;
		public ArrayList<Data> data;
		public Geo geo;

		public static class Data {
			public String id;
			public String name;
			public ArrayList<Segment> segment;

			public static class Segment {
				public String id;
				public String name;
				public String value;
			}
		}
	}

	public static class Regs {
		public Integer coppa;
		public Integer gdpr;
	}

	public static class Source {

		public Integer fd;

		public String tid;

		public String pchain;

		public Source.SourceExt ext;

		public static class SourceExt {

			public Source.SChain schain;

		}

		public static class  SChain {

			public String ver;

			public List<Source.Node> nodes;

			public Integer complete;
		}

		public static class Node {

			public String asi;

			public Integer hp;

			public String sid;

			public String rid;

		}

	}
}
