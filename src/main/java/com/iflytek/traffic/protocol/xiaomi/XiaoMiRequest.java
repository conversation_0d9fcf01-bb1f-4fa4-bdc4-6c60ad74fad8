package com.iflytek.traffic.protocol.xiaomi;

import com.alibaba.fastjson.annotation.JSONField;
import com.iflytek.traffic.protocol.rtb25.request.Source;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @datetime 2025/6/16 14:49
 */

public class XiaoMiRequest {
	public String id;

	public ArrayList<Imp> imp;

	public App app;

	public Device device;

	public User user;

	public List<String> bcat;

	public List<String> badv;

	public List<String> bapp;

	//1一价；2二价
	public Integer at = 1;
	// 最大时间
	public Integer tmax;
	// 结算货币
	public List<String> cur;

	public Regs regs;

	public Source source;

	public static class Imp {
		public String id;

		public Banner banner;

		public Video video;

		@JSONField(name = "native")
		public Native native1;
		// 1 插屏 or 全屏 0 非
		public Integer instl = 0;

		public float bidfloor = 0;

		public Integer secure;

		public String bidfloorcur = "USD";

		public String tagid;

		public String displaymanager;

		public String displaymanagerver;

		public Ext ext;

		public static class Ext {
			public Integer reward;
			// 10 = icon
			public Integer adtype;
			// 0 = The ad is new user acquisition;
			//1 = User retention ad; 2 = No limit
			public Integer landingtype;

			public List<String> package_list;
			// 广告数量受限于小米GetApps上推荐的广告位
			public Integer ad_count;
			// An array of search keywords, only
			//valid when search box is used by
			//users
			public List<String> query;

			public String deeplink;

			public String fallback;
		}
	}
	public static class Banner {
		public int w;
		public int h;
		public ArrayList<Format> format;
		public Integer wmax;
		public Integer hmax;
		public Integer wmin;
		public Integer hmin;
		public List<Integer> battr;
		//Blocked banner ad types. Refer to Appendix 6.2-
		//Banner Ad Types.
		public List<Integer> btype;
		public Integer pos;
		public List<String> mimes;
		// banner 位置
		public Integer topframe;
		public List<Integer> expdir;
		public List<Integer> api;
		public String id;
		public Integer vcm;

		public static class Format {
			public int w;
			public int h;
		}
	}

	public static class Video {
		public Integer w;
		public Integer h;
		public Integer placement;
		public List<String> mimes;
		public Integer minduration;
		public Integer maxduration;
		public List<Integer> protocols;
		public Integer startdelay;
		public Integer linearity;
		public Integer skip;
		public Integer skipmin;
		public Integer skipafter;
		public Integer sequence;
		public List<Integer> battr;
		//
		public Integer maxextended;
		public Integer minbitrate;
		public Integer maxbitrate;
		public Integer boxingallowed;
		public List<Integer> playbackmethod;
		public Integer playbackend;
		public List<Integer> delivery;
		public Integer pos;
		public List<Banner> companionad;
		public List<Integer> api;
		public List<Integer> companiontype;
	}

	public static class Native {
		public String request;
		public String ver = "1";

		public static class RequestNative {
			@JSONField(name = "native")
			public Request request1;
		}

		public static class Request {
			public String ver;
			public Integer context;
			public Integer plcmttype;
			public List<Asset> assets;


			public static class Asset {
				public Integer id;
				public int required;
				public Title title;

				public static class Title {
					public int len;
				}

				public Image img;

				public static class Image {
					// 1-icon；2-logo；3-img
					public Integer type;
					public Integer w;
					public Integer h;
					public Integer wmin;
					public Integer hmin;
					public List<String> mimes;
				}

				public Video video;
				public static class Video {
					public List<String> mimes;
					public Integer minduration;
					public Integer maxduration;
					public List<Integer> protocols;
					public Ext ext;

					public static class Ext {
					}
				}

				public Data data;

				public static class Data {
					//2 desc; 3 rating; 5 downloads; 12 cta
					public Integer type;
					public Integer len;
				}

			}
		}
	}

	public static class App {
		public String id;
		public String name;
		public String bundle;
		public String ver;
		public String storeurl;
		public Publisher publisher;
	}

	public static class Device {
		public String ua;
		public Geo geo;

		public static class Geo {
			public String country;
			public String region;
		}

		public String ifa;
		public String ip;
		public Integer devicetype;
		public Integer connectiontype;
		public String make;
		public String model;
		public String os;
		public String osv;
		public Integer dnt;
		public Integer lmt;
		public String language;
		public String carrier;
	}

	public static class User {
		public String id;
		public Ext ext;

		public static class Ext {
			public String consent;
		}
	}

	public static class Regs {
		public Integer coppa;
		public Ext ext;

		public static class Ext {
			public Integer gdpr;
		}
	}

	public static class Publisher {
		public String id;
		public String name;
		public String domain;
	}

	public static class Source {

		public Integer fd;

		public String tid;

		public String pchain;

		public SourceExt ext;

		public static class SourceExt {

			public SChain schain;

		}

		public static class  SChain {

			public String ver;

			public List<Node> nodes;

			public Integer complete;
		}

		public static class Node {

			public String asi;

			public Integer hp;

			public String sid;

			public String rid;

		}

	}
}

