package com.iflytek.traffic.protocol.xiaomi;

import java.util.ArrayList;
/**
 * <AUTHOR>
 * @datetime 2025/6/16 14:51
 */
public class XiaoMiResponse {
	public String id;
	public ArrayList<SeatBid> seatbid;

	public String bidid;
	public String cur = "USD";
	public Integer nbr;

	public static class SeatBid {
		public ArrayList<Bid> bid;
		public String seat;

		public static class Bid {
			public String id;
			public String impid;
			public float price;
			public String nurl;
			public String burl;
			public String lurl;
			//public int protocol;
			public String adm;
			public Integer w;
			public Integer h;
			public String adid;
			public ArrayList<String> adomain = new ArrayList<String>();
			public String bundle;
			public String iurl;
			public String cid;
			public String crid;
			// 策略ID 预先沟通
			public String tactic;
			public ArrayList<String> cat = new ArrayList<>();
			public ArrayList<Integer> attr = new ArrayList<Integer>();
			public Integer api;
			public Integer protocol;
			//Creative media rating per IQG guidelines. 1-All Audiences. 2-Everyone Over 12. 3-Mature Audiences
			public Integer qagmediarating = 1;
			public String language;
			public String dealid;

			public Integer exp;

			public Ext ext;

			public String insurl;

			public static class Ext {
				// 1 = Using Xiaomi GetApps automatic download capability
				public Integer adDownloadType;

				public String privacyUrl;

				public String privacyToken;

				public Integer landingtype;

				public String deeplink;

				public String fallback;
			}

			public static class Native {
				public ArrayList<Asset> assets;
				public Link link;
				public ArrayList<String> imptrackers;

				public static class Asset {
					public int id;
					public int required;
					public Title title;
					public Video video;
					public Image img;
					public Data data;
					public Link link;

					public static class Title {
						public String text;
					}

					public static class Image {
						public String url;
						public Integer w;
						public Integer h;
					}

					public static class Video {
						//vast xml
						public String vasttag;
					}

					public static class Data {
						public String value;
					}
				}


				public static class Link {
					public String url;
					public ArrayList<String> clicktrackers;
					public String fallback;
				}
			}
		}
	}
}

