package com.iflytek.traffic.protocol.xiaomi;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.iflytek.traffic.data.entity.SspSlotInfo;
import com.iflytek.traffic.data.provider.FusionProvider;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.protocol.Protocol;
import com.iflytek.traffic.protocol.ProtocolParser;
import com.iflytek.traffic.protocol.rtb25.Native;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.asset.Asset;
import com.iflytek.traffic.session.request.*;
import com.iflytek.traffic.session.response.Bid;
import com.iflytek.traffic.session.response.SeatBid;
import com.iflytek.traffic.session.response.UnifiedResponse;
import com.iflytek.traffic.ssp.SspEp;
import com.iflytek.traffic.util.Util;
import com.iflytek.traffic.util.constant.Constants;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @datetime 2025/6/16 15:04
 */
@Slf4j
@Protocol(name = "xiaomi")
public class XiaoMiParser extends ProtocolParser<XiaoMiRequest, XiaoMiResponse> {
	@Override
	public UnifiedRequest reqBody2UnifiedReq(byte[] body, SspEp sspEp) throws Exception {
		UnifiedRequest unifiedRequest = new UnifiedRequest();
		XiaoMiRequest bidrequest;
		try {
			bidrequest = JSON.parseObject(body, XiaoMiRequest.class);
			if (log.isInfoEnabled()) {
				log.info("ssp:{}, ep:{}, recv req:{}", sspEp.getSspName(), sspEp.getSspEpId(), JSON.toJSONString(bidrequest));
			}
		} catch (Exception e) {
			log.warn("ssp:{}, ep:{}, parse err:{}", sspEp.getSspName(), sspEp.getSspEpId(), Util.genStackInfo(e), e);
			unifiedRequest.setNeedForward(false);
			return unifiedRequest;
		}
		
		unifiedRequest.setMediaReqId(bidrequest.id);
		Map<String, Impression> imps = xiaomiImpList2UnifiedReqImpList(bidrequest.imp, sspEp.getSspId());
		if (!imps.isEmpty()) {
			unifiedRequest.setImps(imps);
		}
		unifiedRequest.setApp(xiaomiApp2UnifiedReqApp(bidrequest.app));
		unifiedRequest.setDevice(xiaomiDevice2UnifiedReqDevice(bidrequest.device));
		unifiedRequest.setUser(xiaomiUser2UnifiedReqUser(bidrequest.user));
		unifiedRequest.setRegs(xiaomiRegs2UnifiedReqRegs(bidrequest.regs));
		unifiedRequest.setSource(xiaomiSource2UnifiedReqSource(bidrequest.source));
		unifiedRequest.setAt(bidrequest.at);
		unifiedRequest.setTmax(bidrequest.tmax);
		unifiedRequest.setBcat(bidrequest.bcat != null ? new ArrayList<>(bidrequest.bcat) : null);
		unifiedRequest.setBadv(bidrequest.badv != null ? new ArrayList<>(bidrequest.badv) : null);
		unifiedRequest.setBapp(bidrequest.bapp != null ? new ArrayList<>(bidrequest.bapp) : null);
		unifiedRequest.setCur(bidrequest.cur != null ? new ArrayList<>(bidrequest.cur) : null);

        return unifiedRequest;
    }

	@Override
	public XiaoMiRequest unifiedReq2ProtocolReq(SessionContext sessionContext, UnifiedRequest unifiedReq, DspEpObj dspEpObj) throws Exception {
		return null;
	}

	@Override
	public void trafficFusion(SessionContext sessionContext, XiaoMiRequest xiaoMiRequest, DspEpObj dspEpObj) {
		if (MapUtil.isEmpty(sessionContext.getDspFusionTarget()) || !sessionContext.getDspFusionTarget().containsKey(dspEpObj.getDspId())) {
			return;
		}
		Map<String, FusionProvider.TargetTraffic> impTargetTraffic = sessionContext.getDspFusionTarget().get(dspEpObj.getDspId());
		if (MapUtil.isEmpty(impTargetTraffic)) {
			return;
		}
		for (XiaoMiRequest.Imp imp : xiaoMiRequest.imp) {
			FusionProvider.TargetTraffic traffic = impTargetTraffic.get(imp.id);
			if (traffic == null) {
				continue;
			}
			if (StrUtil.isNotBlank(traffic.getTarAppId())) {
				xiaoMiRequest.app.id = traffic.getTarAppId();
			}
			if (StrUtil.isNotBlank(traffic.getTarPkg())) {
				xiaoMiRequest.app.bundle = traffic.getTarPkg();
			}
			if (StrUtil.isNotBlank(traffic.getTarTagId())) {
				imp.tagid = traffic.getTarTagId();
			}
		}
		log.info("dsp name:{}, id: {}, ep: {}, after fusion request:{}", dspEpObj.getName(), dspEpObj.getDspId(), dspEpObj.getDspEpId(), JSON.toJSONString(xiaoMiRequest));
	}

	@Override
	public XiaoMiResponse unifiedResp2ProtocolResp(SessionContext sessionContext, UnifiedResponse unifiedResp) throws Exception {
		XiaoMiResponse xiaoMiResponse = new XiaoMiResponse();
		xiaoMiResponse.id = sessionContext.getUnifiedRequest().getMediaReqId();
		xiaoMiResponse.bidid = unifiedResp.getBidId();
		xiaoMiResponse.nbr = unifiedResp.getNbr();
		xiaoMiResponse.cur = unifiedResp.getCur();
		xiaoMiResponse.seatbid = unifiedRespSeatBidList2xiaomiSeatBidList(unifiedResp.getSeatbids(), sessionContext);

		return xiaoMiResponse;
	}

	@Override
	public UnifiedResponse respBody2UnifiedResp(int code, byte[] body, DspEpObj dspEpObj) throws Exception {
		return null;
	}

	@Override
	public Class<XiaoMiRequest> getTypeT() {
		return XiaoMiRequest.class;
	}

	@Override
	public Class<XiaoMiResponse> getTypeR() {
		return XiaoMiResponse.class;
	}

	private Map<String, Impression> xiaomiImpList2UnifiedReqImpList(ArrayList<XiaoMiRequest.Imp> impList, int sspId) {
		Map<String, Impression> imps = new HashMap<>();
		// 转化imp信息
		if (impList != null && !impList.isEmpty()) {
			for (XiaoMiRequest.Imp imp : impList) {
				Impression i = xiaomiImp2UnifiedReqImp(imp, sspId);
				imps.put(i.getImpId(), i);
			}
		}
		return imps;
    }

	private Impression xiaomiImp2UnifiedReqImp(XiaoMiRequest.Imp imp, int sspId) {
		if (imp == null) {
			return null;
		}
		Impression impression = new Impression();
		impression.setImpId(imp.id);

		impression.setBidfloor((long) (imp.bidfloor * Constants.PRICE_MULTIPLY_MILLIONS));
		impression.setBidfloorcur(imp.bidfloorcur);
		impression.setTagId(imp.tagid);

		impression.setInstl(imp.instl);
		impression.setSecure(imp.secure);
		impression.setDisplaymanager(imp.displaymanager);
		impression.setDisplaymanagerver(imp.displaymanagerver);

		if (imp.ext != null) {
			impression.setExt(xiaomiImpExt2UnifiedReqImpExt(imp.ext));
		}

		// 从数据库中读广告位类型
		SspSlotInfo sspSlotInfo = getSspSlotInfo(sspId, imp.tagid);
		if (sspSlotInfo != null) {
			Asset.AdType adType = Asset.AdType.getAdType(sspSlotInfo.getSlotType());
			impression.setAdType(adType);
		}

		// 广告素材
		if (imp.banner != null) {
			if (impression.getAdType() == Asset.AdType.UNKNOWN) {
				impression.setAdType(Asset.AdType.SUPPER_BANNER);
			}
			impression.setBanner(xiaomiBanner2UnifiedReqBanner(imp.banner));
			impression.getAdUnitSize().add(imp.banner.w + "*" + imp.banner.h);
			impression.setRequestAdType(Asset.AdType.SUPPER_BANNER);
		}
		if (imp.video != null) {
			if (impression.getAdType() == Asset.AdType.UNKNOWN) {
				impression.setAdType(Asset.AdType.SUPPER_VIDEO);
			}
			impression.setVideo(xiaomiVideo2UnifiedReqVideo(imp.video));
			impression.getAdUnitSize().add(imp.video.w + "*" + imp.video.h);
			impression.setRequestAdType(Asset.AdType.SUPPER_VIDEO);
		}
		if (imp.native1 != null) {
			if (impression.getAdType() == Asset.AdType.UNKNOWN) {
				impression.setAdType(Asset.AdType.SUPPER_NATIVE);
			}
			impression.setNative1(xiaomiNative2UnifiedReqNative(imp.native1, impression));
			impression.setRequestAdType(Asset.AdType.SUPPER_NATIVE);
		}

		return impression;
	}
	
	private Impression.Ext xiaomiImpExt2UnifiedReqImpExt(XiaoMiRequest.Imp.Ext xiaomiExt) {
		if (xiaomiExt == null) {
         return null;
        }
		Impression.Ext ext = new Impression.Ext();
		ext.setReward(xiaomiExt.reward);
		if ("1".equals(xiaomiExt.deeplink)) {
			ext.setDeeplink(1);
		}
		if ("1".equals(xiaomiExt.fallback)) {
			ext.setFallback(1);
		}
		// xiaomi靠广告位区分广告位类型
//		ext.setAdtype(xiaomiExt.adtype);
		ext.setPackageList(xiaomiExt.package_list);
		ext.setAdCount(xiaomiExt.ad_count);
		
		return ext;
	}

	private Banner xiaomiBanner2UnifiedReqBanner(XiaoMiRequest.Banner xiaomiBanner) {
		if (xiaomiBanner == null) {
			return null;
		}
		Banner banner = new Banner();

		if (xiaomiBanner.format != null) {
			banner.setFormat(new ArrayList<>());
			for (XiaoMiRequest.Banner.Format xiaomiFormat : xiaomiBanner.format) {
				Format format = xiaomiFormat2UnifiedReqFormat(xiaomiFormat);
				banner.getFormat().add(format);
			}
		}

		banner.setBattr(xiaomiBanner.battr != null ? new ArrayList<>(xiaomiBanner.battr) : null);
		banner.setBtype(xiaomiBanner.btype != null ? new ArrayList<>(xiaomiBanner.btype) : null);
		banner.setMimes(xiaomiBanner.mimes != null ? new ArrayList<>(xiaomiBanner.mimes) : null);
		banner.setExpdir(xiaomiBanner.expdir != null ? new ArrayList<>(xiaomiBanner.expdir) : null);
		banner.setApi(xiaomiBanner.api != null ? new ArrayList<>(xiaomiBanner.api) : null);
		banner.setW(xiaomiBanner.w);
		banner.setH(xiaomiBanner.h);
		banner.setWmax(xiaomiBanner.wmax);
		banner.setHmax(xiaomiBanner.hmax);
		banner.setWmin(xiaomiBanner.wmin);
		banner.setHmin(xiaomiBanner.hmin);
		banner.setPos(xiaomiBanner.pos);
		banner.setTopframe(xiaomiBanner.topframe);
		banner.setId(xiaomiBanner.id);
		banner.setVcm(xiaomiBanner.vcm);

		return banner;
	}

	private Format xiaomiFormat2UnifiedReqFormat(XiaoMiRequest.Banner.Format xiaomiFormat) {
		if (xiaomiFormat == null) {
			return null;
		}
		Format format = new Format();
		format.setW(xiaomiFormat.w);
		format.setH(xiaomiFormat.h);
		return format;
	}

	private Video xiaomiVideo2UnifiedReqVideo(XiaoMiRequest.Video xiaomiVideo) {
		if (xiaomiVideo == null) {
			return null;
		}

		Video video = new Video();

		video.setMimes(xiaomiVideo.mimes != null ? new ArrayList<>(xiaomiVideo.mimes) : null);
		video.setProtocols(xiaomiVideo.protocols != null ? new ArrayList<>(xiaomiVideo.protocols) : null);
		video.setBattr(xiaomiVideo.battr != null ? new ArrayList<>(xiaomiVideo.battr) : null);
		video.setPlaybackmethod(xiaomiVideo.playbackmethod != null ? new ArrayList<>(xiaomiVideo.playbackmethod) : null);
		video.setDelivery(xiaomiVideo.delivery != null ? new ArrayList<>(xiaomiVideo.delivery) : null);
		video.setApi(xiaomiVideo.api != null ? new ArrayList<>(xiaomiVideo.api) : null);
		video.setCompaniontype(xiaomiVideo.companiontype != null ? new ArrayList<>(xiaomiVideo.companiontype) : null);

		if (xiaomiVideo.companionad != null) {
			video.setCompanionad(new ArrayList<>());
			for (XiaoMiRequest.Banner xiaomiBanner : xiaomiVideo.companionad) {
				Banner banner = xiaomiBanner2UnifiedReqBanner(xiaomiBanner);
				video.getCompanionad().add(banner);
			}
		}

		video.setW(xiaomiVideo.w);
		video.setH(xiaomiVideo.h);
		video.setPlacement(xiaomiVideo.placement);
		video.setMinduration(xiaomiVideo.minduration);
		video.setMaxduration(xiaomiVideo.maxduration);
		video.setStartdelay(xiaomiVideo.startdelay);
		video.setLinearity(xiaomiVideo.linearity);
		video.setSkip(xiaomiVideo.skip);
		video.setSkipmin(xiaomiVideo.skipmin);
		video.setSkipafter(xiaomiVideo.skipafter);
		video.setSequence(xiaomiVideo.sequence);
		video.setMaxextended(xiaomiVideo.maxextended);
		video.setMinbitrate(xiaomiVideo.minbitrate);
		video.setMaxbitrate(xiaomiVideo.maxbitrate);
		video.setBoxingallowed(xiaomiVideo.boxingallowed);
		video.setPlaybackend(xiaomiVideo.playbackend);
		video.setPos(xiaomiVideo.pos);

		return video;
	}

	private Native xiaomiNative2UnifiedReqNative(XiaoMiRequest.Native xiaomiNative, Impression impression) {
		if (xiaomiNative == null) {
			return null;
		}

		Native native1 = new Native();
//		native1.request = xiaomiNative.request;
		native1.ver = xiaomiNative.ver;
		
		if (xiaomiNative.request != null) {
			Native.Request request = new Native.Request();
			request.native1 = convertRequestXiaoMiToUnified(xiaomiNative.request, impression);
			native1.request = JSON.toJSONString(request);
		}

		return native1;
	}

	private Native.NativeRequest convertRequestXiaoMiToUnified(String requestJson, Impression impression) {
		XiaoMiRequest.Native.RequestNative tempRequestNative = JSON.parseObject(requestJson, XiaoMiRequest.Native.RequestNative.class);
		if (tempRequestNative == null || tempRequestNative.request1 == null) {
			return null;
		}

		XiaoMiRequest.Native.Request aRequest = tempRequestNative.request1;
		Native.NativeRequest native1Request = new Native.NativeRequest();
		native1Request.ver = aRequest.ver;
		
		if (aRequest.assets != null) {
			native1Request.assets = aRequest.assets.stream()
					.map(this::convertAssetXiaoMiToUnified)
					.collect(Collectors.toList());
		}

		int adSizeType = Constants.NATIVE_IMG_TYPE_IMG;
		if (impression.getAdType() == Asset.AdType.SUPPER_ICON || impression.getAdType() == Asset.AdType.SUPPER_PUSH) {
			adSizeType = Constants.NATIVE_IMG_TYPE_ICON;
		}
		for (Native.NativeRequest.Asset asset : native1Request.assets) {
			if (asset.img != null && asset.img.type == adSizeType) {
				int w=0,h=0;
				if (asset.img.w != null) {
					w = asset.img.w;
				} else if (asset.img.wmin != null) {
					w = asset.img.wmin;
				}
				if (asset.img.h != null) {
					h = asset.img.h;
				} else if (asset.img.hmin != null) {
					h = asset.img.hmin;
				}
				impression.getAdUnitSize().add(w + "*" + h);
				break;
			} else if (asset.video != null) {
				impression.getAdUnitSize().add("16*9");
				break;
			}
		}

		return native1Request;
	}

	private Native.NativeRequest.Asset convertAssetXiaoMiToUnified(XiaoMiRequest.Native.Request.Asset aAsset) {
		if (aAsset == null) {
			return null;
		}
		Native.NativeRequest.Asset bAsset = new Native.NativeRequest.Asset();
		bAsset.id = aAsset.id;
		bAsset.required = aAsset.required;
		
		if (aAsset.title != null) {
			Native.NativeRequest.Title bTitle = new Native.NativeRequest.Title();
			bTitle.len = aAsset.title.len;
			bAsset.title = bTitle;
		}
		
		if (aAsset.img != null) {
			Native.NativeRequest.Image bImage = new Native.NativeRequest.Image();
			bImage.type = aAsset.img.type;
			bImage.w = aAsset.img.w;
			bImage.h = aAsset.img.h;
			bImage.wmin = aAsset.img.wmin;
			bImage.hmin = aAsset.img.hmin;
			bImage.mimes = aAsset.img.mimes != null ? new ArrayList<>(aAsset.img.mimes) : null;
			bAsset.img = bImage;
		}
		
		if (aAsset.video != null) {
			Native.NativeRequest.Video bVideo = new Native.NativeRequest.Video();
			bVideo.mimes = aAsset.video.mimes != null ? new ArrayList<>(aAsset.video.mimes) : null;
			bVideo.minduration = aAsset.video.minduration;
			bVideo.maxduration = aAsset.video.maxduration;
			bVideo.protocols = aAsset.video.protocols != null ? new ArrayList<>(aAsset.video.protocols) : null;
			bAsset.video = bVideo;
		}
		
		if (aAsset.data != null) {
			Native.NativeRequest.Data bData = new Native.NativeRequest.Data();
			bData.type = aAsset.data.type;
			bData.len = aAsset.data.len;
			bAsset.data = bData;
		}

		return bAsset;
	}

	private App xiaomiApp2UnifiedReqApp(XiaoMiRequest.App xiaomiApp) {
		if (xiaomiApp == null) {
			return null;
		}

		App unifiedApp = new App();

		unifiedApp.setId(xiaomiApp.id);
		unifiedApp.setName(xiaomiApp.name);
		unifiedApp.setBundle(xiaomiApp.bundle);
		unifiedApp.setVer(xiaomiApp.ver);
		unifiedApp.setStoreurl(xiaomiApp.storeurl);
		
		if (xiaomiApp.publisher != null) {
			unifiedApp.setPublisher(convertPublisherXiaoMiToUnified(xiaomiApp.publisher));
		}
		return unifiedApp;
	}

	private Publisher convertPublisherXiaoMiToUnified(XiaoMiRequest.Publisher xiaomiPublisher) {
		if (xiaomiPublisher == null) {
			return null;
		}
		Publisher publisher = new Publisher();
		
		publisher.setId(xiaomiPublisher.id);
		publisher.setName(xiaomiPublisher.name);
		publisher.setDomain(xiaomiPublisher.domain);

		return publisher;
	}

	private Device xiaomiDevice2UnifiedReqDevice(XiaoMiRequest.Device xiaomiDevice) {
		if (xiaomiDevice == null) {
			return null;
		}
		Device device = new Device();

		device.setUa(xiaomiDevice.ua);
		device.setIfa(xiaomiDevice.ifa);
		device.setIp(xiaomiDevice.ip);
		device.setMake(xiaomiDevice.make);
		device.setModel(xiaomiDevice.model);
		device.setDnt(xiaomiDevice.dnt);
		device.setLmt(xiaomiDevice.lmt);
		device.setLanguage(xiaomiDevice.language);
		device.setCarrierOrigin(xiaomiDevice.carrier);
		device.setOsOrigin(xiaomiDevice.os);
		device.setOsv(xiaomiDevice.osv);
		if (xiaomiDevice.os != null) {
			String os = xiaomiDevice.os.toLowerCase().trim();
			if (os.contains("android")) {
				device.setOs(Device.Os.ANDROID);
			} else if (os.contains("ios")) {
				device.setOs(Device.Os.IOS);
			} else if (os.contains("windows")) {
				device.setOs(Device.Os.WINDOWS);
			}
		}
		device.setDevicetypeOrigin(xiaomiDevice.devicetype);
		if (xiaomiDevice.devicetype != null) {
			switch (xiaomiDevice.devicetype) {
				case 1:
					device.setDeviceType(Device.DeviceType.PAD);
					break;
				case 2:
					device.setDeviceType(Device.DeviceType.PC);
					break;
				case 3:
					device.setDeviceType(Device.DeviceType.TV);
					break;
				case 4:
					device.setDeviceType(Device.DeviceType.PHONE);
					break;
				case 5:
					device.setDeviceType(Device.DeviceType.PAD);
					break;
				case 7:
					device.setDeviceType(Device.DeviceType.TV);
					break;
				default:
					device.setDeviceType(Device.DeviceType.UNKNOWN);
					break;
			}
		}

		device.setConnectiontype(xiaomiDevice.connectiontype);
		if (xiaomiDevice.connectiontype != null) {
			switch (xiaomiDevice.connectiontype) {
				case 0:
					device.setNetwork(Device.Network.UNKNOWN);
					break;
				case 1:
					device.setNetwork(Device.Network.ETHERNET);
					break;
				case 2:
					device.setNetwork(Device.Network.WIFI);
					break;
				case 3:
					device.setNetwork(Device.Network.MOBUNKNOWN);
					break;
				case 4:
					device.setNetwork(Device.Network.M2G);
					break;
				case 5:
					device.setNetwork(Device.Network.M3G);
					break;
				case 6:
					device.setNetwork(Device.Network.M4G);
					break;
				default:
					device.setNetwork(Device.Network.UNKNOWN);
					break;
			}
		}

		if (xiaomiDevice.geo != null) {
			Geo geo = new Geo();
			geo.setCountry(xiaomiDevice.geo.country);
			geo.setRegion(xiaomiDevice.geo.region);
			device.setGeo(geo);
		}

		return device;
	}

	private User xiaomiUser2UnifiedReqUser(XiaoMiRequest.User xiaomiUser) {
		if (xiaomiUser == null) {
			return null;
		}
		User user = new User();
		user.setId(xiaomiUser.id);
		return user;
	}

	private Regs xiaomiRegs2UnifiedReqRegs(XiaoMiRequest.Regs xiaomiRegs) {
		if (xiaomiRegs == null) {
			return null;
		}
		Regs regs = new Regs();
		regs.setCoppa(xiaomiRegs.coppa);
		if (xiaomiRegs.ext != null) {
			Regs.Ext ext = new Regs.Ext();
			ext.setGdpr(xiaomiRegs.ext.gdpr);
			regs.setExt(ext);
		}
		return regs;
	}

	private Source xiaomiSource2UnifiedReqSource(XiaoMiRequest.Source xiaomiSource) {
		if (xiaomiSource == null) {
			return null;
		}

		Source source = new Source();
		source.setFd(xiaomiSource.fd);
		source.setTid(xiaomiSource.tid);
		source.setPchain(xiaomiSource.pchain);
		source.setExt(convertSourceExt(xiaomiSource.ext));

		return source;
	}

	private Source.SourceExt convertSourceExt(XiaoMiRequest.Source.SourceExt xiaomiSourceExt) {
		if (xiaomiSourceExt == null) {
			return null;
		}

		Source.SourceExt ext = new Source.SourceExt();
		ext.setSchain(convertSChain(xiaomiSourceExt.schain));

		return ext;
	}

	private Source.SChain convertSChain(XiaoMiRequest.Source.SChain xiaomiSChain) {
		if (xiaomiSChain == null) {
			return null;
		}

		Source.SChain schainB = new Source.SChain();
		schainB.setVer(xiaomiSChain.ver);
		schainB.setComplete(xiaomiSChain.complete);
		schainB.setNodes(convertNodes(xiaomiSChain.nodes));

		return schainB;
	}

	private List<Source.Node> convertNodes(List<XiaoMiRequest.Source.Node> xiaomiNodes) {
		if (xiaomiNodes == null) {
			return null;
		}

		List<Source.Node> nodes = new ArrayList<>();
		for (XiaoMiRequest.Source.Node node : xiaomiNodes) {
			nodes.add(convertNode(node));
		}

		return nodes;
	}

	private Source.Node convertNode(XiaoMiRequest.Source.Node xiaomiNode) {
		if (xiaomiNode == null) {
			return null;
		}

		Source.Node node = new Source.Node();
		node.setAsi(xiaomiNode.asi);
		node.setHp(xiaomiNode.hp);
		node.setSid(xiaomiNode.sid);
		node.setRid(xiaomiNode.rid);

		return node;
	}


	private ArrayList<XiaoMiResponse.SeatBid> unifiedRespSeatBidList2xiaomiSeatBidList(List<SeatBid> seatBids, SessionContext sessionContext) {
		if (seatBids == null || seatBids.isEmpty()) {
			return null;
		}
		ArrayList<XiaoMiResponse.SeatBid> xiaomiSeatBids = new ArrayList<>();
		for (SeatBid seatBid : seatBids) {
			XiaoMiResponse.SeatBid xiaomiSeatBid = new XiaoMiResponse.SeatBid();
			xiaomiSeatBid.seat = seatBid.getSeat();
			if (seatBid.getBids() != null && !seatBid.getBids().isEmpty()) {
				xiaomiSeatBid.bid = new ArrayList<>();
				for (Bid bid : seatBid.getBids()) {
					XiaoMiResponse.SeatBid.Bid xiaomiBid = unifiedRespBid2xiaomiBid(bid, sessionContext);
					if (xiaomiBid != null) {
						xiaomiSeatBid.bid.add(xiaomiBid);
					}
				}
			}
			xiaomiSeatBids.add(xiaomiSeatBid);
		}
		return xiaomiSeatBids;
	}
	
	private XiaoMiResponse.SeatBid.Bid unifiedRespBid2xiaomiBid(Bid bid, SessionContext sessionContext) {
		if (bid == null) {
			return null;
		}
		XiaoMiResponse.SeatBid.Bid xiaomiBid = new XiaoMiResponse.SeatBid.Bid();
		xiaomiBid.id = bid.getBidId();
		xiaomiBid.impid = bid.getImpId();
		if (bid.getPrice() != null) {
			xiaomiBid.price = (float)(bid.getPrice() *1.0/ Constants.PRICE_MULTIPLY_MILLIONS);
		}
		xiaomiBid.nurl = bid.getNurl();
		Integer settlementType = sessionContext.getSspEp().getSettlementType();
		if (settlementType == Constants.SETTLEMENTTYPE_BURL) {
			xiaomiBid.burl = bid.getBurl();
		}
		xiaomiBid.adm = bid.getAdm();
		xiaomiBid.w = bid.getW();
		xiaomiBid.h = bid.getH();
		xiaomiBid.adid = bid.getAdid();
		xiaomiBid.cid = bid.getCid();
		xiaomiBid.crid = bid.getCrid();
		xiaomiBid.tactic = bid.getTactic();
		xiaomiBid.api = bid.getApi();
		xiaomiBid.protocol = bid.getProtocol();
		xiaomiBid.qagmediarating = bid.getQagmediarating();
		xiaomiBid.language = bid.getLanguage();
		xiaomiBid.dealid = bid.getDealId();
		xiaomiBid.exp = bid.getExp();
		xiaomiBid.bundle = bid.getBundle();
		xiaomiBid.iurl = bid.getIurl();
		xiaomiBid.insurl = bid.getExt() != null ? bid.getExt().insurl : null;
		xiaomiBid.adomain = bid.getAdomain() != null ? new ArrayList<>(bid.getAdomain()) : new ArrayList<>();
		xiaomiBid.cat = bid.getCat() != null ? new ArrayList<>(bid.getCat()) : new ArrayList<>();
		xiaomiBid.attr = bid.getAttr() != null ? new ArrayList<>(bid.getAttr()) : new ArrayList<>();

		if (bid.getExt() != null) {
			XiaoMiResponse.SeatBid.Bid.Ext xiaomiBidExt = new XiaoMiResponse.SeatBid.Bid.Ext();
			xiaomiBidExt.landingtype = bid.getExt().landingtype;
			if (xiaomiBidExt.landingtype == null) {
				xiaomiBidExt.landingtype = 2;
			}
			xiaomiBidExt.deeplink = bid.getExt().deeplink;
			xiaomiBidExt.fallback = bid.getExt().fallback;
			xiaomiBid.ext = xiaomiBidExt;
		}

		return xiaomiBid;
	}
	
}
