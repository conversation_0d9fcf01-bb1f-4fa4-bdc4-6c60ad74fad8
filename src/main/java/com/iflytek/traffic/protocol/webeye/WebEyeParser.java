package com.iflytek.traffic.protocol.webeye;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.iflytek.traffic.data.provider.FusionProvider;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.protocol.Protocol;
import com.iflytek.traffic.protocol.ProtocolParser;
import com.iflytek.traffic.protocol.rtb25.request.Imp;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.asset.Asset;
import com.iflytek.traffic.session.request.*;
import com.iflytek.traffic.session.response.Bid;
import com.iflytek.traffic.session.response.SeatBid;
import com.iflytek.traffic.session.response.UnifiedResponse;
import com.iflytek.traffic.ssp.SspEp;
import com.iflytek.traffic.util.Util;
import com.iflytek.traffic.util.constant.Constants;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @datetime 2025/6/25 16:22
 */
@Slf4j
@Protocol(name = "webeye")
public class WebEyeParser extends ProtocolParser<WebEyeRequest, WebEyeResponse> {
	@Override
	public UnifiedRequest reqBody2UnifiedReq(byte[] body, SspEp sspEp) throws Exception {
		UnifiedRequest unifiedRequest = new UnifiedRequest();
		WebEyeRequest bidrequest;
		try {
            bidrequest = JSON.parseObject(body, WebEyeRequest.class);
			if (log.isDebugEnabled()) {
				log.debug("ssp:{}, ep:{}, recv req:{}", sspEp.getSspName(), sspEp.getSspEpId(), JSON.toJSONString(bidrequest));
			}
		} catch (Exception e) {
			log.warn("ssp:{}, ep:{}, parse err:{}", sspEp.getSspName(), sspEp.getSspEpId(), Util.genStackInfo(e), e);
			unifiedRequest.setNeedForward(false);
			return unifiedRequest;
		}

		unifiedRequest.setMediaReqId(bidrequest.id);
		Map<String, Impression> imps = reqBody2UnifiedReqImp(bidrequest.imp);
		if (!imps.isEmpty()) {
			unifiedRequest.setImps(imps);
		}

		unifiedRequest.setApp(reqBody2UnifiedReqApp(bidrequest.app));
		unifiedRequest.setDevice(reqBody2UnifiedReqDevice(bidrequest.device));
		unifiedRequest.setUser(reqBody2UnifiedReqUser(bidrequest.user));
		unifiedRequest.setTest(bidrequest.test);
		unifiedRequest.setAt(bidrequest.at);
		unifiedRequest.setTmax(bidrequest.tmax);
		unifiedRequest.setAllimps(bidrequest.allimps);
		unifiedRequest.setSource(reqBody2UnifiedReqSource(bidrequest.source));
		unifiedRequest.setRegs(reqBody2UnifiedReqRegs(bidrequest.regs));

		if (bidrequest.bcat != null) {
			unifiedRequest.setBcat(new ArrayList<>(bidrequest.bcat));
		}
		if (bidrequest.badv != null) {
			unifiedRequest.setBadv(new ArrayList<>(bidrequest.badv));
		}
		if (bidrequest.bapp != null) {
			unifiedRequest.setBapp(new ArrayList<>(bidrequest.bapp));
		}
		if (bidrequest.wseat != null) {
			unifiedRequest.setWseat(new ArrayList<>(bidrequest.wseat));
		}
		if (bidrequest.bseat != null) {
			unifiedRequest.setBseat(new ArrayList<>(bidrequest.bseat));
		}
		if (bidrequest.cur != null) {
			unifiedRequest.setCur(new ArrayList<>(bidrequest.cur));
		}
		if (bidrequest.wlang != null) {
			unifiedRequest.setWlang(new ArrayList<>(bidrequest.wlang));
		}

		return unifiedRequest;
	}

	@Override
	public WebEyeRequest unifiedReq2ProtocolReq(SessionContext sessionContext, UnifiedRequest unifiedReq, DspEpObj dspEpObj) throws Exception {
		return null;
	}

	@Override
	public void trafficFusion(SessionContext sessionContext, WebEyeRequest webEyeRequest, DspEpObj dspEpObj) {
		if (MapUtil.isEmpty(sessionContext.getDspFusionTarget()) || !sessionContext.getDspFusionTarget().containsKey(dspEpObj.getDspId())) {
			return;
		}
		Map<String, FusionProvider.TargetTraffic> impTargetTraffic = sessionContext.getDspFusionTarget().get(dspEpObj.getDspId());
		if (MapUtil.isEmpty(impTargetTraffic)) {
			return;
		}
		for (Imp imp : webEyeRequest.imp) {
			FusionProvider.TargetTraffic traffic = impTargetTraffic.get(imp.id);
			if (traffic == null) {
				continue;
			}
			if (StrUtil.isNotBlank(traffic.getTarAppId())) {
				webEyeRequest.app.id = traffic.getTarAppId();
			}
			if (StrUtil.isNotBlank(traffic.getTarPkg())) {
				webEyeRequest.app.bundle = traffic.getTarPkg();
			}
			if (StrUtil.isNotBlank(traffic.getTarTagId())) {
				imp.tagid = traffic.getTarTagId();
			}
		}
		log.info("dsp name:{}, id: {}, ep: {}, after fusion request:{}", dspEpObj.getName(), dspEpObj.getDspId(), dspEpObj.getDspEpId(), JSON.toJSONString(webEyeRequest));
	}

	@Override
	public WebEyeResponse unifiedResp2ProtocolResp(SessionContext sessionContext, UnifiedResponse unifiedResp) throws Exception {
		WebEyeResponse webeyeResponse = new WebEyeResponse();
		UnifiedRequest unifiedRequest = sessionContext.getUnifiedRequest();
		webeyeResponse.id = sessionContext.getUnifiedRequest().getMediaReqId();
		webeyeResponse.bidid = unifiedResp.getBidId();
		webeyeResponse.cur = unifiedResp.getCur();
		webeyeResponse.customdata = unifiedResp.getCustomdata();
		webeyeResponse.nbr = unifiedResp.getNbr();
		Integer settlementType = sessionContext.getSspEp().getSettlementType();
		webeyeResponse.seatbid = unifiedResp.getSeatbids() != null ?
				unifiedResp.getSeatbids().stream()
                        .map(a -> unifiedResp2WebEyeSeatBid(a, settlementType, unifiedRequest))
						.collect(Collectors.toList()) : null;
		if (CollUtil.isNotEmpty(webeyeResponse.seatbid) &&
				CollUtil.isNotEmpty(webeyeResponse.seatbid.get(0).bid)) {
			sessionContext.setBidDealId(webeyeResponse.seatbid.get(0).bid.get(0).dealid);
		}
		return webeyeResponse;
	}

	@Override
	public UnifiedResponse respBody2UnifiedResp(int code, byte[] body, DspEpObj dspEpObj) throws Exception {
		return null;
	}

	@Override
	public Class<WebEyeRequest> getTypeT() {
		return WebEyeRequest.class;
	}

	@Override
	public Class<WebEyeResponse> getTypeR() {
		return WebEyeResponse.class;
	}

	private Map<String, Impression> reqBody2UnifiedReqImp(List<WebEyeRequest.WebEyeImp> impList) {
		Map<String, Impression> imps = new HashMap<>();
		// 转化imp信息
		if (impList != null && !impList.isEmpty()) {
			for (WebEyeRequest.WebEyeImp imp : impList) {
				Impression i = reqBody2UnifiedReqImp(imp);
				imps.put(i.getImpId(), i);
			}
		}
		return imps;
	}

	private Impression reqBody2UnifiedReqImp(WebEyeRequest.WebEyeImp webEyeImp) {
		if (webEyeImp == null) {
			return null;
		}
		Impression impression = new Impression();
		impression.setImpId(webEyeImp.id);

		impression.setBidfloor((long) (webEyeImp.bidfloor * Constants.PRICE_MULTIPLY_MILLIONS));
		impression.setBidfloorcur(webEyeImp.bidfloorcur);
		impression.setTagId(webEyeImp.tagid);

		if (webEyeImp.metric != null && !webEyeImp.metric.isEmpty()) {
			impression.setMetric(new ArrayList<>());
			for (com.iflytek.traffic.protocol.rtb25.request.Metric metric : webEyeImp.metric) {
				Metric innerMetric = new Metric(metric);
				impression.getMetric().add(innerMetric);
			}
		}

		if (webEyeImp.pmp != null) {
			impression.setPmp(new Pmp(webEyeImp.pmp));
		}
		impression.setInstl(webEyeImp.instl);
		impression.setSecure(webEyeImp.secure);
		impression.setDisplaymanager(webEyeImp.displaymanager);
		impression.setDisplaymanagerver(webEyeImp.displaymanagerver);
		impression.setClickbrowser(webEyeImp.clickbrowser);
		impression.setIframebuster(webEyeImp.iframebuster);
		impression.setExp(webEyeImp.exp);

		if (webEyeImp.ext != null) {
			Impression.Ext ext = new Impression.Ext();
			ext.setReward(webEyeImp.ext.reward);
			ext.setDeeplink(webEyeImp.ext.deeplink);
			ext.setFallback(webEyeImp.ext.fallback);
			impression.setExt(ext);
		}

		// 广告素材
		if (webEyeImp.banner != null) {
			impression.setBanner(new Banner(webEyeImp.banner));
			impression.setAdType(Asset.AdType.SUPPER_BANNER);
			impression.getAdUnitSize().add(webEyeImp.banner.w + "*" + webEyeImp.banner.h);
			impression.setRequestAdType(Asset.AdType.SUPPER_BANNER);
		}
		if (webEyeImp.video != null) {
			impression.setVideo(new Video(webEyeImp.video));
			impression.setAdType(Asset.AdType.SUPPER_VIDEO);
			impression.getAdUnitSize().add(webEyeImp.video.w + "*" + webEyeImp.video.h);
			if (webEyeImp.ext != null) {
				if (webEyeImp.ext.reward != null && webEyeImp.ext.reward == 1) {
//					impression.setAdType(Asset.AdType.SUPPER_REWARD_VIDEO);
				}
			}
			if (webEyeImp.video.ext != null && webEyeImp.video.ext.rewarded != null && webEyeImp.video.ext.rewarded == 1) {
//				impression.setAdType(Asset.AdType.SUPPER_REWARD_VIDEO);
				if (impression.getExt() != null) {
					impression.getExt().setReward(webEyeImp.video.ext.rewarded);
				} else {
					Impression.Ext ext = new Impression.Ext();
					ext.setReward(webEyeImp.video.ext.rewarded);
					impression.setExt(ext);
				}
			}
			impression.setRequestAdType(Asset.AdType.SUPPER_VIDEO);
		}
		if (webEyeImp.audio!= null) {
			impression.setAudio(new Audio(webEyeImp.audio));
		}
		if (webEyeImp.native1 != null) {
			impression.setNative1(webEyeImp.native1);
			impression.setAdType(Asset.AdType.SUPPER_NATIVE);
			impression.getNativeAdunitSize(webEyeImp.native1);
			impression.setRequestAdType(Asset.AdType.SUPPER_NATIVE);
		}

		return impression;

	}

	private App reqBody2UnifiedReqApp(com.iflytek.traffic.protocol.rtb25.request.App app) {
		if (app != null) {
			return new App(app);
		}
		return null;
	}

	private Device reqBody2UnifiedReqDevice(com.iflytek.traffic.protocol.rtb25.request.Device device) {
		if (device != null) {
			return new Device(device);
		}
		return null;
	}

	private User reqBody2UnifiedReqUser(com.iflytek.traffic.protocol.rtb25.request.User user) {
		if (user != null) {
			return new User(user);
		}
		return null;
	}

	private Source reqBody2UnifiedReqSource(com.iflytek.traffic.protocol.rtb25.request.Source source) {
		if (source != null) {
			return new Source(source);
		}
		return null;
	}

	private Regs reqBody2UnifiedReqRegs(WebEyeRequest.WebEyeRegs regs) {
		if (regs != null) {
			Regs regs1 = new Regs();
			regs1.setCoppa(regs.coppa);
			if (regs.ext != null) {
				Regs.Ext ext = new Regs.Ext();
                ext.setGdpr(regs.ext.gdpr);
                regs1.setExt(ext);
			}
			return regs1;
		}
		return null;
	}

	private WebEyeResponse.WebEyeSeatBid unifiedResp2WebEyeSeatBid(SeatBid seatBid, Integer settlementType, UnifiedRequest unifiedRequest) {
		WebEyeResponse.WebEyeSeatBid webEyeSeatBid = new WebEyeResponse.WebEyeSeatBid();
		webEyeSeatBid.seat = seatBid.getSeat();
		webEyeSeatBid.group = seatBid.getGroup();
		if (CollUtil.isNotEmpty(seatBid.getBids())) {
            webEyeSeatBid.bid = seatBid.getBids().stream()
					.map(bid -> {
						String impId = bid.getImpId();
						Impression imp = unifiedRequest.getImps().get(impId);
						return unifiedResp2WebEyeBid(bid, settlementType, imp);
					})
					.collect(Collectors.toList());
		}
		return webEyeSeatBid;
	}
	
	private WebEyeResponse.WebEyeBid unifiedResp2WebEyeBid(Bid bid, Integer settlementType, Impression imp) {
		if (bid == null) {
			return null;
		}
		WebEyeResponse.WebEyeBid webEyeBid = new WebEyeResponse.WebEyeBid();
		webEyeBid.id = bid.getBidId();
		webEyeBid.impid = bid.getImpId();
		if (bid.getPrice() != null) {
			webEyeBid.price = (float)(bid.getPrice() *1.0/ Constants.PRICE_MULTIPLY_MILLIONS);
		}
		webEyeBid.nurl = bid.getNurl();
		if (settlementType == Constants.SETTLEMENTTYPE_BURL) {
			webEyeBid.burl = bid.getBurl();
		}
		webEyeBid.adm = bid.getAdm();
		webEyeBid.adid = bid.getAdid();
		webEyeBid.adomain = bid.getAdomain() != null ? new ArrayList<>(bid.getAdomain()) : null;
		webEyeBid.bundle = bid.getBundle();
		webEyeBid.iurl = bid.getIurl();
		webEyeBid.cid = bid.getCid();
		webEyeBid.crid = bid.getCrid();
		webEyeBid.tactic = bid.getTactic();
		webEyeBid.cat = bid.getCat() != null ? new ArrayList<>(bid.getCat()) : null;
		webEyeBid.attr = bid.getAttr() != null ? new ArrayList<>(bid.getAttr()) : null;
		webEyeBid.api = bid.getApi();
		webEyeBid.protocol = bid.getProtocol();
		webEyeBid.qagmediarating = bid.getQagmediarating();
		webEyeBid.language = bid.getLanguage();
		webEyeBid.dealid = bid.getDealId();
		webEyeBid.w = bid.getW();
		webEyeBid.h = bid.getH();
		webEyeBid.wratio = bid.getWratio();
		webEyeBid.hratio = bid.getHratio();
		webEyeBid.exp = bid.getExp();
		if (bid.getExt() != null) {
			WebEyeResponse.WebEyeBidExt ext = new WebEyeResponse.WebEyeBidExt();
			ext.deeplink = bid.getExt().deeplink;
			ext.fallback = bid.getExt().fallback;
			ext.storeurl = bid.getExt().storeurl;
			webEyeBid.ext = ext;
		}

		Asset.AdType adType = imp.getAdType();
		if (adType == Asset.AdType.SUPPER_VIDEO ) {
			if (imp.getExt() != null) {
				if (imp.getExt().getDeeplink() != null && imp.getExt().getDeeplink() == 1 && imp.getExt().getFallback() != null && imp.getExt().getFallback() == 1) {
					if (bid.getExt() != null && StrUtil.isNotEmpty(bid.getExt().deeplink) && StrUtil.isNotEmpty(bid.getExt().fallback)) {
						webEyeBid.adm = replace(bid.getAdm(), bid.getExt().fallback);
					}
                } else if (imp.getExt().getDeeplink() != null && imp.getExt().getDeeplink() == 1) {
					if (bid.getExt() != null) {
						if (StrUtil.isNotEmpty(bid.getExt().fallback)) {
							webEyeBid.adm = replace(bid.getAdm(), bid.getExt().fallback);
						} else if (StrUtil.isNotEmpty(bid.getExt().deeplink)){
							webEyeBid.adm = replace(bid.getAdm(), bid.getExt().deeplink);
						}
					}
				}
			}

		}
		return webEyeBid;
	}

	private String replace(String adm, String url) {
		if (StrUtil.isEmpty(adm)) {
			return adm;
		}
		if (StrUtil.isEmpty(url)) {
			return adm;
		}

		adm = adm.replaceAll(
				"(<ClickThrough[^>]*>)(<!\\[CDATA\\[)?([^<]*?)(]]>)?(</ClickThrough>)",
				"$1$2" + url + "$4$5"
		);

		adm = adm.replaceAll(
				"(<CompanionClickThrough[^>]*>)(<!\\[CDATA\\[)?([^<]*?)(]]>)?(</CompanionClickThrough>)",
				"$1$2" + url + "$4$5"
		);

		return adm;
	}
}
