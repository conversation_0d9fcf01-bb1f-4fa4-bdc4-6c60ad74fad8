package com.iflytek.traffic.protocol.webeye;

import com.alibaba.fastjson.annotation.JSONField;
import com.iflytek.traffic.protocol.rtb25.request.Device;
import com.iflytek.traffic.protocol.rtb25.request.Imp;
import com.iflytek.traffic.protocol.rtb25.request.Regs;
import com.iflytek.traffic.protocol.rtb25.request.Rtb25Request;

import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2025/6/25 15:54
 */
public class WebEyeRequest extends Rtb25Request {
	public List<WebEyeImp> imp;

	public WebEyeDevice device;

	public WebEyeRegs regs;


	public static class WebEyeImp extends Imp {

		public ImpExt ext;

	}

	public static class ImpExt {

		public Integer adtype;

		public Integer reward;

		public Integer deeplink;

		public Integer fallback;

		public Skadn skadn;

	}

	public static class Skadn {

		public String version;

		public List<String> versions;

		public String sourceapp;

		public List<String> skadnetids;

	}

	public static class WebEyeDevice extends Device {

		public DeviceExt ext;

	}

	public static class DeviceExt {

		public String ifv;

		public Integer atts;

	}

	public static class WebEyeRegs extends Regs {

		public RegsExt ext;

	}

	public static class RegsExt {

		public Integer gdpr;

		@JSONField(name = "us_privacy")
		public String usprivacy;

	}
}
