package com.iflytek.traffic.protocol.webeye;

import com.iflytek.traffic.protocol.rtb25.response.Bid;
import com.iflytek.traffic.protocol.rtb25.response.Rtb25Response;
import com.iflytek.traffic.protocol.rtb25.response.SeatBid;

import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2025/6/25 16:01
 */
public class WebEyeResponse extends Rtb25Response {
	public String bidId;

	public List<WebEyeSeatBid> seatbid;

	public static class WebEyeSeatBid extends SeatBid {

		public List<WebEyeBid> bid;

	}

	public static class WebEyeBid extends Bid {

		public WebEyeBidExt ext;

	}

	public static class WebEyeBidExt {

		public Skadn skadn;

		public String deeplink;

		public String fallback;

		public String storeurl;

	}

	public static class Skadn {

		public String version;

		public String campaign;

		public String itunesitem;

		public String sourceapp;

		public String network;

		public String nonce;

		public String timestamp;

		public List<FidelitySignature> fidelities;

	}

	public static class FidelitySignature {

		public String fidelity;

		public String nonce;

		public String timestamp;

		public String signature;

	}
}
