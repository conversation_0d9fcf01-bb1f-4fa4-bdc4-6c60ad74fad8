package com.iflytek.traffic.protocol.webeye;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * <AUTHOR>
 * @datetime 2025/6/26 0:13
 */
public class WebEyeNative {

	public String request;

	public String ver;

	public static class Request {

		@J<PERSON>NField(name = "native")
		public NativeRequest native1;

	}

	public static class NativeRequest {

		public String ver;

		public List<Asset> assets;

		public static class Asset {

			public Integer id;

			public Integer required;

			public Title title;

			public Image img;

			public Video video;

			public Data data;

		}

		public static class Title {

			public int len;

		}

		public static class Image {

			public int type;

			public int w;

			public int wmin;

			public int h;

			public int hmin;

			public List<String> mimes;

		}

		public static class Video {

			public List<String> mimes;

			public int minduration;

			public int maxduration;

			public List<Integer> protocols;

		}

		public static class Data {

			public int type;

			public int len;

		}

	}

	public static class NativeResponse {

		public String ver;

		public List<Asset> assets;

		public Link link;

		public List<String> imptrackers;

		public static class Asset {

			public Integer id;

			public Integer required;

			public Title title;

			public Image img;

			public Video video;

			public Data data;

			public Link link;

		}

		public static class Title {

			public String text;

		}

		public static class Image {

			public String url;

			public Integer w;

			public Integer h;

		}

		public static class Video {

			public String vasttag;

		}

		public static class Data {

			public String label;

			public String value;

		}

		public static class Link {

			public String url;

			public List<String> clicktrackers;

		}

	}

}
