// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: material_log.proto

package com.iflytek.traffic.log;

public final class MaterialLogProto {
    private MaterialLogProto() {
    }

    public static void registerAllExtensions(
            com.google.protobuf.ExtensionRegistryLite registry) {
    }

    public static void registerAllExtensions(
            com.google.protobuf.ExtensionRegistry registry) {
        registerAllExtensions(
                (com.google.protobuf.ExtensionRegistryLite) registry);
    }

    public interface MaterialLogOrBuilder extends
            // @@protoc_insertion_point(interface_extends:com.iflytek.traffic.log.MaterialLog)
            com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * dsp 信息
         * </pre>
         *
         * <code>required int64 dsp_id = 1;</code>
         * @return Whether the dspId field is set.
         */
        boolean hasDspId();

        /**
         * <pre>
         * dsp 信息
         * </pre>
         *
         * <code>required int64 dsp_id = 1;</code>
         * @return The dspId.
         */
        long getDspId();

        /**
         * <code>required int64 dsp_ep_id = 2;</code>
         * @return Whether the dspEpId field is set.
         */
        boolean hasDspEpId();

        /**
         * <code>required int64 dsp_ep_id = 2;</code>
         * @return The dspEpId.
         */
        long getDspEpId();

        /**
         * <pre>
         * 地区信息
         * </pre>
         *
         * <code>required string region = 4;</code>
         * @return Whether the region field is set.
         */
        boolean hasRegion();

        /**
         * <pre>
         * 地区信息
         * </pre>
         *
         * <code>required string region = 4;</code>
         * @return The region.
         */
        String getRegion();

        /**
         * <pre>
         * 地区信息
         * </pre>
         *
         * <code>required string region = 4;</code>
         * @return The bytes for region.
         */
        com.google.protobuf.ByteString
        getRegionBytes();

        /**
         * <pre>
         * ssp 信息
         * </pre>
         *
         * <code>required int64 ssp_id = 5;</code>
         * @return Whether the sspId field is set.
         */
        boolean hasSspId();

        /**
         * <pre>
         * ssp 信息
         * </pre>
         *
         * <code>required int64 ssp_id = 5;</code>
         * @return The sspId.
         */
        long getSspId();

        /**
         * <code>required int64 ssp_ep_id = 6;</code>
         * @return Whether the sspEpId field is set.
         */
        boolean hasSspEpId();

        /**
         * <code>required int64 ssp_ep_id = 6;</code>
         * @return The sspEpId.
         */
        long getSspEpId();

        /**
         * <code>optional string ssp_hash_id = 7;</code>
         * @return Whether the sspHashId field is set.
         */
        boolean hasSspHashId();

        /**
         * <code>optional string ssp_hash_id = 7;</code>
         * @return The sspHashId.
         */
        String getSspHashId();

        /**
         * <code>optional string ssp_hash_id = 7;</code>
         * @return The bytes for sspHashId.
         */
        com.google.protobuf.ByteString
        getSspHashIdBytes();

        /**
         * <pre>
         * 物料
         * </pre>
         *
         * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
         * @return Whether the material field is set.
         */
        boolean hasMaterial();

        /**
         * <pre>
         * 物料
         * </pre>
         *
         * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
         * @return The material.
         */
        MaterialLog.Material getMaterial();

        /**
         * <pre>
         * 物料
         * </pre>
         *
         * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
         */
        MaterialLog.MaterialOrBuilder getMaterialOrBuilder();

        /**
         * <pre>
         * 如果为 true 可能需要往 t_ssp_material_info 插入数据
         * </pre>
         *
         * <code>required bool ssp_audit = 9;</code>
         * @return Whether the sspAudit field is set.
         */
        boolean hasSspAudit();

        /**
         * <pre>
         * 如果为 true 可能需要往 t_ssp_material_info 插入数据
         * </pre>
         *
         * <code>required bool ssp_audit = 9;</code>
         * @return The sspAudit.
         */
        boolean getSspAudit();

        /**
         * <code>optional string ssp_audit_hash_id = 11;</code>
         * @return Whether the sspAuditHashId field is set.
         */
        boolean hasSspAuditHashId();

        /**
         * <code>optional string ssp_audit_hash_id = 11;</code>
         * @return The sspAuditHashId.
         */
        String getSspAuditHashId();

        /**
         * <code>optional string ssp_audit_hash_id = 11;</code>
         * @return The bytes for sspAuditHashId.
         */
        com.google.protobuf.ByteString
        getSspAuditHashIdBytes();

        /**
         * <pre>
         * 如果为 true 可能需要往 t_machine_audit_material_info  插入数据
         * </pre>
         *
         * <code>required bool machine_audit = 10;</code>
         * @return Whether the machineAudit field is set.
         */
        boolean hasMachineAudit();

        /**
         * <pre>
         * 如果为 true 可能需要往 t_machine_audit_material_info  插入数据
         * </pre>
         *
         * <code>required bool machine_audit = 10;</code>
         * @return The machineAudit.
         */
        boolean getMachineAudit();

        /**
         * <code>optional string machine_audit_hash_id = 12;</code>
         * @return Whether the machineAuditHashId field is set.
         */
        boolean hasMachineAuditHashId();

        /**
         * <code>optional string machine_audit_hash_id = 12;</code>
         * @return The machineAuditHashId.
         */
        String getMachineAuditHashId();

        /**
         * <code>optional string machine_audit_hash_id = 12;</code>
         * @return The bytes for machineAuditHashId.
         */
        com.google.protobuf.ByteString
        getMachineAuditHashIdBytes();

        /**
         * <code>required int32 os = 13;</code>
         * @return Whether the os field is set.
         */
        boolean hasOs();

        /**
         * <code>required int32 os = 13;</code>
         * @return The os.
         */
        int getOs();
    }

    /**
     * Protobuf type {@code com.iflytek.traffic.log.MaterialLog}
     */
    public static final class MaterialLog extends
            com.google.protobuf.GeneratedMessageV3 implements
            // @@protoc_insertion_point(message_implements:com.iflytek.traffic.log.MaterialLog)
            MaterialLogOrBuilder {
        private static final long serialVersionUID = 0L;

        // Use MaterialLog.newBuilder() to construct.
        private MaterialLog(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
            super(builder);
        }

        private MaterialLog() {
            region_ = "";
            sspHashId_ = "";
            sspAuditHashId_ = "";
            machineAuditHashId_ = "";
        }

        @Override
        @SuppressWarnings({"unused"})
        protected Object newInstance(
                UnusedPrivateParameter unused) {
            return new MaterialLog();
        }

        @Override
        public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
            return this.unknownFields;
        }

        public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
            return MaterialLogProto.internal_static_com_iflytek_traffic_log_MaterialLog_descriptor;
        }

        @Override
        protected FieldAccessorTable
        internalGetFieldAccessorTable() {
            return MaterialLogProto.internal_static_com_iflytek_traffic_log_MaterialLog_fieldAccessorTable
                    .ensureFieldAccessorsInitialized(
                            MaterialLog.class, Builder.class);
        }

        public interface MaterialOrBuilder extends
                // @@protoc_insertion_point(interface_extends:com.iflytek.traffic.log.MaterialLog.Material)
                com.google.protobuf.MessageOrBuilder {

            /**
             * <code>required string hash_id = 1;</code>
             * @return Whether the hashId field is set.
             */
            boolean hasHashId();

            /**
             * <code>required string hash_id = 1;</code>
             * @return The hashId.
             */
            String getHashId();

            /**
             * <code>required string hash_id = 1;</code>
             * @return The bytes for hashId.
             */
            com.google.protobuf.ByteString
            getHashIdBytes();

            /**
             * <pre>
             * t_dsp_material
             * </pre>
             *
             * <code>optional string adm = 2;</code>
             * @return Whether the adm field is set.
             */
            boolean hasAdm();

            /**
             * <pre>
             * t_dsp_material
             * </pre>
             *
             * <code>optional string adm = 2;</code>
             * @return The adm.
             */
            String getAdm();

            /**
             * <pre>
             * t_dsp_material
             * </pre>
             *
             * <code>optional string adm = 2;</code>
             * @return The bytes for adm.
             */
            com.google.protobuf.ByteString
            getAdmBytes();

            /**
             * <code>optional string bundle = 3;</code>
             * @return Whether the bundle field is set.
             */
            boolean hasBundle();

            /**
             * <code>optional string bundle = 3;</code>
             * @return The bundle.
             */
            String getBundle();

            /**
             * <code>optional string bundle = 3;</code>
             * @return The bytes for bundle.
             */
            com.google.protobuf.ByteString
            getBundleBytes();

            /**
             * <code>optional string iurl = 4;</code>
             * @return Whether the iurl field is set.
             */
            boolean hasIurl();

            /**
             * <code>optional string iurl = 4;</code>
             * @return The iurl.
             */
            String getIurl();

            /**
             * <code>optional string iurl = 4;</code>
             * @return The bytes for iurl.
             */
            com.google.protobuf.ByteString
            getIurlBytes();

            /**
             * <code>optional string cid = 5;</code>
             * @return Whether the cid field is set.
             */
            boolean hasCid();

            /**
             * <code>optional string cid = 5;</code>
             * @return The cid.
             */
            String getCid();

            /**
             * <code>optional string cid = 5;</code>
             * @return The bytes for cid.
             */
            com.google.protobuf.ByteString
            getCidBytes();

            /**
             * <code>repeated string cat = 6;</code>
             * @return A list containing the cat.
             */
            java.util.List<String>
            getCatList();

            /**
             * <code>repeated string cat = 6;</code>
             * @return The count of cat.
             */
            int getCatCount();

            /**
             * <code>repeated string cat = 6;</code>
             * @param index The index of the element to return.
             * @return The cat at the given index.
             */
            String getCat(int index);

            /**
             * <code>repeated string cat = 6;</code>
             * @param index The index of the value to return.
             * @return The bytes of the cat at the given index.
             */
            com.google.protobuf.ByteString
            getCatBytes(int index);

            /**
             * <code>required string crid = 7;</code>
             * @return Whether the crid field is set.
             */
            boolean hasCrid();

            /**
             * <code>required string crid = 7;</code>
             * @return The crid.
             */
            String getCrid();

            /**
             * <code>required string crid = 7;</code>
             * @return The bytes for crid.
             */
            com.google.protobuf.ByteString
            getCridBytes();

            /**
             * <code>repeated int32 attr = 8;</code>
             * @return A list containing the attr.
             */
            java.util.List<Integer> getAttrList();

            /**
             * <code>repeated int32 attr = 8;</code>
             * @return The count of attr.
             */
            int getAttrCount();

            /**
             * <code>repeated int32 attr = 8;</code>
             * @param index The index of the element to return.
             * @return The attr at the given index.
             */
            int getAttr(int index);

            /**
             * <code>optional int32 api = 9;</code>
             * @return Whether the api field is set.
             */
            boolean hasApi();

            /**
             * <code>optional int32 api = 9;</code>
             * @return The api.
             */
            int getApi();

            /**
             * <code>optional int32 protocol = 10;</code>
             * @return Whether the protocol field is set.
             */
            boolean hasProtocol();

            /**
             * <code>optional int32 protocol = 10;</code>
             * @return The protocol.
             */
            int getProtocol();

            /**
             * <code>optional string dealId = 11;</code>
             * @return Whether the dealId field is set.
             */
            boolean hasDealId();

            /**
             * <code>optional string dealId = 11;</code>
             * @return The dealId.
             */
            String getDealId();

            /**
             * <code>optional string dealId = 11;</code>
             * @return The bytes for dealId.
             */
            com.google.protobuf.ByteString
            getDealIdBytes();

            /**
             * <code>optional int32 w = 12;</code>
             * @return Whether the w field is set.
             */
            boolean hasW();

            /**
             * <code>optional int32 w = 12;</code>
             * @return The w.
             */
            int getW();

            /**
             * <code>optional int32 h = 13;</code>
             * @return Whether the h field is set.
             */
            boolean hasH();

            /**
             * <code>optional int32 h = 13;</code>
             * @return The h.
             */
            int getH();

            /**
             * <code>optional int32 wratio = 14;</code>
             * @return Whether the wratio field is set.
             */
            boolean hasWratio();

            /**
             * <code>optional int32 wratio = 14;</code>
             * @return The wratio.
             */
            int getWratio();

            /**
             * <code>optional int32 hratio = 15;</code>
             * @return Whether the hratio field is set.
             */
            boolean hasHratio();

            /**
             * <code>optional int32 hratio = 15;</code>
             * @return The hratio.
             */
            int getHratio();

            /**
             * <code>optional string ext = 16;</code>
             * @return Whether the ext field is set.
             */
            boolean hasExt();

            /**
             * <code>optional string ext = 16;</code>
             * @return The ext.
             */
            String getExt();

            /**
             * <code>optional string ext = 16;</code>
             * @return The bytes for ext.
             */
            com.google.protobuf.ByteString
            getExtBytes();

            /**
             * <code>optional string admHashId = 17;</code>
             * @return Whether the admHashId field is set.
             */
            boolean hasAdmHashId();

            /**
             * <code>optional string admHashId = 17;</code>
             * @return The admHashId.
             */
            String getAdmHashId();

            /**
             * <code>optional string admHashId = 17;</code>
             * @return The bytes for admHashId.
             */
            com.google.protobuf.ByteString
            getAdmHashIdBytes();

            /**
             * <code>optional int32 adType = 18;</code>
             * @return Whether the adType field is set.
             */
            boolean hasAdType();

            /**
             * <code>optional int32 adType = 18;</code>
             * @return The adType.
             */
            int getAdType();
        }

        /**
         * Protobuf type {@code com.iflytek.traffic.log.MaterialLog.Material}
         */
        public static final class Material extends
                com.google.protobuf.GeneratedMessageV3 implements
                // @@protoc_insertion_point(message_implements:com.iflytek.traffic.log.MaterialLog.Material)
                MaterialOrBuilder {
            private static final long serialVersionUID = 0L;

            // Use Material.newBuilder() to construct.
            private Material(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
                super(builder);
            }

            private Material() {
                hashId_ = "";
                adm_ = "";
                bundle_ = "";
                iurl_ = "";
                cid_ = "";
                cat_ = com.google.protobuf.LazyStringArrayList.EMPTY;
                crid_ = "";
                attr_ = emptyIntList();
                dealId_ = "";
                ext_ = "";
                admHashId_ = "";
            }

            @Override
            @SuppressWarnings({"unused"})
            protected Object newInstance(
                    UnusedPrivateParameter unused) {
                return new Material();
            }

            @Override
            public final com.google.protobuf.UnknownFieldSet
            getUnknownFields() {
                return this.unknownFields;
            }

            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return MaterialLogProto.internal_static_com_iflytek_traffic_log_MaterialLog_Material_descriptor;
            }

            @Override
            protected FieldAccessorTable
            internalGetFieldAccessorTable() {
                return MaterialLogProto.internal_static_com_iflytek_traffic_log_MaterialLog_Material_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                Material.class, Builder.class);
            }

            private int bitField0_;
            public static final int HASH_ID_FIELD_NUMBER = 1;
            private volatile Object hashId_;

            /**
             * <code>required string hash_id = 1;</code>
             * @return Whether the hashId field is set.
             */
            @Override
            public boolean hasHashId() {
                return ((bitField0_ & 0x00000001) != 0);
            }

            /**
             * <code>required string hash_id = 1;</code>
             * @return The hashId.
             */
            @Override
            public String getHashId() {
                Object ref = hashId_;
                if (ref instanceof String) {
                    return (String) ref;
                } else {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    String s = bs.toStringUtf8();
                    if (bs.isValidUtf8()) {
                        hashId_ = s;
                    }
                    return s;
                }
            }

            /**
             * <code>required string hash_id = 1;</code>
             * @return The bytes for hashId.
             */
            @Override
            public com.google.protobuf.ByteString
            getHashIdBytes() {
                Object ref = hashId_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (String) ref);
                    hashId_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            public static final int ADM_FIELD_NUMBER = 2;
            private volatile Object adm_;

            /**
             * <pre>
             * t_dsp_material
             * </pre>
             *
             * <code>optional string adm = 2;</code>
             * @return Whether the adm field is set.
             */
            @Override
            public boolean hasAdm() {
                return ((bitField0_ & 0x00000002) != 0);
            }

            /**
             * <pre>
             * t_dsp_material
             * </pre>
             *
             * <code>optional string adm = 2;</code>
             * @return The adm.
             */
            @Override
            public String getAdm() {
                Object ref = adm_;
                if (ref instanceof String) {
                    return (String) ref;
                } else {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    String s = bs.toStringUtf8();
                    if (bs.isValidUtf8()) {
                        adm_ = s;
                    }
                    return s;
                }
            }

            /**
             * <pre>
             * t_dsp_material
             * </pre>
             *
             * <code>optional string adm = 2;</code>
             * @return The bytes for adm.
             */
            @Override
            public com.google.protobuf.ByteString
            getAdmBytes() {
                Object ref = adm_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (String) ref);
                    adm_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            public static final int BUNDLE_FIELD_NUMBER = 3;
            private volatile Object bundle_;

            /**
             * <code>optional string bundle = 3;</code>
             * @return Whether the bundle field is set.
             */
            @Override
            public boolean hasBundle() {
                return ((bitField0_ & 0x00000004) != 0);
            }

            /**
             * <code>optional string bundle = 3;</code>
             * @return The bundle.
             */
            @Override
            public String getBundle() {
                Object ref = bundle_;
                if (ref instanceof String) {
                    return (String) ref;
                } else {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    String s = bs.toStringUtf8();
                    if (bs.isValidUtf8()) {
                        bundle_ = s;
                    }
                    return s;
                }
            }

            /**
             * <code>optional string bundle = 3;</code>
             * @return The bytes for bundle.
             */
            @Override
            public com.google.protobuf.ByteString
            getBundleBytes() {
                Object ref = bundle_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (String) ref);
                    bundle_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            public static final int IURL_FIELD_NUMBER = 4;
            private volatile Object iurl_;

            /**
             * <code>optional string iurl = 4;</code>
             * @return Whether the iurl field is set.
             */
            @Override
            public boolean hasIurl() {
                return ((bitField0_ & 0x00000008) != 0);
            }

            /**
             * <code>optional string iurl = 4;</code>
             * @return The iurl.
             */
            @Override
            public String getIurl() {
                Object ref = iurl_;
                if (ref instanceof String) {
                    return (String) ref;
                } else {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    String s = bs.toStringUtf8();
                    if (bs.isValidUtf8()) {
                        iurl_ = s;
                    }
                    return s;
                }
            }

            /**
             * <code>optional string iurl = 4;</code>
             * @return The bytes for iurl.
             */
            @Override
            public com.google.protobuf.ByteString
            getIurlBytes() {
                Object ref = iurl_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (String) ref);
                    iurl_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            public static final int CID_FIELD_NUMBER = 5;
            private volatile Object cid_;

            /**
             * <code>optional string cid = 5;</code>
             * @return Whether the cid field is set.
             */
            @Override
            public boolean hasCid() {
                return ((bitField0_ & 0x00000010) != 0);
            }

            /**
             * <code>optional string cid = 5;</code>
             * @return The cid.
             */
            @Override
            public String getCid() {
                Object ref = cid_;
                if (ref instanceof String) {
                    return (String) ref;
                } else {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    String s = bs.toStringUtf8();
                    if (bs.isValidUtf8()) {
                        cid_ = s;
                    }
                    return s;
                }
            }

            /**
             * <code>optional string cid = 5;</code>
             * @return The bytes for cid.
             */
            @Override
            public com.google.protobuf.ByteString
            getCidBytes() {
                Object ref = cid_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (String) ref);
                    cid_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            public static final int CAT_FIELD_NUMBER = 6;
            private com.google.protobuf.LazyStringList cat_;

            /**
             * <code>repeated string cat = 6;</code>
             * @return A list containing the cat.
             */
            public com.google.protobuf.ProtocolStringList
            getCatList() {
                return cat_;
            }

            /**
             * <code>repeated string cat = 6;</code>
             * @return The count of cat.
             */
            public int getCatCount() {
                return cat_.size();
            }

            /**
             * <code>repeated string cat = 6;</code>
             * @param index The index of the element to return.
             * @return The cat at the given index.
             */
            public String getCat(int index) {
                return cat_.get(index);
            }

            /**
             * <code>repeated string cat = 6;</code>
             * @param index The index of the value to return.
             * @return The bytes of the cat at the given index.
             */
            public com.google.protobuf.ByteString
            getCatBytes(int index) {
                return cat_.getByteString(index);
            }

            public static final int CRID_FIELD_NUMBER = 7;
            private volatile Object crid_;

            /**
             * <code>required string crid = 7;</code>
             * @return Whether the crid field is set.
             */
            @Override
            public boolean hasCrid() {
                return ((bitField0_ & 0x00000020) != 0);
            }

            /**
             * <code>required string crid = 7;</code>
             * @return The crid.
             */
            @Override
            public java.lang.String getCrid() {
                java.lang.Object ref = crid_;
                if (ref instanceof java.lang.String) {
                    return (java.lang.String) ref;
                } else {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    if (bs.isValidUtf8()) {
                        crid_ = s;
                    }
                    return s;
                }
            }

            /**
             * <code>required string crid = 7;</code>
             * @return The bytes for crid.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getCridBytes() {
                java.lang.Object ref = crid_;
                if (ref instanceof java.lang.String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    crid_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            public static final int ATTR_FIELD_NUMBER = 8;
            private com.google.protobuf.Internal.IntList attr_;

            /**
             * <code>repeated int32 attr = 8;</code>
             * @return A list containing the attr.
             */
            @java.lang.Override
            public java.util.List<java.lang.Integer>
            getAttrList() {
                return attr_;
            }

            /**
             * <code>repeated int32 attr = 8;</code>
             * @return The count of attr.
             */
            public int getAttrCount() {
                return attr_.size();
            }

            /**
             * <code>repeated int32 attr = 8;</code>
             * @param index The index of the element to return.
             * @return The attr at the given index.
             */
            public int getAttr(int index) {
                return attr_.getInt(index);
            }

            public static final int API_FIELD_NUMBER = 9;
            private int api_;

            /**
             * <code>optional int32 api = 9;</code>
             * @return Whether the api field is set.
             */
            @java.lang.Override
            public boolean hasApi() {
                return ((bitField0_ & 0x00000040) != 0);
            }

            /**
             * <code>optional int32 api = 9;</code>
             * @return The api.
             */
            @java.lang.Override
            public int getApi() {
                return api_;
            }

            public static final int PROTOCOL_FIELD_NUMBER = 10;
            private int protocol_;

            /**
             * <code>optional int32 protocol = 10;</code>
             * @return Whether the protocol field is set.
             */
            @java.lang.Override
            public boolean hasProtocol() {
                return ((bitField0_ & 0x00000080) != 0);
            }

            /**
             * <code>optional int32 protocol = 10;</code>
             * @return The protocol.
             */
            @java.lang.Override
            public int getProtocol() {
                return protocol_;
            }

            public static final int DEALID_FIELD_NUMBER = 11;
            private volatile java.lang.Object dealId_;

            /**
             * <code>optional string dealId = 11;</code>
             * @return Whether the dealId field is set.
             */
            @java.lang.Override
            public boolean hasDealId() {
                return ((bitField0_ & 0x00000100) != 0);
            }

            /**
             * <code>optional string dealId = 11;</code>
             * @return The dealId.
             */
            @java.lang.Override
            public java.lang.String getDealId() {
                java.lang.Object ref = dealId_;
                if (ref instanceof java.lang.String) {
                    return (java.lang.String) ref;
                } else {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    if (bs.isValidUtf8()) {
                        dealId_ = s;
                    }
                    return s;
                }
            }

            /**
             * <code>optional string dealId = 11;</code>
             * @return The bytes for dealId.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getDealIdBytes() {
                java.lang.Object ref = dealId_;
                if (ref instanceof java.lang.String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    dealId_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            public static final int W_FIELD_NUMBER = 12;
            private int w_;

            /**
             * <code>optional int32 w = 12;</code>
             * @return Whether the w field is set.
             */
            @java.lang.Override
            public boolean hasW() {
                return ((bitField0_ & 0x00000200) != 0);
            }

            /**
             * <code>optional int32 w = 12;</code>
             * @return The w.
             */
            @java.lang.Override
            public int getW() {
                return w_;
            }

            public static final int H_FIELD_NUMBER = 13;
            private int h_;

            /**
             * <code>optional int32 h = 13;</code>
             * @return Whether the h field is set.
             */
            @java.lang.Override
            public boolean hasH() {
                return ((bitField0_ & 0x00000400) != 0);
            }

            /**
             * <code>optional int32 h = 13;</code>
             * @return The h.
             */
            @java.lang.Override
            public int getH() {
                return h_;
            }

            public static final int WRATIO_FIELD_NUMBER = 14;
            private int wratio_;

            /**
             * <code>optional int32 wratio = 14;</code>
             * @return Whether the wratio field is set.
             */
            @java.lang.Override
            public boolean hasWratio() {
                return ((bitField0_ & 0x00000800) != 0);
            }

            /**
             * <code>optional int32 wratio = 14;</code>
             * @return The wratio.
             */
            @java.lang.Override
            public int getWratio() {
                return wratio_;
            }

            public static final int HRATIO_FIELD_NUMBER = 15;
            private int hratio_;

            /**
             * <code>optional int32 hratio = 15;</code>
             * @return Whether the hratio field is set.
             */
            @java.lang.Override
            public boolean hasHratio() {
                return ((bitField0_ & 0x00001000) != 0);
            }

            /**
             * <code>optional int32 hratio = 15;</code>
             * @return The hratio.
             */
            @java.lang.Override
            public int getHratio() {
                return hratio_;
            }

            public static final int EXT_FIELD_NUMBER = 16;
            private volatile java.lang.Object ext_;

            /**
             * <code>optional string ext = 16;</code>
             * @return Whether the ext field is set.
             */
            @java.lang.Override
            public boolean hasExt() {
                return ((bitField0_ & 0x00002000) != 0);
            }

            /**
             * <code>optional string ext = 16;</code>
             * @return The ext.
             */
            @java.lang.Override
            public java.lang.String getExt() {
                java.lang.Object ref = ext_;
                if (ref instanceof java.lang.String) {
                    return (java.lang.String) ref;
                } else {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    if (bs.isValidUtf8()) {
                        ext_ = s;
                    }
                    return s;
                }
            }

            /**
             * <code>optional string ext = 16;</code>
             * @return The bytes for ext.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getExtBytes() {
                java.lang.Object ref = ext_;
                if (ref instanceof java.lang.String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    ext_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            public static final int ADMHASHID_FIELD_NUMBER = 17;
            private volatile java.lang.Object admHashId_;

            /**
             * <code>optional string admHashId = 17;</code>
             * @return Whether the admHashId field is set.
             */
            @java.lang.Override
            public boolean hasAdmHashId() {
                return ((bitField0_ & 0x00004000) != 0);
            }

            /**
             * <code>optional string admHashId = 17;</code>
             * @return The admHashId.
             */
            @java.lang.Override
            public java.lang.String getAdmHashId() {
                java.lang.Object ref = admHashId_;
                if (ref instanceof java.lang.String) {
                    return (java.lang.String) ref;
                } else {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    if (bs.isValidUtf8()) {
                        admHashId_ = s;
                    }
                    return s;
                }
            }

            /**
             * <code>optional string admHashId = 17;</code>
             * @return The bytes for admHashId.
             */
            @java.lang.Override
            public com.google.protobuf.ByteString
            getAdmHashIdBytes() {
                java.lang.Object ref = admHashId_;
                if (ref instanceof java.lang.String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    admHashId_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            public static final int ADTYPE_FIELD_NUMBER = 18;
            private int adType_;

            /**
             * <code>optional int32 adType = 18;</code>
             * @return Whether the adType field is set.
             */
            @java.lang.Override
            public boolean hasAdType() {
                return ((bitField0_ & 0x00008000) != 0);
            }

            /**
             * <code>optional int32 adType = 18;</code>
             * @return The adType.
             */
            @java.lang.Override
            public int getAdType() {
                return adType_;
            }

            private byte memoizedIsInitialized = -1;

            @java.lang.Override
            public final boolean isInitialized() {
                byte isInitialized = memoizedIsInitialized;
                if (isInitialized == 1) return true;
                if (isInitialized == 0) return false;

                if (!hasHashId()) {
                    memoizedIsInitialized = 0;
                    return false;
                }
                if (!hasCrid()) {
                    memoizedIsInitialized = 0;
                    return false;
                }
                memoizedIsInitialized = 1;
                return true;
            }

            @java.lang.Override
            public void writeTo(com.google.protobuf.CodedOutputStream output)
                    throws java.io.IOException {
                if (((bitField0_ & 0x00000001) != 0)) {
                    com.google.protobuf.GeneratedMessageV3.writeString(output, 1, hashId_);
                }
                if (((bitField0_ & 0x00000002) != 0)) {
                    com.google.protobuf.GeneratedMessageV3.writeString(output, 2, adm_);
                }
                if (((bitField0_ & 0x00000004) != 0)) {
                    com.google.protobuf.GeneratedMessageV3.writeString(output, 3, bundle_);
                }
                if (((bitField0_ & 0x00000008) != 0)) {
                    com.google.protobuf.GeneratedMessageV3.writeString(output, 4, iurl_);
                }
                if (((bitField0_ & 0x00000010) != 0)) {
                    com.google.protobuf.GeneratedMessageV3.writeString(output, 5, cid_);
                }
                for (int i = 0; i < cat_.size(); i++) {
                    com.google.protobuf.GeneratedMessageV3.writeString(output, 6, cat_.getRaw(i));
                }
                if (((bitField0_ & 0x00000020) != 0)) {
                    com.google.protobuf.GeneratedMessageV3.writeString(output, 7, crid_);
                }
                for (int i = 0; i < attr_.size(); i++) {
                    output.writeInt32(8, attr_.getInt(i));
                }
                if (((bitField0_ & 0x00000040) != 0)) {
                    output.writeInt32(9, api_);
                }
                if (((bitField0_ & 0x00000080) != 0)) {
                    output.writeInt32(10, protocol_);
                }
                if (((bitField0_ & 0x00000100) != 0)) {
                    com.google.protobuf.GeneratedMessageV3.writeString(output, 11, dealId_);
                }
                if (((bitField0_ & 0x00000200) != 0)) {
                    output.writeInt32(12, w_);
                }
                if (((bitField0_ & 0x00000400) != 0)) {
                    output.writeInt32(13, h_);
                }
                if (((bitField0_ & 0x00000800) != 0)) {
                    output.writeInt32(14, wratio_);
                }
                if (((bitField0_ & 0x00001000) != 0)) {
                    output.writeInt32(15, hratio_);
                }
                if (((bitField0_ & 0x00002000) != 0)) {
                    com.google.protobuf.GeneratedMessageV3.writeString(output, 16, ext_);
                }
                if (((bitField0_ & 0x00004000) != 0)) {
                    com.google.protobuf.GeneratedMessageV3.writeString(output, 17, admHashId_);
                }
                if (((bitField0_ & 0x00008000) != 0)) {
                    output.writeInt32(18, adType_);
                }
                getUnknownFields().writeTo(output);
            }

            @java.lang.Override
            public int getSerializedSize() {
                int size = memoizedSize;
                if (size != -1) return size;

                size = 0;
                if (((bitField0_ & 0x00000001) != 0)) {
                    size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, hashId_);
                }
                if (((bitField0_ & 0x00000002) != 0)) {
                    size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, adm_);
                }
                if (((bitField0_ & 0x00000004) != 0)) {
                    size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, bundle_);
                }
                if (((bitField0_ & 0x00000008) != 0)) {
                    size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, iurl_);
                }
                if (((bitField0_ & 0x00000010) != 0)) {
                    size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, cid_);
                }
                {
                    int dataSize = 0;
                    for (int i = 0; i < cat_.size(); i++) {
                        dataSize += computeStringSizeNoTag(cat_.getRaw(i));
                    }
                    size += dataSize;
                    size += 1 * getCatList().size();
                }
                if (((bitField0_ & 0x00000020) != 0)) {
                    size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, crid_);
                }
                {
                    int dataSize = 0;
                    for (int i = 0; i < attr_.size(); i++) {
                        dataSize += com.google.protobuf.CodedOutputStream
                                .computeInt32SizeNoTag(attr_.getInt(i));
                    }
                    size += dataSize;
                    size += 1 * getAttrList().size();
                }
                if (((bitField0_ & 0x00000040) != 0)) {
                    size += com.google.protobuf.CodedOutputStream
                            .computeInt32Size(9, api_);
                }
                if (((bitField0_ & 0x00000080) != 0)) {
                    size += com.google.protobuf.CodedOutputStream
                            .computeInt32Size(10, protocol_);
                }
                if (((bitField0_ & 0x00000100) != 0)) {
                    size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, dealId_);
                }
                if (((bitField0_ & 0x00000200) != 0)) {
                    size += com.google.protobuf.CodedOutputStream
                            .computeInt32Size(12, w_);
                }
                if (((bitField0_ & 0x00000400) != 0)) {
                    size += com.google.protobuf.CodedOutputStream
                            .computeInt32Size(13, h_);
                }
                if (((bitField0_ & 0x00000800) != 0)) {
                    size += com.google.protobuf.CodedOutputStream
                            .computeInt32Size(14, wratio_);
                }
                if (((bitField0_ & 0x00001000) != 0)) {
                    size += com.google.protobuf.CodedOutputStream
                            .computeInt32Size(15, hratio_);
                }
                if (((bitField0_ & 0x00002000) != 0)) {
                    size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, ext_);
                }
                if (((bitField0_ & 0x00004000) != 0)) {
                    size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, admHashId_);
                }
                if (((bitField0_ & 0x00008000) != 0)) {
                    size += com.google.protobuf.CodedOutputStream
                            .computeInt32Size(18, adType_);
                }
                size += getUnknownFields().getSerializedSize();
                memoizedSize = size;
                return size;
            }

            @java.lang.Override
            public boolean equals(final java.lang.Object obj) {
                if (obj == this) {
                    return true;
                }
                if (!(obj instanceof com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material)) {
                    return super.equals(obj);
                }
                com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material other = (com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material) obj;

                if (hasHashId() != other.hasHashId()) return false;
                if (hasHashId()) {
                    if (!getHashId()
                            .equals(other.getHashId())) return false;
                }
                if (hasAdm() != other.hasAdm()) return false;
                if (hasAdm()) {
                    if (!getAdm()
                            .equals(other.getAdm())) return false;
                }
                if (hasBundle() != other.hasBundle()) return false;
                if (hasBundle()) {
                    if (!getBundle()
                            .equals(other.getBundle())) return false;
                }
                if (hasIurl() != other.hasIurl()) return false;
                if (hasIurl()) {
                    if (!getIurl()
                            .equals(other.getIurl())) return false;
                }
                if (hasCid() != other.hasCid()) return false;
                if (hasCid()) {
                    if (!getCid()
                            .equals(other.getCid())) return false;
                }
                if (!getCatList()
                        .equals(other.getCatList())) return false;
                if (hasCrid() != other.hasCrid()) return false;
                if (hasCrid()) {
                    if (!getCrid()
                            .equals(other.getCrid())) return false;
                }
                if (!getAttrList()
                        .equals(other.getAttrList())) return false;
                if (hasApi() != other.hasApi()) return false;
                if (hasApi()) {
                    if (getApi()
                            != other.getApi()) return false;
                }
                if (hasProtocol() != other.hasProtocol()) return false;
                if (hasProtocol()) {
                    if (getProtocol()
                            != other.getProtocol()) return false;
                }
                if (hasDealId() != other.hasDealId()) return false;
                if (hasDealId()) {
                    if (!getDealId()
                            .equals(other.getDealId())) return false;
                }
                if (hasW() != other.hasW()) return false;
                if (hasW()) {
                    if (getW()
                            != other.getW()) return false;
                }
                if (hasH() != other.hasH()) return false;
                if (hasH()) {
                    if (getH()
                            != other.getH()) return false;
                }
                if (hasWratio() != other.hasWratio()) return false;
                if (hasWratio()) {
                    if (getWratio()
                            != other.getWratio()) return false;
                }
                if (hasHratio() != other.hasHratio()) return false;
                if (hasHratio()) {
                    if (getHratio()
                            != other.getHratio()) return false;
                }
                if (hasExt() != other.hasExt()) return false;
                if (hasExt()) {
                    if (!getExt()
                            .equals(other.getExt())) return false;
                }
                if (hasAdmHashId() != other.hasAdmHashId()) return false;
                if (hasAdmHashId()) {
                    if (!getAdmHashId()
                            .equals(other.getAdmHashId())) return false;
                }
                if (hasAdType() != other.hasAdType()) return false;
                if (hasAdType()) {
                    if (getAdType()
                            != other.getAdType()) return false;
                }
                if (!getUnknownFields().equals(other.getUnknownFields())) return false;
                return true;
            }

            @java.lang.Override
            public int hashCode() {
                if (memoizedHashCode != 0) {
                    return memoizedHashCode;
                }
                int hash = 41;
                hash = (19 * hash) + getDescriptor().hashCode();
                if (hasHashId()) {
                    hash = (37 * hash) + HASH_ID_FIELD_NUMBER;
                    hash = (53 * hash) + getHashId().hashCode();
                }
                if (hasAdm()) {
                    hash = (37 * hash) + ADM_FIELD_NUMBER;
                    hash = (53 * hash) + getAdm().hashCode();
                }
                if (hasBundle()) {
                    hash = (37 * hash) + BUNDLE_FIELD_NUMBER;
                    hash = (53 * hash) + getBundle().hashCode();
                }
                if (hasIurl()) {
                    hash = (37 * hash) + IURL_FIELD_NUMBER;
                    hash = (53 * hash) + getIurl().hashCode();
                }
                if (hasCid()) {
                    hash = (37 * hash) + CID_FIELD_NUMBER;
                    hash = (53 * hash) + getCid().hashCode();
                }
                if (getCatCount() > 0) {
                    hash = (37 * hash) + CAT_FIELD_NUMBER;
                    hash = (53 * hash) + getCatList().hashCode();
                }
                if (hasCrid()) {
                    hash = (37 * hash) + CRID_FIELD_NUMBER;
                    hash = (53 * hash) + getCrid().hashCode();
                }
                if (getAttrCount() > 0) {
                    hash = (37 * hash) + ATTR_FIELD_NUMBER;
                    hash = (53 * hash) + getAttrList().hashCode();
                }
                if (hasApi()) {
                    hash = (37 * hash) + API_FIELD_NUMBER;
                    hash = (53 * hash) + getApi();
                }
                if (hasProtocol()) {
                    hash = (37 * hash) + PROTOCOL_FIELD_NUMBER;
                    hash = (53 * hash) + getProtocol();
                }
                if (hasDealId()) {
                    hash = (37 * hash) + DEALID_FIELD_NUMBER;
                    hash = (53 * hash) + getDealId().hashCode();
                }
                if (hasW()) {
                    hash = (37 * hash) + W_FIELD_NUMBER;
                    hash = (53 * hash) + getW();
                }
                if (hasH()) {
                    hash = (37 * hash) + H_FIELD_NUMBER;
                    hash = (53 * hash) + getH();
                }
                if (hasWratio()) {
                    hash = (37 * hash) + WRATIO_FIELD_NUMBER;
                    hash = (53 * hash) + getWratio();
                }
                if (hasHratio()) {
                    hash = (37 * hash) + HRATIO_FIELD_NUMBER;
                    hash = (53 * hash) + getHratio();
                }
                if (hasExt()) {
                    hash = (37 * hash) + EXT_FIELD_NUMBER;
                    hash = (53 * hash) + getExt().hashCode();
                }
                if (hasAdmHashId()) {
                    hash = (37 * hash) + ADMHASHID_FIELD_NUMBER;
                    hash = (53 * hash) + getAdmHashId().hashCode();
                }
                if (hasAdType()) {
                    hash = (37 * hash) + ADTYPE_FIELD_NUMBER;
                    hash = (53 * hash) + getAdType();
                }
                hash = (29 * hash) + getUnknownFields().hashCode();
                memoizedHashCode = hash;
                return hash;
            }

            public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material parseFrom(
                    java.nio.ByteBuffer data)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return PARSER.parseFrom(data);
            }

            public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material parseFrom(
                    java.nio.ByteBuffer data,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return PARSER.parseFrom(data, extensionRegistry);
            }

            public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material parseFrom(
                    com.google.protobuf.ByteString data)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return PARSER.parseFrom(data);
            }

            public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material parseFrom(
                    com.google.protobuf.ByteString data,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return PARSER.parseFrom(data, extensionRegistry);
            }

            public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material parseFrom(byte[] data)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return PARSER.parseFrom(data);
            }

            public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material parseFrom(
                    byte[] data,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                return PARSER.parseFrom(data, extensionRegistry);
            }

            public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material parseFrom(java.io.InputStream input)
                    throws java.io.IOException {
                return com.google.protobuf.GeneratedMessageV3
                        .parseWithIOException(PARSER, input);
            }

            public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material parseFrom(
                    java.io.InputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                return com.google.protobuf.GeneratedMessageV3
                        .parseWithIOException(PARSER, input, extensionRegistry);
            }

            public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material parseDelimitedFrom(java.io.InputStream input)
                    throws java.io.IOException {
                return com.google.protobuf.GeneratedMessageV3
                        .parseDelimitedWithIOException(PARSER, input);
            }

            public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material parseDelimitedFrom(
                    java.io.InputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                return com.google.protobuf.GeneratedMessageV3
                        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
            }

            public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material parseFrom(
                    com.google.protobuf.CodedInputStream input)
                    throws java.io.IOException {
                return com.google.protobuf.GeneratedMessageV3
                        .parseWithIOException(PARSER, input);
            }

            public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material parseFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                return com.google.protobuf.GeneratedMessageV3
                        .parseWithIOException(PARSER, input, extensionRegistry);
            }

            @java.lang.Override
            public Builder newBuilderForType() {
                return newBuilder();
            }

            public static Builder newBuilder() {
                return DEFAULT_INSTANCE.toBuilder();
            }

            public static Builder newBuilder(com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material prototype) {
                return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
            }

            @java.lang.Override
            public Builder toBuilder() {
                return this == DEFAULT_INSTANCE
                        ? new Builder() : new Builder().mergeFrom(this);
            }

            @java.lang.Override
            protected Builder newBuilderForType(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                Builder builder = new Builder(parent);
                return builder;
            }

            /**
             * Protobuf type {@code com.iflytek.traffic.log.MaterialLog.Material}
             */
            public static final class Builder extends
                    com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                    // @@protoc_insertion_point(builder_implements:com.iflytek.traffic.log.MaterialLog.Material)
                    com.iflytek.traffic.log.MaterialLogProto.MaterialLog.MaterialOrBuilder {
                public static final com.google.protobuf.Descriptors.Descriptor
                getDescriptor() {
                    return com.iflytek.traffic.log.MaterialLogProto.internal_static_com_iflytek_traffic_log_MaterialLog_Material_descriptor;
                }

                @java.lang.Override
                protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
                internalGetFieldAccessorTable() {
                    return com.iflytek.traffic.log.MaterialLogProto.internal_static_com_iflytek_traffic_log_MaterialLog_Material_fieldAccessorTable
                            .ensureFieldAccessorsInitialized(
                                    com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.class, com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.Builder.class);
                }

                // Construct using com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.newBuilder()
                private Builder() {

                }

                private Builder(
                        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                    super(parent);

                }

                @java.lang.Override
                public Builder clear() {
                    super.clear();
                    hashId_ = "";
                    bitField0_ = (bitField0_ & ~0x00000001);
                    adm_ = "";
                    bitField0_ = (bitField0_ & ~0x00000002);
                    bundle_ = "";
                    bitField0_ = (bitField0_ & ~0x00000004);
                    iurl_ = "";
                    bitField0_ = (bitField0_ & ~0x00000008);
                    cid_ = "";
                    bitField0_ = (bitField0_ & ~0x00000010);
                    cat_ = com.google.protobuf.LazyStringArrayList.EMPTY;
                    bitField0_ = (bitField0_ & ~0x00000020);
                    crid_ = "";
                    bitField0_ = (bitField0_ & ~0x00000040);
                    attr_ = emptyIntList();
                    bitField0_ = (bitField0_ & ~0x00000080);
                    api_ = 0;
                    bitField0_ = (bitField0_ & ~0x00000100);
                    protocol_ = 0;
                    bitField0_ = (bitField0_ & ~0x00000200);
                    dealId_ = "";
                    bitField0_ = (bitField0_ & ~0x00000400);
                    w_ = 0;
                    bitField0_ = (bitField0_ & ~0x00000800);
                    h_ = 0;
                    bitField0_ = (bitField0_ & ~0x00001000);
                    wratio_ = 0;
                    bitField0_ = (bitField0_ & ~0x00002000);
                    hratio_ = 0;
                    bitField0_ = (bitField0_ & ~0x00004000);
                    ext_ = "";
                    bitField0_ = (bitField0_ & ~0x00008000);
                    admHashId_ = "";
                    bitField0_ = (bitField0_ & ~0x00010000);
                    adType_ = 0;
                    bitField0_ = (bitField0_ & ~0x00020000);
                    return this;
                }

                @java.lang.Override
                public com.google.protobuf.Descriptors.Descriptor
                getDescriptorForType() {
                    return com.iflytek.traffic.log.MaterialLogProto.internal_static_com_iflytek_traffic_log_MaterialLog_Material_descriptor;
                }

                @java.lang.Override
                public com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material getDefaultInstanceForType() {
                    return com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.getDefaultInstance();
                }

                @java.lang.Override
                public com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material build() {
                    com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material result = buildPartial();
                    if (!result.isInitialized()) {
                        throw newUninitializedMessageException(result);
                    }
                    return result;
                }

                @java.lang.Override
                public com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material buildPartial() {
                    com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material result = new com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material(this);
                    int from_bitField0_ = bitField0_;
                    int to_bitField0_ = 0;
                    if (((from_bitField0_ & 0x00000001) != 0)) {
                        to_bitField0_ |= 0x00000001;
                    }
                    result.hashId_ = hashId_;
                    if (((from_bitField0_ & 0x00000002) != 0)) {
                        to_bitField0_ |= 0x00000002;
                    }
                    result.adm_ = adm_;
                    if (((from_bitField0_ & 0x00000004) != 0)) {
                        to_bitField0_ |= 0x00000004;
                    }
                    result.bundle_ = bundle_;
                    if (((from_bitField0_ & 0x00000008) != 0)) {
                        to_bitField0_ |= 0x00000008;
                    }
                    result.iurl_ = iurl_;
                    if (((from_bitField0_ & 0x00000010) != 0)) {
                        to_bitField0_ |= 0x00000010;
                    }
                    result.cid_ = cid_;
                    if (((bitField0_ & 0x00000020) != 0)) {
                        cat_ = cat_.getUnmodifiableView();
                        bitField0_ = (bitField0_ & ~0x00000020);
                    }
                    result.cat_ = cat_;
                    if (((from_bitField0_ & 0x00000040) != 0)) {
                        to_bitField0_ |= 0x00000020;
                    }
                    result.crid_ = crid_;
                    if (((bitField0_ & 0x00000080) != 0)) {
                        attr_.makeImmutable();
                        bitField0_ = (bitField0_ & ~0x00000080);
                    }
                    result.attr_ = attr_;
                    if (((from_bitField0_ & 0x00000100) != 0)) {
                        result.api_ = api_;
                        to_bitField0_ |= 0x00000040;
                    }
                    if (((from_bitField0_ & 0x00000200) != 0)) {
                        result.protocol_ = protocol_;
                        to_bitField0_ |= 0x00000080;
                    }
                    if (((from_bitField0_ & 0x00000400) != 0)) {
                        to_bitField0_ |= 0x00000100;
                    }
                    result.dealId_ = dealId_;
                    if (((from_bitField0_ & 0x00000800) != 0)) {
                        result.w_ = w_;
                        to_bitField0_ |= 0x00000200;
                    }
                    if (((from_bitField0_ & 0x00001000) != 0)) {
                        result.h_ = h_;
                        to_bitField0_ |= 0x00000400;
                    }
                    if (((from_bitField0_ & 0x00002000) != 0)) {
                        result.wratio_ = wratio_;
                        to_bitField0_ |= 0x00000800;
                    }
                    if (((from_bitField0_ & 0x00004000) != 0)) {
                        result.hratio_ = hratio_;
                        to_bitField0_ |= 0x00001000;
                    }
                    if (((from_bitField0_ & 0x00008000) != 0)) {
                        to_bitField0_ |= 0x00002000;
                    }
                    result.ext_ = ext_;
                    if (((from_bitField0_ & 0x00010000) != 0)) {
                        to_bitField0_ |= 0x00004000;
                    }
                    result.admHashId_ = admHashId_;
                    if (((from_bitField0_ & 0x00020000) != 0)) {
                        result.adType_ = adType_;
                        to_bitField0_ |= 0x00008000;
                    }
                    result.bitField0_ = to_bitField0_;
                    onBuilt();
                    return result;
                }

                @java.lang.Override
                public Builder clone() {
                    return super.clone();
                }

                @java.lang.Override
                public Builder setField(
                        com.google.protobuf.Descriptors.FieldDescriptor field,
                        java.lang.Object value) {
                    return super.setField(field, value);
                }

                @java.lang.Override
                public Builder clearField(
                        com.google.protobuf.Descriptors.FieldDescriptor field) {
                    return super.clearField(field);
                }

                @java.lang.Override
                public Builder clearOneof(
                        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                    return super.clearOneof(oneof);
                }

                @java.lang.Override
                public Builder setRepeatedField(
                        com.google.protobuf.Descriptors.FieldDescriptor field,
                        int index, java.lang.Object value) {
                    return super.setRepeatedField(field, index, value);
                }

                @java.lang.Override
                public Builder addRepeatedField(
                        com.google.protobuf.Descriptors.FieldDescriptor field,
                        java.lang.Object value) {
                    return super.addRepeatedField(field, value);
                }

                @java.lang.Override
                public Builder mergeFrom(com.google.protobuf.Message other) {
                    if (other instanceof com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material) {
                        return mergeFrom((com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material) other);
                    } else {
                        super.mergeFrom(other);
                        return this;
                    }
                }

                public Builder mergeFrom(com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material other) {
                    if (other == com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.getDefaultInstance())
                        return this;
                    if (other.hasHashId()) {
                        bitField0_ |= 0x00000001;
                        hashId_ = other.hashId_;
                        onChanged();
                    }
                    if (other.hasAdm()) {
                        bitField0_ |= 0x00000002;
                        adm_ = other.adm_;
                        onChanged();
                    }
                    if (other.hasBundle()) {
                        bitField0_ |= 0x00000004;
                        bundle_ = other.bundle_;
                        onChanged();
                    }
                    if (other.hasIurl()) {
                        bitField0_ |= 0x00000008;
                        iurl_ = other.iurl_;
                        onChanged();
                    }
                    if (other.hasCid()) {
                        bitField0_ |= 0x00000010;
                        cid_ = other.cid_;
                        onChanged();
                    }
                    if (!other.cat_.isEmpty()) {
                        if (cat_.isEmpty()) {
                            cat_ = other.cat_;
                            bitField0_ = (bitField0_ & ~0x00000020);
                        } else {
                            ensureCatIsMutable();
                            cat_.addAll(other.cat_);
                        }
                        onChanged();
                    }
                    if (other.hasCrid()) {
                        bitField0_ |= 0x00000040;
                        crid_ = other.crid_;
                        onChanged();
                    }
                    if (!other.attr_.isEmpty()) {
                        if (attr_.isEmpty()) {
                            attr_ = other.attr_;
                            bitField0_ = (bitField0_ & ~0x00000080);
                        } else {
                            ensureAttrIsMutable();
                            attr_.addAll(other.attr_);
                        }
                        onChanged();
                    }
                    if (other.hasApi()) {
                        setApi(other.getApi());
                    }
                    if (other.hasProtocol()) {
                        setProtocol(other.getProtocol());
                    }
                    if (other.hasDealId()) {
                        bitField0_ |= 0x00000400;
                        dealId_ = other.dealId_;
                        onChanged();
                    }
                    if (other.hasW()) {
                        setW(other.getW());
                    }
                    if (other.hasH()) {
                        setH(other.getH());
                    }
                    if (other.hasWratio()) {
                        setWratio(other.getWratio());
                    }
                    if (other.hasHratio()) {
                        setHratio(other.getHratio());
                    }
                    if (other.hasExt()) {
                        bitField0_ |= 0x00008000;
                        ext_ = other.ext_;
                        onChanged();
                    }
                    if (other.hasAdmHashId()) {
                        bitField0_ |= 0x00010000;
                        admHashId_ = other.admHashId_;
                        onChanged();
                    }
                    if (other.hasAdType()) {
                        setAdType(other.getAdType());
                    }
                    this.mergeUnknownFields(other.getUnknownFields());
                    onChanged();
                    return this;
                }

                @java.lang.Override
                public final boolean isInitialized() {
                    if (!hasHashId()) {
                        return false;
                    }
                    if (!hasCrid()) {
                        return false;
                    }
                    return true;
                }

                @java.lang.Override
                public Builder mergeFrom(
                        com.google.protobuf.CodedInputStream input,
                        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                        throws java.io.IOException {
                    if (extensionRegistry == null) {
                        throw new java.lang.NullPointerException();
                    }
                    try {
                        boolean done = false;
                        while (!done) {
                            int tag = input.readTag();
                            switch (tag) {
                                case 0:
                                    done = true;
                                    break;
                                case 10: {
                                    hashId_ = input.readBytes();
                                    bitField0_ |= 0x00000001;
                                    break;
                                } // case 10
                                case 18: {
                                    adm_ = input.readBytes();
                                    bitField0_ |= 0x00000002;
                                    break;
                                } // case 18
                                case 26: {
                                    bundle_ = input.readBytes();
                                    bitField0_ |= 0x00000004;
                                    break;
                                } // case 26
                                case 34: {
                                    iurl_ = input.readBytes();
                                    bitField0_ |= 0x00000008;
                                    break;
                                } // case 34
                                case 42: {
                                    cid_ = input.readBytes();
                                    bitField0_ |= 0x00000010;
                                    break;
                                } // case 42
                                case 50: {
                                    com.google.protobuf.ByteString bs = input.readBytes();
                                    ensureCatIsMutable();
                                    cat_.add(bs);
                                    break;
                                } // case 50
                                case 58: {
                                    crid_ = input.readBytes();
                                    bitField0_ |= 0x00000040;
                                    break;
                                } // case 58
                                case 64: {
                                    int v = input.readInt32();
                                    ensureAttrIsMutable();
                                    attr_.addInt(v);
                                    break;
                                } // case 64
                                case 66: {
                                    int length = input.readRawVarint32();
                                    int limit = input.pushLimit(length);
                                    ensureAttrIsMutable();
                                    while (input.getBytesUntilLimit() > 0) {
                                        attr_.addInt(input.readInt32());
                                    }
                                    input.popLimit(limit);
                                    break;
                                } // case 66
                                case 72: {
                                    api_ = input.readInt32();
                                    bitField0_ |= 0x00000100;
                                    break;
                                } // case 72
                                case 80: {
                                    protocol_ = input.readInt32();
                                    bitField0_ |= 0x00000200;
                                    break;
                                } // case 80
                                case 90: {
                                    dealId_ = input.readBytes();
                                    bitField0_ |= 0x00000400;
                                    break;
                                } // case 90
                                case 96: {
                                    w_ = input.readInt32();
                                    bitField0_ |= 0x00000800;
                                    break;
                                } // case 96
                                case 104: {
                                    h_ = input.readInt32();
                                    bitField0_ |= 0x00001000;
                                    break;
                                } // case 104
                                case 112: {
                                    wratio_ = input.readInt32();
                                    bitField0_ |= 0x00002000;
                                    break;
                                } // case 112
                                case 120: {
                                    hratio_ = input.readInt32();
                                    bitField0_ |= 0x00004000;
                                    break;
                                } // case 120
                                case 130: {
                                    ext_ = input.readBytes();
                                    bitField0_ |= 0x00008000;
                                    break;
                                } // case 130
                                case 138: {
                                    admHashId_ = input.readBytes();
                                    bitField0_ |= 0x00010000;
                                    break;
                                } // case 138
                                case 144: {
                                    adType_ = input.readInt32();
                                    bitField0_ |= 0x00020000;
                                    break;
                                } // case 144
                                default: {
                                    if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                                        done = true; // was an endgroup tag
                                    }
                                    break;
                                } // default:
                            } // switch (tag)
                        } // while (!done)
                    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                        throw e.unwrapIOException();
                    } finally {
                        onChanged();
                    } // finally
                    return this;
                }

                private int bitField0_;

                private java.lang.Object hashId_ = "";

                /**
                 * <code>required string hash_id = 1;</code>
                 * @return Whether the hashId field is set.
                 */
                public boolean hasHashId() {
                    return ((bitField0_ & 0x00000001) != 0);
                }

                /**
                 * <code>required string hash_id = 1;</code>
                 * @return The hashId.
                 */
                public java.lang.String getHashId() {
                    java.lang.Object ref = hashId_;
                    if (!(ref instanceof java.lang.String)) {
                        com.google.protobuf.ByteString bs =
                                (com.google.protobuf.ByteString) ref;
                        java.lang.String s = bs.toStringUtf8();
                        if (bs.isValidUtf8()) {
                            hashId_ = s;
                        }
                        return s;
                    } else {
                        return (java.lang.String) ref;
                    }
                }

                /**
                 * <code>required string hash_id = 1;</code>
                 * @return The bytes for hashId.
                 */
                public com.google.protobuf.ByteString
                getHashIdBytes() {
                    java.lang.Object ref = hashId_;
                    if (ref instanceof String) {
                        com.google.protobuf.ByteString b =
                                com.google.protobuf.ByteString.copyFromUtf8(
                                        (java.lang.String) ref);
                        hashId_ = b;
                        return b;
                    } else {
                        return (com.google.protobuf.ByteString) ref;
                    }
                }

                /**
                 * <code>required string hash_id = 1;</code>
                 * @param value The hashId to set.
                 * @return This builder for chaining.
                 */
                public Builder setHashId(
                        java.lang.String value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00000001;
                    hashId_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>required string hash_id = 1;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearHashId() {
                    bitField0_ = (bitField0_ & ~0x00000001);
                    hashId_ = getDefaultInstance().getHashId();
                    onChanged();
                    return this;
                }

                /**
                 * <code>required string hash_id = 1;</code>
                 * @param value The bytes for hashId to set.
                 * @return This builder for chaining.
                 */
                public Builder setHashIdBytes(
                        com.google.protobuf.ByteString value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00000001;
                    hashId_ = value;
                    onChanged();
                    return this;
                }

                private java.lang.Object adm_ = "";

                /**
                 * <pre>
                 * t_dsp_material
                 * </pre>
                 *
                 * <code>optional string adm = 2;</code>
                 * @return Whether the adm field is set.
                 */
                public boolean hasAdm() {
                    return ((bitField0_ & 0x00000002) != 0);
                }

                /**
                 * <pre>
                 * t_dsp_material
                 * </pre>
                 *
                 * <code>optional string adm = 2;</code>
                 * @return The adm.
                 */
                public java.lang.String getAdm() {
                    java.lang.Object ref = adm_;
                    if (!(ref instanceof java.lang.String)) {
                        com.google.protobuf.ByteString bs =
                                (com.google.protobuf.ByteString) ref;
                        java.lang.String s = bs.toStringUtf8();
                        if (bs.isValidUtf8()) {
                            adm_ = s;
                        }
                        return s;
                    } else {
                        return (java.lang.String) ref;
                    }
                }

                /**
                 * <pre>
                 * t_dsp_material
                 * </pre>
                 *
                 * <code>optional string adm = 2;</code>
                 * @return The bytes for adm.
                 */
                public com.google.protobuf.ByteString
                getAdmBytes() {
                    java.lang.Object ref = adm_;
                    if (ref instanceof String) {
                        com.google.protobuf.ByteString b =
                                com.google.protobuf.ByteString.copyFromUtf8(
                                        (java.lang.String) ref);
                        adm_ = b;
                        return b;
                    } else {
                        return (com.google.protobuf.ByteString) ref;
                    }
                }

                /**
                 * <pre>
                 * t_dsp_material
                 * </pre>
                 *
                 * <code>optional string adm = 2;</code>
                 * @param value The adm to set.
                 * @return This builder for chaining.
                 */
                public Builder setAdm(
                        java.lang.String value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00000002;
                    adm_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <pre>
                 * t_dsp_material
                 * </pre>
                 *
                 * <code>optional string adm = 2;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearAdm() {
                    bitField0_ = (bitField0_ & ~0x00000002);
                    adm_ = getDefaultInstance().getAdm();
                    onChanged();
                    return this;
                }

                /**
                 * <pre>
                 * t_dsp_material
                 * </pre>
                 *
                 * <code>optional string adm = 2;</code>
                 * @param value The bytes for adm to set.
                 * @return This builder for chaining.
                 */
                public Builder setAdmBytes(
                        com.google.protobuf.ByteString value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00000002;
                    adm_ = value;
                    onChanged();
                    return this;
                }

                private java.lang.Object bundle_ = "";

                /**
                 * <code>optional string bundle = 3;</code>
                 * @return Whether the bundle field is set.
                 */
                public boolean hasBundle() {
                    return ((bitField0_ & 0x00000004) != 0);
                }

                /**
                 * <code>optional string bundle = 3;</code>
                 * @return The bundle.
                 */
                public java.lang.String getBundle() {
                    java.lang.Object ref = bundle_;
                    if (!(ref instanceof java.lang.String)) {
                        com.google.protobuf.ByteString bs =
                                (com.google.protobuf.ByteString) ref;
                        java.lang.String s = bs.toStringUtf8();
                        if (bs.isValidUtf8()) {
                            bundle_ = s;
                        }
                        return s;
                    } else {
                        return (java.lang.String) ref;
                    }
                }

                /**
                 * <code>optional string bundle = 3;</code>
                 * @return The bytes for bundle.
                 */
                public com.google.protobuf.ByteString
                getBundleBytes() {
                    java.lang.Object ref = bundle_;
                    if (ref instanceof String) {
                        com.google.protobuf.ByteString b =
                                com.google.protobuf.ByteString.copyFromUtf8(
                                        (java.lang.String) ref);
                        bundle_ = b;
                        return b;
                    } else {
                        return (com.google.protobuf.ByteString) ref;
                    }
                }

                /**
                 * <code>optional string bundle = 3;</code>
                 * @param value The bundle to set.
                 * @return This builder for chaining.
                 */
                public Builder setBundle(
                        java.lang.String value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00000004;
                    bundle_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional string bundle = 3;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearBundle() {
                    bitField0_ = (bitField0_ & ~0x00000004);
                    bundle_ = getDefaultInstance().getBundle();
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional string bundle = 3;</code>
                 * @param value The bytes for bundle to set.
                 * @return This builder for chaining.
                 */
                public Builder setBundleBytes(
                        com.google.protobuf.ByteString value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00000004;
                    bundle_ = value;
                    onChanged();
                    return this;
                }

                private java.lang.Object iurl_ = "";

                /**
                 * <code>optional string iurl = 4;</code>
                 * @return Whether the iurl field is set.
                 */
                public boolean hasIurl() {
                    return ((bitField0_ & 0x00000008) != 0);
                }

                /**
                 * <code>optional string iurl = 4;</code>
                 * @return The iurl.
                 */
                public java.lang.String getIurl() {
                    java.lang.Object ref = iurl_;
                    if (!(ref instanceof java.lang.String)) {
                        com.google.protobuf.ByteString bs =
                                (com.google.protobuf.ByteString) ref;
                        java.lang.String s = bs.toStringUtf8();
                        if (bs.isValidUtf8()) {
                            iurl_ = s;
                        }
                        return s;
                    } else {
                        return (java.lang.String) ref;
                    }
                }

                /**
                 * <code>optional string iurl = 4;</code>
                 * @return The bytes for iurl.
                 */
                public com.google.protobuf.ByteString
                getIurlBytes() {
                    java.lang.Object ref = iurl_;
                    if (ref instanceof String) {
                        com.google.protobuf.ByteString b =
                                com.google.protobuf.ByteString.copyFromUtf8(
                                        (java.lang.String) ref);
                        iurl_ = b;
                        return b;
                    } else {
                        return (com.google.protobuf.ByteString) ref;
                    }
                }

                /**
                 * <code>optional string iurl = 4;</code>
                 * @param value The iurl to set.
                 * @return This builder for chaining.
                 */
                public Builder setIurl(
                        java.lang.String value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00000008;
                    iurl_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional string iurl = 4;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearIurl() {
                    bitField0_ = (bitField0_ & ~0x00000008);
                    iurl_ = getDefaultInstance().getIurl();
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional string iurl = 4;</code>
                 * @param value The bytes for iurl to set.
                 * @return This builder for chaining.
                 */
                public Builder setIurlBytes(
                        com.google.protobuf.ByteString value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00000008;
                    iurl_ = value;
                    onChanged();
                    return this;
                }

                private java.lang.Object cid_ = "";

                /**
                 * <code>optional string cid = 5;</code>
                 * @return Whether the cid field is set.
                 */
                public boolean hasCid() {
                    return ((bitField0_ & 0x00000010) != 0);
                }

                /**
                 * <code>optional string cid = 5;</code>
                 * @return The cid.
                 */
                public java.lang.String getCid() {
                    java.lang.Object ref = cid_;
                    if (!(ref instanceof java.lang.String)) {
                        com.google.protobuf.ByteString bs =
                                (com.google.protobuf.ByteString) ref;
                        java.lang.String s = bs.toStringUtf8();
                        if (bs.isValidUtf8()) {
                            cid_ = s;
                        }
                        return s;
                    } else {
                        return (java.lang.String) ref;
                    }
                }

                /**
                 * <code>optional string cid = 5;</code>
                 * @return The bytes for cid.
                 */
                public com.google.protobuf.ByteString
                getCidBytes() {
                    java.lang.Object ref = cid_;
                    if (ref instanceof String) {
                        com.google.protobuf.ByteString b =
                                com.google.protobuf.ByteString.copyFromUtf8(
                                        (java.lang.String) ref);
                        cid_ = b;
                        return b;
                    } else {
                        return (com.google.protobuf.ByteString) ref;
                    }
                }

                /**
                 * <code>optional string cid = 5;</code>
                 * @param value The cid to set.
                 * @return This builder for chaining.
                 */
                public Builder setCid(
                        java.lang.String value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00000010;
                    cid_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional string cid = 5;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearCid() {
                    bitField0_ = (bitField0_ & ~0x00000010);
                    cid_ = getDefaultInstance().getCid();
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional string cid = 5;</code>
                 * @param value The bytes for cid to set.
                 * @return This builder for chaining.
                 */
                public Builder setCidBytes(
                        com.google.protobuf.ByteString value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00000010;
                    cid_ = value;
                    onChanged();
                    return this;
                }

                private com.google.protobuf.LazyStringList cat_ = com.google.protobuf.LazyStringArrayList.EMPTY;

                private void ensureCatIsMutable() {
                    if (!((bitField0_ & 0x00000020) != 0)) {
                        cat_ = new com.google.protobuf.LazyStringArrayList(cat_);
                        bitField0_ |= 0x00000020;
                    }
                }

                /**
                 * <code>repeated string cat = 6;</code>
                 * @return A list containing the cat.
                 */
                public com.google.protobuf.ProtocolStringList
                getCatList() {
                    return cat_.getUnmodifiableView();
                }

                /**
                 * <code>repeated string cat = 6;</code>
                 * @return The count of cat.
                 */
                public int getCatCount() {
                    return cat_.size();
                }

                /**
                 * <code>repeated string cat = 6;</code>
                 * @param index The index of the element to return.
                 * @return The cat at the given index.
                 */
                public java.lang.String getCat(int index) {
                    return cat_.get(index);
                }

                /**
                 * <code>repeated string cat = 6;</code>
                 * @param index The index of the value to return.
                 * @return The bytes of the cat at the given index.
                 */
                public com.google.protobuf.ByteString
                getCatBytes(int index) {
                    return cat_.getByteString(index);
                }

                /**
                 * <code>repeated string cat = 6;</code>
                 * @param index The index to set the value at.
                 * @param value The cat to set.
                 * @return This builder for chaining.
                 */
                public Builder setCat(
                        int index, java.lang.String value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureCatIsMutable();
                    cat_.set(index, value);
                    onChanged();
                    return this;
                }

                /**
                 * <code>repeated string cat = 6;</code>
                 * @param value The cat to add.
                 * @return This builder for chaining.
                 */
                public Builder addCat(
                        java.lang.String value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureCatIsMutable();
                    cat_.add(value);
                    onChanged();
                    return this;
                }

                /**
                 * <code>repeated string cat = 6;</code>
                 * @param values The cat to add.
                 * @return This builder for chaining.
                 */
                public Builder addAllCat(
                        java.lang.Iterable<java.lang.String> values) {
                    ensureCatIsMutable();
                    com.google.protobuf.AbstractMessageLite.Builder.addAll(
                            values, cat_);
                    onChanged();
                    return this;
                }

                /**
                 * <code>repeated string cat = 6;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearCat() {
                    cat_ = com.google.protobuf.LazyStringArrayList.EMPTY;
                    bitField0_ = (bitField0_ & ~0x00000020);
                    onChanged();
                    return this;
                }

                /**
                 * <code>repeated string cat = 6;</code>
                 * @param value The bytes of the cat to add.
                 * @return This builder for chaining.
                 */
                public Builder addCatBytes(
                        com.google.protobuf.ByteString value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    ensureCatIsMutable();
                    cat_.add(value);
                    onChanged();
                    return this;
                }

                private java.lang.Object crid_ = "";

                /**
                 * <code>required string crid = 7;</code>
                 * @return Whether the crid field is set.
                 */
                public boolean hasCrid() {
                    return ((bitField0_ & 0x00000040) != 0);
                }

                /**
                 * <code>required string crid = 7;</code>
                 * @return The crid.
                 */
                public java.lang.String getCrid() {
                    java.lang.Object ref = crid_;
                    if (!(ref instanceof java.lang.String)) {
                        com.google.protobuf.ByteString bs =
                                (com.google.protobuf.ByteString) ref;
                        java.lang.String s = bs.toStringUtf8();
                        if (bs.isValidUtf8()) {
                            crid_ = s;
                        }
                        return s;
                    } else {
                        return (java.lang.String) ref;
                    }
                }

                /**
                 * <code>required string crid = 7;</code>
                 * @return The bytes for crid.
                 */
                public com.google.protobuf.ByteString
                getCridBytes() {
                    java.lang.Object ref = crid_;
                    if (ref instanceof String) {
                        com.google.protobuf.ByteString b =
                                com.google.protobuf.ByteString.copyFromUtf8(
                                        (java.lang.String) ref);
                        crid_ = b;
                        return b;
                    } else {
                        return (com.google.protobuf.ByteString) ref;
                    }
                }

                /**
                 * <code>required string crid = 7;</code>
                 * @param value The crid to set.
                 * @return This builder for chaining.
                 */
                public Builder setCrid(
                        java.lang.String value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00000040;
                    crid_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>required string crid = 7;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearCrid() {
                    bitField0_ = (bitField0_ & ~0x00000040);
                    crid_ = getDefaultInstance().getCrid();
                    onChanged();
                    return this;
                }

                /**
                 * <code>required string crid = 7;</code>
                 * @param value The bytes for crid to set.
                 * @return This builder for chaining.
                 */
                public Builder setCridBytes(
                        com.google.protobuf.ByteString value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00000040;
                    crid_ = value;
                    onChanged();
                    return this;
                }

                private com.google.protobuf.Internal.IntList attr_ = emptyIntList();

                private void ensureAttrIsMutable() {
                    if (!((bitField0_ & 0x00000080) != 0)) {
                        attr_ = mutableCopy(attr_);
                        bitField0_ |= 0x00000080;
                    }
                }

                /**
                 * <code>repeated int32 attr = 8;</code>
                 * @return A list containing the attr.
                 */
                public java.util.List<java.lang.Integer>
                getAttrList() {
                    return ((bitField0_ & 0x00000080) != 0) ?
                            java.util.Collections.unmodifiableList(attr_) : attr_;
                }

                /**
                 * <code>repeated int32 attr = 8;</code>
                 * @return The count of attr.
                 */
                public int getAttrCount() {
                    return attr_.size();
                }

                /**
                 * <code>repeated int32 attr = 8;</code>
                 * @param index The index of the element to return.
                 * @return The attr at the given index.
                 */
                public int getAttr(int index) {
                    return attr_.getInt(index);
                }

                /**
                 * <code>repeated int32 attr = 8;</code>
                 * @param index The index to set the value at.
                 * @param value The attr to set.
                 * @return This builder for chaining.
                 */
                public Builder setAttr(
                        int index, int value) {
                    ensureAttrIsMutable();
                    attr_.setInt(index, value);
                    onChanged();
                    return this;
                }

                /**
                 * <code>repeated int32 attr = 8;</code>
                 * @param value The attr to add.
                 * @return This builder for chaining.
                 */
                public Builder addAttr(int value) {
                    ensureAttrIsMutable();
                    attr_.addInt(value);
                    onChanged();
                    return this;
                }

                /**
                 * <code>repeated int32 attr = 8;</code>
                 * @param values The attr to add.
                 * @return This builder for chaining.
                 */
                public Builder addAllAttr(
                        java.lang.Iterable<? extends java.lang.Integer> values) {
                    ensureAttrIsMutable();
                    com.google.protobuf.AbstractMessageLite.Builder.addAll(
                            values, attr_);
                    onChanged();
                    return this;
                }

                /**
                 * <code>repeated int32 attr = 8;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearAttr() {
                    attr_ = emptyIntList();
                    bitField0_ = (bitField0_ & ~0x00000080);
                    onChanged();
                    return this;
                }

                private int api_;

                /**
                 * <code>optional int32 api = 9;</code>
                 * @return Whether the api field is set.
                 */
                @java.lang.Override
                public boolean hasApi() {
                    return ((bitField0_ & 0x00000100) != 0);
                }

                /**
                 * <code>optional int32 api = 9;</code>
                 * @return The api.
                 */
                @java.lang.Override
                public int getApi() {
                    return api_;
                }

                /**
                 * <code>optional int32 api = 9;</code>
                 * @param value The api to set.
                 * @return This builder for chaining.
                 */
                public Builder setApi(int value) {
                    bitField0_ |= 0x00000100;
                    api_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional int32 api = 9;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearApi() {
                    bitField0_ = (bitField0_ & ~0x00000100);
                    api_ = 0;
                    onChanged();
                    return this;
                }

                private int protocol_;

                /**
                 * <code>optional int32 protocol = 10;</code>
                 * @return Whether the protocol field is set.
                 */
                @java.lang.Override
                public boolean hasProtocol() {
                    return ((bitField0_ & 0x00000200) != 0);
                }

                /**
                 * <code>optional int32 protocol = 10;</code>
                 * @return The protocol.
                 */
                @java.lang.Override
                public int getProtocol() {
                    return protocol_;
                }

                /**
                 * <code>optional int32 protocol = 10;</code>
                 * @param value The protocol to set.
                 * @return This builder for chaining.
                 */
                public Builder setProtocol(int value) {
                    bitField0_ |= 0x00000200;
                    protocol_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional int32 protocol = 10;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearProtocol() {
                    bitField0_ = (bitField0_ & ~0x00000200);
                    protocol_ = 0;
                    onChanged();
                    return this;
                }

                private java.lang.Object dealId_ = "";

                /**
                 * <code>optional string dealId = 11;</code>
                 * @return Whether the dealId field is set.
                 */
                public boolean hasDealId() {
                    return ((bitField0_ & 0x00000400) != 0);
                }

                /**
                 * <code>optional string dealId = 11;</code>
                 * @return The dealId.
                 */
                public java.lang.String getDealId() {
                    java.lang.Object ref = dealId_;
                    if (!(ref instanceof java.lang.String)) {
                        com.google.protobuf.ByteString bs =
                                (com.google.protobuf.ByteString) ref;
                        java.lang.String s = bs.toStringUtf8();
                        if (bs.isValidUtf8()) {
                            dealId_ = s;
                        }
                        return s;
                    } else {
                        return (java.lang.String) ref;
                    }
                }

                /**
                 * <code>optional string dealId = 11;</code>
                 * @return The bytes for dealId.
                 */
                public com.google.protobuf.ByteString
                getDealIdBytes() {
                    java.lang.Object ref = dealId_;
                    if (ref instanceof String) {
                        com.google.protobuf.ByteString b =
                                com.google.protobuf.ByteString.copyFromUtf8(
                                        (java.lang.String) ref);
                        dealId_ = b;
                        return b;
                    } else {
                        return (com.google.protobuf.ByteString) ref;
                    }
                }

                /**
                 * <code>optional string dealId = 11;</code>
                 * @param value The dealId to set.
                 * @return This builder for chaining.
                 */
                public Builder setDealId(
                        java.lang.String value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00000400;
                    dealId_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional string dealId = 11;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearDealId() {
                    bitField0_ = (bitField0_ & ~0x00000400);
                    dealId_ = getDefaultInstance().getDealId();
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional string dealId = 11;</code>
                 * @param value The bytes for dealId to set.
                 * @return This builder for chaining.
                 */
                public Builder setDealIdBytes(
                        com.google.protobuf.ByteString value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00000400;
                    dealId_ = value;
                    onChanged();
                    return this;
                }

                private int w_;

                /**
                 * <code>optional int32 w = 12;</code>
                 * @return Whether the w field is set.
                 */
                @java.lang.Override
                public boolean hasW() {
                    return ((bitField0_ & 0x00000800) != 0);
                }

                /**
                 * <code>optional int32 w = 12;</code>
                 * @return The w.
                 */
                @java.lang.Override
                public int getW() {
                    return w_;
                }

                /**
                 * <code>optional int32 w = 12;</code>
                 * @param value The w to set.
                 * @return This builder for chaining.
                 */
                public Builder setW(int value) {
                    bitField0_ |= 0x00000800;
                    w_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional int32 w = 12;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearW() {
                    bitField0_ = (bitField0_ & ~0x00000800);
                    w_ = 0;
                    onChanged();
                    return this;
                }

                private int h_;

                /**
                 * <code>optional int32 h = 13;</code>
                 * @return Whether the h field is set.
                 */
                @java.lang.Override
                public boolean hasH() {
                    return ((bitField0_ & 0x00001000) != 0);
                }

                /**
                 * <code>optional int32 h = 13;</code>
                 * @return The h.
                 */
                @java.lang.Override
                public int getH() {
                    return h_;
                }

                /**
                 * <code>optional int32 h = 13;</code>
                 * @param value The h to set.
                 * @return This builder for chaining.
                 */
                public Builder setH(int value) {
                    bitField0_ |= 0x00001000;
                    h_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional int32 h = 13;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearH() {
                    bitField0_ = (bitField0_ & ~0x00001000);
                    h_ = 0;
                    onChanged();
                    return this;
                }

                private int wratio_;

                /**
                 * <code>optional int32 wratio = 14;</code>
                 * @return Whether the wratio field is set.
                 */
                @java.lang.Override
                public boolean hasWratio() {
                    return ((bitField0_ & 0x00002000) != 0);
                }

                /**
                 * <code>optional int32 wratio = 14;</code>
                 * @return The wratio.
                 */
                @java.lang.Override
                public int getWratio() {
                    return wratio_;
                }

                /**
                 * <code>optional int32 wratio = 14;</code>
                 * @param value The wratio to set.
                 * @return This builder for chaining.
                 */
                public Builder setWratio(int value) {
                    bitField0_ |= 0x00002000;
                    wratio_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional int32 wratio = 14;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearWratio() {
                    bitField0_ = (bitField0_ & ~0x00002000);
                    wratio_ = 0;
                    onChanged();
                    return this;
                }

                private int hratio_;

                /**
                 * <code>optional int32 hratio = 15;</code>
                 * @return Whether the hratio field is set.
                 */
                @java.lang.Override
                public boolean hasHratio() {
                    return ((bitField0_ & 0x00004000) != 0);
                }

                /**
                 * <code>optional int32 hratio = 15;</code>
                 * @return The hratio.
                 */
                @java.lang.Override
                public int getHratio() {
                    return hratio_;
                }

                /**
                 * <code>optional int32 hratio = 15;</code>
                 * @param value The hratio to set.
                 * @return This builder for chaining.
                 */
                public Builder setHratio(int value) {
                    bitField0_ |= 0x00004000;
                    hratio_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional int32 hratio = 15;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearHratio() {
                    bitField0_ = (bitField0_ & ~0x00004000);
                    hratio_ = 0;
                    onChanged();
                    return this;
                }

                private java.lang.Object ext_ = "";

                /**
                 * <code>optional string ext = 16;</code>
                 * @return Whether the ext field is set.
                 */
                public boolean hasExt() {
                    return ((bitField0_ & 0x00008000) != 0);
                }

                /**
                 * <code>optional string ext = 16;</code>
                 * @return The ext.
                 */
                public java.lang.String getExt() {
                    java.lang.Object ref = ext_;
                    if (!(ref instanceof java.lang.String)) {
                        com.google.protobuf.ByteString bs =
                                (com.google.protobuf.ByteString) ref;
                        java.lang.String s = bs.toStringUtf8();
                        if (bs.isValidUtf8()) {
                            ext_ = s;
                        }
                        return s;
                    } else {
                        return (java.lang.String) ref;
                    }
                }

                /**
                 * <code>optional string ext = 16;</code>
                 * @return The bytes for ext.
                 */
                public com.google.protobuf.ByteString
                getExtBytes() {
                    java.lang.Object ref = ext_;
                    if (ref instanceof String) {
                        com.google.protobuf.ByteString b =
                                com.google.protobuf.ByteString.copyFromUtf8(
                                        (java.lang.String) ref);
                        ext_ = b;
                        return b;
                    } else {
                        return (com.google.protobuf.ByteString) ref;
                    }
                }

                /**
                 * <code>optional string ext = 16;</code>
                 * @param value The ext to set.
                 * @return This builder for chaining.
                 */
                public Builder setExt(
                        java.lang.String value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00008000;
                    ext_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional string ext = 16;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearExt() {
                    bitField0_ = (bitField0_ & ~0x00008000);
                    ext_ = getDefaultInstance().getExt();
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional string ext = 16;</code>
                 * @param value The bytes for ext to set.
                 * @return This builder for chaining.
                 */
                public Builder setExtBytes(
                        com.google.protobuf.ByteString value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00008000;
                    ext_ = value;
                    onChanged();
                    return this;
                }

                private java.lang.Object admHashId_ = "";

                /**
                 * <code>optional string admHashId = 17;</code>
                 * @return Whether the admHashId field is set.
                 */
                public boolean hasAdmHashId() {
                    return ((bitField0_ & 0x00010000) != 0);
                }

                /**
                 * <code>optional string admHashId = 17;</code>
                 * @return The admHashId.
                 */
                public java.lang.String getAdmHashId() {
                    java.lang.Object ref = admHashId_;
                    if (!(ref instanceof java.lang.String)) {
                        com.google.protobuf.ByteString bs =
                                (com.google.protobuf.ByteString) ref;
                        java.lang.String s = bs.toStringUtf8();
                        if (bs.isValidUtf8()) {
                            admHashId_ = s;
                        }
                        return s;
                    } else {
                        return (java.lang.String) ref;
                    }
                }

                /**
                 * <code>optional string admHashId = 17;</code>
                 * @return The bytes for admHashId.
                 */
                public com.google.protobuf.ByteString
                getAdmHashIdBytes() {
                    java.lang.Object ref = admHashId_;
                    if (ref instanceof String) {
                        com.google.protobuf.ByteString b =
                                com.google.protobuf.ByteString.copyFromUtf8(
                                        (java.lang.String) ref);
                        admHashId_ = b;
                        return b;
                    } else {
                        return (com.google.protobuf.ByteString) ref;
                    }
                }

                /**
                 * <code>optional string admHashId = 17;</code>
                 * @param value The admHashId to set.
                 * @return This builder for chaining.
                 */
                public Builder setAdmHashId(
                        java.lang.String value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00010000;
                    admHashId_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional string admHashId = 17;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearAdmHashId() {
                    bitField0_ = (bitField0_ & ~0x00010000);
                    admHashId_ = getDefaultInstance().getAdmHashId();
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional string admHashId = 17;</code>
                 * @param value The bytes for admHashId to set.
                 * @return This builder for chaining.
                 */
                public Builder setAdmHashIdBytes(
                        com.google.protobuf.ByteString value) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    bitField0_ |= 0x00010000;
                    admHashId_ = value;
                    onChanged();
                    return this;
                }

                private int adType_;

                /**
                 * <code>optional int32 adType = 18;</code>
                 * @return Whether the adType field is set.
                 */
                @java.lang.Override
                public boolean hasAdType() {
                    return ((bitField0_ & 0x00020000) != 0);
                }

                /**
                 * <code>optional int32 adType = 18;</code>
                 * @return The adType.
                 */
                @java.lang.Override
                public int getAdType() {
                    return adType_;
                }

                /**
                 * <code>optional int32 adType = 18;</code>
                 * @param value The adType to set.
                 * @return This builder for chaining.
                 */
                public Builder setAdType(int value) {
                    bitField0_ |= 0x00020000;
                    adType_ = value;
                    onChanged();
                    return this;
                }

                /**
                 * <code>optional int32 adType = 18;</code>
                 * @return This builder for chaining.
                 */
                public Builder clearAdType() {
                    bitField0_ = (bitField0_ & ~0x00020000);
                    adType_ = 0;
                    onChanged();
                    return this;
                }

                @java.lang.Override
                public final Builder setUnknownFields(
                        final com.google.protobuf.UnknownFieldSet unknownFields) {
                    return super.setUnknownFields(unknownFields);
                }

                @java.lang.Override
                public final Builder mergeUnknownFields(
                        final com.google.protobuf.UnknownFieldSet unknownFields) {
                    return super.mergeUnknownFields(unknownFields);
                }


                // @@protoc_insertion_point(builder_scope:com.iflytek.traffic.log.MaterialLog.Material)
            }

            // @@protoc_insertion_point(class_scope:com.iflytek.traffic.log.MaterialLog.Material)
            private static final com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material DEFAULT_INSTANCE;

            static {
                DEFAULT_INSTANCE = new com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material();
            }

            public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material getDefaultInstance() {
                return DEFAULT_INSTANCE;
            }

            @java.lang.Deprecated
            public static final com.google.protobuf.Parser<Material>
                    PARSER = new com.google.protobuf.AbstractParser<Material>() {
                @java.lang.Override
                public Material parsePartialFrom(
                        com.google.protobuf.CodedInputStream input,
                        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                        throws com.google.protobuf.InvalidProtocolBufferException {
                    Builder builder = newBuilder();
                    try {
                        builder.mergeFrom(input, extensionRegistry);
                    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                        throw e.setUnfinishedMessage(builder.buildPartial());
                    } catch (com.google.protobuf.UninitializedMessageException e) {
                        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
                    } catch (java.io.IOException e) {
                        throw new com.google.protobuf.InvalidProtocolBufferException(e)
                                .setUnfinishedMessage(builder.buildPartial());
                    }
                    return builder.buildPartial();
                }
            };

            public static com.google.protobuf.Parser<Material> parser() {
                return PARSER;
            }

            @java.lang.Override
            public com.google.protobuf.Parser<Material> getParserForType() {
                return PARSER;
            }

            @java.lang.Override
            public com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material getDefaultInstanceForType() {
                return DEFAULT_INSTANCE;
            }

        }

        private int bitField0_;
        public static final int DSP_ID_FIELD_NUMBER = 1;
        private long dspId_;

        /**
         * <pre>
         * dsp 信息
         * </pre>
         *
         * <code>required int64 dsp_id = 1;</code>
         * @return Whether the dspId field is set.
         */
        @java.lang.Override
        public boolean hasDspId() {
            return ((bitField0_ & 0x00000001) != 0);
        }

        /**
         * <pre>
         * dsp 信息
         * </pre>
         *
         * <code>required int64 dsp_id = 1;</code>
         * @return The dspId.
         */
        @java.lang.Override
        public long getDspId() {
            return dspId_;
        }

        public static final int DSP_EP_ID_FIELD_NUMBER = 2;
        private long dspEpId_;

        /**
         * <code>required int64 dsp_ep_id = 2;</code>
         * @return Whether the dspEpId field is set.
         */
        @java.lang.Override
        public boolean hasDspEpId() {
            return ((bitField0_ & 0x00000002) != 0);
        }

        /**
         * <code>required int64 dsp_ep_id = 2;</code>
         * @return The dspEpId.
         */
        @java.lang.Override
        public long getDspEpId() {
            return dspEpId_;
        }

        public static final int REGION_FIELD_NUMBER = 4;
        private volatile java.lang.Object region_;

        /**
         * <pre>
         * 地区信息
         * </pre>
         *
         * <code>required string region = 4;</code>
         * @return Whether the region field is set.
         */
        @java.lang.Override
        public boolean hasRegion() {
            return ((bitField0_ & 0x00000004) != 0);
        }

        /**
         * <pre>
         * 地区信息
         * </pre>
         *
         * <code>required string region = 4;</code>
         * @return The region.
         */
        @java.lang.Override
        public java.lang.String getRegion() {
            java.lang.Object ref = region_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                if (bs.isValidUtf8()) {
                    region_ = s;
                }
                return s;
            }
        }

        /**
         * <pre>
         * 地区信息
         * </pre>
         *
         * <code>required string region = 4;</code>
         * @return The bytes for region.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getRegionBytes() {
            java.lang.Object ref = region_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                region_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int SSP_ID_FIELD_NUMBER = 5;
        private long sspId_;

        /**
         * <pre>
         * ssp 信息
         * </pre>
         *
         * <code>required int64 ssp_id = 5;</code>
         * @return Whether the sspId field is set.
         */
        @java.lang.Override
        public boolean hasSspId() {
            return ((bitField0_ & 0x00000008) != 0);
        }

        /**
         * <pre>
         * ssp 信息
         * </pre>
         *
         * <code>required int64 ssp_id = 5;</code>
         * @return The sspId.
         */
        @java.lang.Override
        public long getSspId() {
            return sspId_;
        }

        public static final int SSP_EP_ID_FIELD_NUMBER = 6;
        private long sspEpId_;

        /**
         * <code>required int64 ssp_ep_id = 6;</code>
         * @return Whether the sspEpId field is set.
         */
        @java.lang.Override
        public boolean hasSspEpId() {
            return ((bitField0_ & 0x00000010) != 0);
        }

        /**
         * <code>required int64 ssp_ep_id = 6;</code>
         * @return The sspEpId.
         */
        @java.lang.Override
        public long getSspEpId() {
            return sspEpId_;
        }

        public static final int SSP_HASH_ID_FIELD_NUMBER = 7;
        private volatile java.lang.Object sspHashId_;

        /**
         * <code>optional string ssp_hash_id = 7;</code>
         * @return Whether the sspHashId field is set.
         */
        @java.lang.Override
        public boolean hasSspHashId() {
            return ((bitField0_ & 0x00000020) != 0);
        }

        /**
         * <code>optional string ssp_hash_id = 7;</code>
         * @return The sspHashId.
         */
        @java.lang.Override
        public java.lang.String getSspHashId() {
            java.lang.Object ref = sspHashId_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                if (bs.isValidUtf8()) {
                    sspHashId_ = s;
                }
                return s;
            }
        }

        /**
         * <code>optional string ssp_hash_id = 7;</code>
         * @return The bytes for sspHashId.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getSspHashIdBytes() {
            java.lang.Object ref = sspHashId_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                sspHashId_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int MATERIAL_FIELD_NUMBER = 8;
        private com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material material_;

        /**
         * <pre>
         * 物料
         * </pre>
         *
         * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
         * @return Whether the material field is set.
         */
        @java.lang.Override
        public boolean hasMaterial() {
            return ((bitField0_ & 0x00000040) != 0);
        }

        /**
         * <pre>
         * 物料
         * </pre>
         *
         * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
         * @return The material.
         */
        @java.lang.Override
        public com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material getMaterial() {
            return material_ == null ? com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.getDefaultInstance() : material_;
        }

        /**
         * <pre>
         * 物料
         * </pre>
         *
         * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
         */
        @java.lang.Override
        public com.iflytek.traffic.log.MaterialLogProto.MaterialLog.MaterialOrBuilder getMaterialOrBuilder() {
            return material_ == null ? com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.getDefaultInstance() : material_;
        }

        public static final int SSP_AUDIT_FIELD_NUMBER = 9;
        private boolean sspAudit_;

        /**
         * <pre>
         * 如果为 true 可能需要往 t_ssp_material_info 插入数据
         * </pre>
         *
         * <code>required bool ssp_audit = 9;</code>
         * @return Whether the sspAudit field is set.
         */
        @java.lang.Override
        public boolean hasSspAudit() {
            return ((bitField0_ & 0x00000080) != 0);
        }

        /**
         * <pre>
         * 如果为 true 可能需要往 t_ssp_material_info 插入数据
         * </pre>
         *
         * <code>required bool ssp_audit = 9;</code>
         * @return The sspAudit.
         */
        @java.lang.Override
        public boolean getSspAudit() {
            return sspAudit_;
        }

        public static final int SSP_AUDIT_HASH_ID_FIELD_NUMBER = 11;
        private volatile java.lang.Object sspAuditHashId_;

        /**
         * <code>optional string ssp_audit_hash_id = 11;</code>
         * @return Whether the sspAuditHashId field is set.
         */
        @java.lang.Override
        public boolean hasSspAuditHashId() {
            return ((bitField0_ & 0x00000100) != 0);
        }

        /**
         * <code>optional string ssp_audit_hash_id = 11;</code>
         * @return The sspAuditHashId.
         */
        @java.lang.Override
        public java.lang.String getSspAuditHashId() {
            java.lang.Object ref = sspAuditHashId_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                if (bs.isValidUtf8()) {
                    sspAuditHashId_ = s;
                }
                return s;
            }
        }

        /**
         * <code>optional string ssp_audit_hash_id = 11;</code>
         * @return The bytes for sspAuditHashId.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getSspAuditHashIdBytes() {
            java.lang.Object ref = sspAuditHashId_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                sspAuditHashId_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int MACHINE_AUDIT_FIELD_NUMBER = 10;
        private boolean machineAudit_;

        /**
         * <pre>
         * 如果为 true 可能需要往 t_machine_audit_material_info  插入数据
         * </pre>
         *
         * <code>required bool machine_audit = 10;</code>
         * @return Whether the machineAudit field is set.
         */
        @java.lang.Override
        public boolean hasMachineAudit() {
            return ((bitField0_ & 0x00000200) != 0);
        }

        /**
         * <pre>
         * 如果为 true 可能需要往 t_machine_audit_material_info  插入数据
         * </pre>
         *
         * <code>required bool machine_audit = 10;</code>
         * @return The machineAudit.
         */
        @java.lang.Override
        public boolean getMachineAudit() {
            return machineAudit_;
        }

        public static final int MACHINE_AUDIT_HASH_ID_FIELD_NUMBER = 12;
        private volatile java.lang.Object machineAuditHashId_;

        /**
         * <code>optional string machine_audit_hash_id = 12;</code>
         * @return Whether the machineAuditHashId field is set.
         */
        @java.lang.Override
        public boolean hasMachineAuditHashId() {
            return ((bitField0_ & 0x00000400) != 0);
        }

        /**
         * <code>optional string machine_audit_hash_id = 12;</code>
         * @return The machineAuditHashId.
         */
        @java.lang.Override
        public java.lang.String getMachineAuditHashId() {
            java.lang.Object ref = machineAuditHashId_;
            if (ref instanceof java.lang.String) {
                return (java.lang.String) ref;
            } else {
                com.google.protobuf.ByteString bs =
                        (com.google.protobuf.ByteString) ref;
                java.lang.String s = bs.toStringUtf8();
                if (bs.isValidUtf8()) {
                    machineAuditHashId_ = s;
                }
                return s;
            }
        }

        /**
         * <code>optional string machine_audit_hash_id = 12;</code>
         * @return The bytes for machineAuditHashId.
         */
        @java.lang.Override
        public com.google.protobuf.ByteString
        getMachineAuditHashIdBytes() {
            java.lang.Object ref = machineAuditHashId_;
            if (ref instanceof java.lang.String) {
                com.google.protobuf.ByteString b =
                        com.google.protobuf.ByteString.copyFromUtf8(
                                (java.lang.String) ref);
                machineAuditHashId_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        public static final int OS_FIELD_NUMBER = 13;
        private int os_;

        /**
         * <code>required int32 os = 13;</code>
         * @return Whether the os field is set.
         */
        @java.lang.Override
        public boolean hasOs() {
            return ((bitField0_ & 0x00000800) != 0);
        }

        /**
         * <code>required int32 os = 13;</code>
         * @return The os.
         */
        @java.lang.Override
        public int getOs() {
            return os_;
        }

        private byte memoizedIsInitialized = -1;

        @java.lang.Override
        public final boolean isInitialized() {
            byte isInitialized = memoizedIsInitialized;
            if (isInitialized == 1) return true;
            if (isInitialized == 0) return false;

            if (!hasDspId()) {
                memoizedIsInitialized = 0;
                return false;
            }
            if (!hasDspEpId()) {
                memoizedIsInitialized = 0;
                return false;
            }
            if (!hasRegion()) {
                memoizedIsInitialized = 0;
                return false;
            }
            if (!hasSspId()) {
                memoizedIsInitialized = 0;
                return false;
            }
            if (!hasSspEpId()) {
                memoizedIsInitialized = 0;
                return false;
            }
            if (!hasMaterial()) {
                memoizedIsInitialized = 0;
                return false;
            }
            if (!hasSspAudit()) {
                memoizedIsInitialized = 0;
                return false;
            }
            if (!hasMachineAudit()) {
                memoizedIsInitialized = 0;
                return false;
            }
            if (!hasOs()) {
                memoizedIsInitialized = 0;
                return false;
            }
            if (!getMaterial().isInitialized()) {
                memoizedIsInitialized = 0;
                return false;
            }
            memoizedIsInitialized = 1;
            return true;
        }

        @java.lang.Override
        public void writeTo(com.google.protobuf.CodedOutputStream output)
                throws java.io.IOException {
            if (((bitField0_ & 0x00000001) != 0)) {
                output.writeInt64(1, dspId_);
            }
            if (((bitField0_ & 0x00000002) != 0)) {
                output.writeInt64(2, dspEpId_);
            }
            if (((bitField0_ & 0x00000004) != 0)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 4, region_);
            }
            if (((bitField0_ & 0x00000008) != 0)) {
                output.writeInt64(5, sspId_);
            }
            if (((bitField0_ & 0x00000010) != 0)) {
                output.writeInt64(6, sspEpId_);
            }
            if (((bitField0_ & 0x00000020) != 0)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 7, sspHashId_);
            }
            if (((bitField0_ & 0x00000040) != 0)) {
                output.writeMessage(8, getMaterial());
            }
            if (((bitField0_ & 0x00000080) != 0)) {
                output.writeBool(9, sspAudit_);
            }
            if (((bitField0_ & 0x00000200) != 0)) {
                output.writeBool(10, machineAudit_);
            }
            if (((bitField0_ & 0x00000100) != 0)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 11, sspAuditHashId_);
            }
            if (((bitField0_ & 0x00000400) != 0)) {
                com.google.protobuf.GeneratedMessageV3.writeString(output, 12, machineAuditHashId_);
            }
            if (((bitField0_ & 0x00000800) != 0)) {
                output.writeInt32(13, os_);
            }
            getUnknownFields().writeTo(output);
        }

        @java.lang.Override
        public int getSerializedSize() {
            int size = memoizedSize;
            if (size != -1) return size;

            size = 0;
            if (((bitField0_ & 0x00000001) != 0)) {
                size += com.google.protobuf.CodedOutputStream
                        .computeInt64Size(1, dspId_);
            }
            if (((bitField0_ & 0x00000002) != 0)) {
                size += com.google.protobuf.CodedOutputStream
                        .computeInt64Size(2, dspEpId_);
            }
            if (((bitField0_ & 0x00000004) != 0)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, region_);
            }
            if (((bitField0_ & 0x00000008) != 0)) {
                size += com.google.protobuf.CodedOutputStream
                        .computeInt64Size(5, sspId_);
            }
            if (((bitField0_ & 0x00000010) != 0)) {
                size += com.google.protobuf.CodedOutputStream
                        .computeInt64Size(6, sspEpId_);
            }
            if (((bitField0_ & 0x00000020) != 0)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, sspHashId_);
            }
            if (((bitField0_ & 0x00000040) != 0)) {
                size += com.google.protobuf.CodedOutputStream
                        .computeMessageSize(8, getMaterial());
            }
            if (((bitField0_ & 0x00000080) != 0)) {
                size += com.google.protobuf.CodedOutputStream
                        .computeBoolSize(9, sspAudit_);
            }
            if (((bitField0_ & 0x00000200) != 0)) {
                size += com.google.protobuf.CodedOutputStream
                        .computeBoolSize(10, machineAudit_);
            }
            if (((bitField0_ & 0x00000100) != 0)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, sspAuditHashId_);
            }
            if (((bitField0_ & 0x00000400) != 0)) {
                size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, machineAuditHashId_);
            }
            if (((bitField0_ & 0x00000800) != 0)) {
                size += com.google.protobuf.CodedOutputStream
                        .computeInt32Size(13, os_);
            }
            size += getUnknownFields().getSerializedSize();
            memoizedSize = size;
            return size;
        }

        @java.lang.Override
        public boolean equals(final java.lang.Object obj) {
            if (obj == this) {
                return true;
            }
            if (!(obj instanceof com.iflytek.traffic.log.MaterialLogProto.MaterialLog)) {
                return super.equals(obj);
            }
            com.iflytek.traffic.log.MaterialLogProto.MaterialLog other = (com.iflytek.traffic.log.MaterialLogProto.MaterialLog) obj;

            if (hasDspId() != other.hasDspId()) return false;
            if (hasDspId()) {
                if (getDspId()
                        != other.getDspId()) return false;
            }
            if (hasDspEpId() != other.hasDspEpId()) return false;
            if (hasDspEpId()) {
                if (getDspEpId()
                        != other.getDspEpId()) return false;
            }
            if (hasRegion() != other.hasRegion()) return false;
            if (hasRegion()) {
                if (!getRegion()
                        .equals(other.getRegion())) return false;
            }
            if (hasSspId() != other.hasSspId()) return false;
            if (hasSspId()) {
                if (getSspId()
                        != other.getSspId()) return false;
            }
            if (hasSspEpId() != other.hasSspEpId()) return false;
            if (hasSspEpId()) {
                if (getSspEpId()
                        != other.getSspEpId()) return false;
            }
            if (hasSspHashId() != other.hasSspHashId()) return false;
            if (hasSspHashId()) {
                if (!getSspHashId()
                        .equals(other.getSspHashId())) return false;
            }
            if (hasMaterial() != other.hasMaterial()) return false;
            if (hasMaterial()) {
                if (!getMaterial()
                        .equals(other.getMaterial())) return false;
            }
            if (hasSspAudit() != other.hasSspAudit()) return false;
            if (hasSspAudit()) {
                if (getSspAudit()
                        != other.getSspAudit()) return false;
            }
            if (hasSspAuditHashId() != other.hasSspAuditHashId()) return false;
            if (hasSspAuditHashId()) {
                if (!getSspAuditHashId()
                        .equals(other.getSspAuditHashId())) return false;
            }
            if (hasMachineAudit() != other.hasMachineAudit()) return false;
            if (hasMachineAudit()) {
                if (getMachineAudit()
                        != other.getMachineAudit()) return false;
            }
            if (hasMachineAuditHashId() != other.hasMachineAuditHashId()) return false;
            if (hasMachineAuditHashId()) {
                if (!getMachineAuditHashId()
                        .equals(other.getMachineAuditHashId())) return false;
            }
            if (hasOs() != other.hasOs()) return false;
            if (hasOs()) {
                if (getOs()
                        != other.getOs()) return false;
            }
            if (!getUnknownFields().equals(other.getUnknownFields())) return false;
            return true;
        }

        @java.lang.Override
        public int hashCode() {
            if (memoizedHashCode != 0) {
                return memoizedHashCode;
            }
            int hash = 41;
            hash = (19 * hash) + getDescriptor().hashCode();
            if (hasDspId()) {
                hash = (37 * hash) + DSP_ID_FIELD_NUMBER;
                hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
                        getDspId());
            }
            if (hasDspEpId()) {
                hash = (37 * hash) + DSP_EP_ID_FIELD_NUMBER;
                hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
                        getDspEpId());
            }
            if (hasRegion()) {
                hash = (37 * hash) + REGION_FIELD_NUMBER;
                hash = (53 * hash) + getRegion().hashCode();
            }
            if (hasSspId()) {
                hash = (37 * hash) + SSP_ID_FIELD_NUMBER;
                hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
                        getSspId());
            }
            if (hasSspEpId()) {
                hash = (37 * hash) + SSP_EP_ID_FIELD_NUMBER;
                hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
                        getSspEpId());
            }
            if (hasSspHashId()) {
                hash = (37 * hash) + SSP_HASH_ID_FIELD_NUMBER;
                hash = (53 * hash) + getSspHashId().hashCode();
            }
            if (hasMaterial()) {
                hash = (37 * hash) + MATERIAL_FIELD_NUMBER;
                hash = (53 * hash) + getMaterial().hashCode();
            }
            if (hasSspAudit()) {
                hash = (37 * hash) + SSP_AUDIT_FIELD_NUMBER;
                hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
                        getSspAudit());
            }
            if (hasSspAuditHashId()) {
                hash = (37 * hash) + SSP_AUDIT_HASH_ID_FIELD_NUMBER;
                hash = (53 * hash) + getSspAuditHashId().hashCode();
            }
            if (hasMachineAudit()) {
                hash = (37 * hash) + MACHINE_AUDIT_FIELD_NUMBER;
                hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
                        getMachineAudit());
            }
            if (hasMachineAuditHashId()) {
                hash = (37 * hash) + MACHINE_AUDIT_HASH_ID_FIELD_NUMBER;
                hash = (53 * hash) + getMachineAuditHashId().hashCode();
            }
            if (hasOs()) {
                hash = (37 * hash) + OS_FIELD_NUMBER;
                hash = (53 * hash) + getOs();
            }
            hash = (29 * hash) + getUnknownFields().hashCode();
            memoizedHashCode = hash;
            return hash;
        }

        public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog parseFrom(
                java.nio.ByteBuffer data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog parseFrom(
                java.nio.ByteBuffer data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog parseFrom(
                com.google.protobuf.ByteString data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog parseFrom(
                com.google.protobuf.ByteString data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog parseFrom(byte[] data)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data);
        }

        public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog parseFrom(
                byte[] data,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return PARSER.parseFrom(data, extensionRegistry);
        }

        public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog parseFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog parseFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog parseDelimitedFrom(java.io.InputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input);
        }

        public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog parseDelimitedFrom(
                java.io.InputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }

        public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog parseFrom(
                com.google.protobuf.CodedInputStream input)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input);
        }

        public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog parseFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws java.io.IOException {
            return com.google.protobuf.GeneratedMessageV3
                    .parseWithIOException(PARSER, input, extensionRegistry);
        }

        @java.lang.Override
        public Builder newBuilderForType() {
            return newBuilder();
        }

        public static Builder newBuilder() {
            return DEFAULT_INSTANCE.toBuilder();
        }

        public static Builder newBuilder(com.iflytek.traffic.log.MaterialLogProto.MaterialLog prototype) {
            return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }

        @java.lang.Override
        public Builder toBuilder() {
            return this == DEFAULT_INSTANCE
                    ? new Builder() : new Builder().mergeFrom(this);
        }

        @java.lang.Override
        protected Builder newBuilderForType(
                com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
            Builder builder = new Builder(parent);
            return builder;
        }

        /**
         * Protobuf type {@code com.iflytek.traffic.log.MaterialLog}
         */
        public static final class Builder extends
                com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
                // @@protoc_insertion_point(builder_implements:com.iflytek.traffic.log.MaterialLog)
                com.iflytek.traffic.log.MaterialLogProto.MaterialLogOrBuilder {
            public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
                return com.iflytek.traffic.log.MaterialLogProto.internal_static_com_iflytek_traffic_log_MaterialLog_descriptor;
            }

            @java.lang.Override
            protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
                return com.iflytek.traffic.log.MaterialLogProto.internal_static_com_iflytek_traffic_log_MaterialLog_fieldAccessorTable
                        .ensureFieldAccessorsInitialized(
                                com.iflytek.traffic.log.MaterialLogProto.MaterialLog.class, com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Builder.class);
            }

            // Construct using com.iflytek.traffic.log.MaterialLogProto.MaterialLog.newBuilder()
            private Builder() {
                maybeForceBuilderInitialization();
            }

            private Builder(
                    com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
                super(parent);
                maybeForceBuilderInitialization();
            }

            private void maybeForceBuilderInitialization() {
                if (com.google.protobuf.GeneratedMessageV3
                        .alwaysUseFieldBuilders) {
                    getMaterialFieldBuilder();
                }
            }

            @java.lang.Override
            public Builder clear() {
                super.clear();
                dspId_ = 0L;
                bitField0_ = (bitField0_ & ~0x00000001);
                dspEpId_ = 0L;
                bitField0_ = (bitField0_ & ~0x00000002);
                region_ = "";
                bitField0_ = (bitField0_ & ~0x00000004);
                sspId_ = 0L;
                bitField0_ = (bitField0_ & ~0x00000008);
                sspEpId_ = 0L;
                bitField0_ = (bitField0_ & ~0x00000010);
                sspHashId_ = "";
                bitField0_ = (bitField0_ & ~0x00000020);
                if (materialBuilder_ == null) {
                    material_ = null;
                } else {
                    materialBuilder_.clear();
                }
                bitField0_ = (bitField0_ & ~0x00000040);
                sspAudit_ = false;
                bitField0_ = (bitField0_ & ~0x00000080);
                sspAuditHashId_ = "";
                bitField0_ = (bitField0_ & ~0x00000100);
                machineAudit_ = false;
                bitField0_ = (bitField0_ & ~0x00000200);
                machineAuditHashId_ = "";
                bitField0_ = (bitField0_ & ~0x00000400);
                os_ = 0;
                bitField0_ = (bitField0_ & ~0x00000800);
                return this;
            }

            @java.lang.Override
            public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
                return com.iflytek.traffic.log.MaterialLogProto.internal_static_com_iflytek_traffic_log_MaterialLog_descriptor;
            }

            @java.lang.Override
            public com.iflytek.traffic.log.MaterialLogProto.MaterialLog getDefaultInstanceForType() {
                return com.iflytek.traffic.log.MaterialLogProto.MaterialLog.getDefaultInstance();
            }

            @java.lang.Override
            public com.iflytek.traffic.log.MaterialLogProto.MaterialLog build() {
                com.iflytek.traffic.log.MaterialLogProto.MaterialLog result = buildPartial();
                if (!result.isInitialized()) {
                    throw newUninitializedMessageException(result);
                }
                return result;
            }

            @java.lang.Override
            public com.iflytek.traffic.log.MaterialLogProto.MaterialLog buildPartial() {
                com.iflytek.traffic.log.MaterialLogProto.MaterialLog result = new com.iflytek.traffic.log.MaterialLogProto.MaterialLog(this);
                int from_bitField0_ = bitField0_;
                int to_bitField0_ = 0;
                if (((from_bitField0_ & 0x00000001) != 0)) {
                    result.dspId_ = dspId_;
                    to_bitField0_ |= 0x00000001;
                }
                if (((from_bitField0_ & 0x00000002) != 0)) {
                    result.dspEpId_ = dspEpId_;
                    to_bitField0_ |= 0x00000002;
                }
                if (((from_bitField0_ & 0x00000004) != 0)) {
                    to_bitField0_ |= 0x00000004;
                }
                result.region_ = region_;
                if (((from_bitField0_ & 0x00000008) != 0)) {
                    result.sspId_ = sspId_;
                    to_bitField0_ |= 0x00000008;
                }
                if (((from_bitField0_ & 0x00000010) != 0)) {
                    result.sspEpId_ = sspEpId_;
                    to_bitField0_ |= 0x00000010;
                }
                if (((from_bitField0_ & 0x00000020) != 0)) {
                    to_bitField0_ |= 0x00000020;
                }
                result.sspHashId_ = sspHashId_;
                if (((from_bitField0_ & 0x00000040) != 0)) {
                    if (materialBuilder_ == null) {
                        result.material_ = material_;
                    } else {
                        result.material_ = materialBuilder_.build();
                    }
                    to_bitField0_ |= 0x00000040;
                }
                if (((from_bitField0_ & 0x00000080) != 0)) {
                    result.sspAudit_ = sspAudit_;
                    to_bitField0_ |= 0x00000080;
                }
                if (((from_bitField0_ & 0x00000100) != 0)) {
                    to_bitField0_ |= 0x00000100;
                }
                result.sspAuditHashId_ = sspAuditHashId_;
                if (((from_bitField0_ & 0x00000200) != 0)) {
                    result.machineAudit_ = machineAudit_;
                    to_bitField0_ |= 0x00000200;
                }
                if (((from_bitField0_ & 0x00000400) != 0)) {
                    to_bitField0_ |= 0x00000400;
                }
                result.machineAuditHashId_ = machineAuditHashId_;
                if (((from_bitField0_ & 0x00000800) != 0)) {
                    result.os_ = os_;
                    to_bitField0_ |= 0x00000800;
                }
                result.bitField0_ = to_bitField0_;
                onBuilt();
                return result;
            }

            @java.lang.Override
            public Builder clone() {
                return super.clone();
            }

            @java.lang.Override
            public Builder setField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.setField(field, value);
            }

            @java.lang.Override
            public Builder clearField(
                    com.google.protobuf.Descriptors.FieldDescriptor field) {
                return super.clearField(field);
            }

            @java.lang.Override
            public Builder clearOneof(
                    com.google.protobuf.Descriptors.OneofDescriptor oneof) {
                return super.clearOneof(oneof);
            }

            @java.lang.Override
            public Builder setRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    int index, java.lang.Object value) {
                return super.setRepeatedField(field, index, value);
            }

            @java.lang.Override
            public Builder addRepeatedField(
                    com.google.protobuf.Descriptors.FieldDescriptor field,
                    java.lang.Object value) {
                return super.addRepeatedField(field, value);
            }

            @java.lang.Override
            public Builder mergeFrom(com.google.protobuf.Message other) {
                if (other instanceof com.iflytek.traffic.log.MaterialLogProto.MaterialLog) {
                    return mergeFrom((com.iflytek.traffic.log.MaterialLogProto.MaterialLog) other);
                } else {
                    super.mergeFrom(other);
                    return this;
                }
            }

            public Builder mergeFrom(com.iflytek.traffic.log.MaterialLogProto.MaterialLog other) {
                if (other == com.iflytek.traffic.log.MaterialLogProto.MaterialLog.getDefaultInstance()) return this;
                if (other.hasDspId()) {
                    setDspId(other.getDspId());
                }
                if (other.hasDspEpId()) {
                    setDspEpId(other.getDspEpId());
                }
                if (other.hasRegion()) {
                    bitField0_ |= 0x00000004;
                    region_ = other.region_;
                    onChanged();
                }
                if (other.hasSspId()) {
                    setSspId(other.getSspId());
                }
                if (other.hasSspEpId()) {
                    setSspEpId(other.getSspEpId());
                }
                if (other.hasSspHashId()) {
                    bitField0_ |= 0x00000020;
                    sspHashId_ = other.sspHashId_;
                    onChanged();
                }
                if (other.hasMaterial()) {
                    mergeMaterial(other.getMaterial());
                }
                if (other.hasSspAudit()) {
                    setSspAudit(other.getSspAudit());
                }
                if (other.hasSspAuditHashId()) {
                    bitField0_ |= 0x00000100;
                    sspAuditHashId_ = other.sspAuditHashId_;
                    onChanged();
                }
                if (other.hasMachineAudit()) {
                    setMachineAudit(other.getMachineAudit());
                }
                if (other.hasMachineAuditHashId()) {
                    bitField0_ |= 0x00000400;
                    machineAuditHashId_ = other.machineAuditHashId_;
                    onChanged();
                }
                if (other.hasOs()) {
                    setOs(other.getOs());
                }
                this.mergeUnknownFields(other.getUnknownFields());
                onChanged();
                return this;
            }

            @java.lang.Override
            public final boolean isInitialized() {
                if (!hasDspId()) {
                    return false;
                }
                if (!hasDspEpId()) {
                    return false;
                }
                if (!hasRegion()) {
                    return false;
                }
                if (!hasSspId()) {
                    return false;
                }
                if (!hasSspEpId()) {
                    return false;
                }
                if (!hasMaterial()) {
                    return false;
                }
                if (!hasSspAudit()) {
                    return false;
                }
                if (!hasMachineAudit()) {
                    return false;
                }
                if (!hasOs()) {
                    return false;
                }
                if (!getMaterial().isInitialized()) {
                    return false;
                }
                return true;
            }

            @java.lang.Override
            public Builder mergeFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws java.io.IOException {
                if (extensionRegistry == null) {
                    throw new java.lang.NullPointerException();
                }
                try {
                    boolean done = false;
                    while (!done) {
                        int tag = input.readTag();
                        switch (tag) {
                            case 0:
                                done = true;
                                break;
                            case 8: {
                                dspId_ = input.readInt64();
                                bitField0_ |= 0x00000001;
                                break;
                            } // case 8
                            case 16: {
                                dspEpId_ = input.readInt64();
                                bitField0_ |= 0x00000002;
                                break;
                            } // case 16
                            case 34: {
                                region_ = input.readBytes();
                                bitField0_ |= 0x00000004;
                                break;
                            } // case 34
                            case 40: {
                                sspId_ = input.readInt64();
                                bitField0_ |= 0x00000008;
                                break;
                            } // case 40
                            case 48: {
                                sspEpId_ = input.readInt64();
                                bitField0_ |= 0x00000010;
                                break;
                            } // case 48
                            case 58: {
                                sspHashId_ = input.readBytes();
                                bitField0_ |= 0x00000020;
                                break;
                            } // case 58
                            case 66: {
                                input.readMessage(
                                        getMaterialFieldBuilder().getBuilder(),
                                        extensionRegistry);
                                bitField0_ |= 0x00000040;
                                break;
                            } // case 66
                            case 72: {
                                sspAudit_ = input.readBool();
                                bitField0_ |= 0x00000080;
                                break;
                            } // case 72
                            case 80: {
                                machineAudit_ = input.readBool();
                                bitField0_ |= 0x00000200;
                                break;
                            } // case 80
                            case 90: {
                                sspAuditHashId_ = input.readBytes();
                                bitField0_ |= 0x00000100;
                                break;
                            } // case 90
                            case 98: {
                                machineAuditHashId_ = input.readBytes();
                                bitField0_ |= 0x00000400;
                                break;
                            } // case 98
                            case 104: {
                                os_ = input.readInt32();
                                bitField0_ |= 0x00000800;
                                break;
                            } // case 104
                            default: {
                                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                                    done = true; // was an endgroup tag
                                }
                                break;
                            } // default:
                        } // switch (tag)
                    } // while (!done)
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.unwrapIOException();
                } finally {
                    onChanged();
                } // finally
                return this;
            }

            private int bitField0_;

            private long dspId_;

            /**
             * <pre>
             * dsp 信息
             * </pre>
             *
             * <code>required int64 dsp_id = 1;</code>
             * @return Whether the dspId field is set.
             */
            @java.lang.Override
            public boolean hasDspId() {
                return ((bitField0_ & 0x00000001) != 0);
            }

            /**
             * <pre>
             * dsp 信息
             * </pre>
             *
             * <code>required int64 dsp_id = 1;</code>
             * @return The dspId.
             */
            @java.lang.Override
            public long getDspId() {
                return dspId_;
            }

            /**
             * <pre>
             * dsp 信息
             * </pre>
             *
             * <code>required int64 dsp_id = 1;</code>
             * @param value The dspId to set.
             * @return This builder for chaining.
             */
            public Builder setDspId(long value) {
                bitField0_ |= 0x00000001;
                dspId_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * dsp 信息
             * </pre>
             *
             * <code>required int64 dsp_id = 1;</code>
             * @return This builder for chaining.
             */
            public Builder clearDspId() {
                bitField0_ = (bitField0_ & ~0x00000001);
                dspId_ = 0L;
                onChanged();
                return this;
            }

            private long dspEpId_;

            /**
             * <code>required int64 dsp_ep_id = 2;</code>
             * @return Whether the dspEpId field is set.
             */
            @java.lang.Override
            public boolean hasDspEpId() {
                return ((bitField0_ & 0x00000002) != 0);
            }

            /**
             * <code>required int64 dsp_ep_id = 2;</code>
             * @return The dspEpId.
             */
            @java.lang.Override
            public long getDspEpId() {
                return dspEpId_;
            }

            /**
             * <code>required int64 dsp_ep_id = 2;</code>
             * @param value The dspEpId to set.
             * @return This builder for chaining.
             */
            public Builder setDspEpId(long value) {
                bitField0_ |= 0x00000002;
                dspEpId_ = value;
                onChanged();
                return this;
            }

            /**
             * <code>required int64 dsp_ep_id = 2;</code>
             * @return This builder for chaining.
             */
            public Builder clearDspEpId() {
                bitField0_ = (bitField0_ & ~0x00000002);
                dspEpId_ = 0L;
                onChanged();
                return this;
            }

            private java.lang.Object region_ = "";

            /**
             * <pre>
             * 地区信息
             * </pre>
             *
             * <code>required string region = 4;</code>
             * @return Whether the region field is set.
             */
            public boolean hasRegion() {
                return ((bitField0_ & 0x00000004) != 0);
            }

            /**
             * <pre>
             * 地区信息
             * </pre>
             *
             * <code>required string region = 4;</code>
             * @return The region.
             */
            public java.lang.String getRegion() {
                java.lang.Object ref = region_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    if (bs.isValidUtf8()) {
                        region_ = s;
                    }
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <pre>
             * 地区信息
             * </pre>
             *
             * <code>required string region = 4;</code>
             * @return The bytes for region.
             */
            public com.google.protobuf.ByteString
            getRegionBytes() {
                java.lang.Object ref = region_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    region_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <pre>
             * 地区信息
             * </pre>
             *
             * <code>required string region = 4;</code>
             * @param value The region to set.
             * @return This builder for chaining.
             */
            public Builder setRegion(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                bitField0_ |= 0x00000004;
                region_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 地区信息
             * </pre>
             *
             * <code>required string region = 4;</code>
             * @return This builder for chaining.
             */
            public Builder clearRegion() {
                bitField0_ = (bitField0_ & ~0x00000004);
                region_ = getDefaultInstance().getRegion();
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 地区信息
             * </pre>
             *
             * <code>required string region = 4;</code>
             * @param value The bytes for region to set.
             * @return This builder for chaining.
             */
            public Builder setRegionBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                bitField0_ |= 0x00000004;
                region_ = value;
                onChanged();
                return this;
            }

            private long sspId_;

            /**
             * <pre>
             * ssp 信息
             * </pre>
             *
             * <code>required int64 ssp_id = 5;</code>
             * @return Whether the sspId field is set.
             */
            @java.lang.Override
            public boolean hasSspId() {
                return ((bitField0_ & 0x00000008) != 0);
            }

            /**
             * <pre>
             * ssp 信息
             * </pre>
             *
             * <code>required int64 ssp_id = 5;</code>
             * @return The sspId.
             */
            @java.lang.Override
            public long getSspId() {
                return sspId_;
            }

            /**
             * <pre>
             * ssp 信息
             * </pre>
             *
             * <code>required int64 ssp_id = 5;</code>
             * @param value The sspId to set.
             * @return This builder for chaining.
             */
            public Builder setSspId(long value) {
                bitField0_ |= 0x00000008;
                sspId_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * ssp 信息
             * </pre>
             *
             * <code>required int64 ssp_id = 5;</code>
             * @return This builder for chaining.
             */
            public Builder clearSspId() {
                bitField0_ = (bitField0_ & ~0x00000008);
                sspId_ = 0L;
                onChanged();
                return this;
            }

            private long sspEpId_;

            /**
             * <code>required int64 ssp_ep_id = 6;</code>
             * @return Whether the sspEpId field is set.
             */
            @java.lang.Override
            public boolean hasSspEpId() {
                return ((bitField0_ & 0x00000010) != 0);
            }

            /**
             * <code>required int64 ssp_ep_id = 6;</code>
             * @return The sspEpId.
             */
            @java.lang.Override
            public long getSspEpId() {
                return sspEpId_;
            }

            /**
             * <code>required int64 ssp_ep_id = 6;</code>
             * @param value The sspEpId to set.
             * @return This builder for chaining.
             */
            public Builder setSspEpId(long value) {
                bitField0_ |= 0x00000010;
                sspEpId_ = value;
                onChanged();
                return this;
            }

            /**
             * <code>required int64 ssp_ep_id = 6;</code>
             * @return This builder for chaining.
             */
            public Builder clearSspEpId() {
                bitField0_ = (bitField0_ & ~0x00000010);
                sspEpId_ = 0L;
                onChanged();
                return this;
            }

            private java.lang.Object sspHashId_ = "";

            /**
             * <code>optional string ssp_hash_id = 7;</code>
             * @return Whether the sspHashId field is set.
             */
            public boolean hasSspHashId() {
                return ((bitField0_ & 0x00000020) != 0);
            }

            /**
             * <code>optional string ssp_hash_id = 7;</code>
             * @return The sspHashId.
             */
            public java.lang.String getSspHashId() {
                java.lang.Object ref = sspHashId_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    if (bs.isValidUtf8()) {
                        sspHashId_ = s;
                    }
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <code>optional string ssp_hash_id = 7;</code>
             * @return The bytes for sspHashId.
             */
            public com.google.protobuf.ByteString
            getSspHashIdBytes() {
                java.lang.Object ref = sspHashId_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    sspHashId_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <code>optional string ssp_hash_id = 7;</code>
             * @param value The sspHashId to set.
             * @return This builder for chaining.
             */
            public Builder setSspHashId(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                bitField0_ |= 0x00000020;
                sspHashId_ = value;
                onChanged();
                return this;
            }

            /**
             * <code>optional string ssp_hash_id = 7;</code>
             * @return This builder for chaining.
             */
            public Builder clearSspHashId() {
                bitField0_ = (bitField0_ & ~0x00000020);
                sspHashId_ = getDefaultInstance().getSspHashId();
                onChanged();
                return this;
            }

            /**
             * <code>optional string ssp_hash_id = 7;</code>
             * @param value The bytes for sspHashId to set.
             * @return This builder for chaining.
             */
            public Builder setSspHashIdBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                bitField0_ |= 0x00000020;
                sspHashId_ = value;
                onChanged();
                return this;
            }

            private com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material material_;
            private com.google.protobuf.SingleFieldBuilderV3<
                    com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material, com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.Builder, com.iflytek.traffic.log.MaterialLogProto.MaterialLog.MaterialOrBuilder> materialBuilder_;

            /**
             * <pre>
             * 物料
             * </pre>
             *
             * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
             * @return Whether the material field is set.
             */
            public boolean hasMaterial() {
                return ((bitField0_ & 0x00000040) != 0);
            }

            /**
             * <pre>
             * 物料
             * </pre>
             *
             * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
             * @return The material.
             */
            public com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material getMaterial() {
                if (materialBuilder_ == null) {
                    return material_ == null ? com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.getDefaultInstance() : material_;
                } else {
                    return materialBuilder_.getMessage();
                }
            }

            /**
             * <pre>
             * 物料
             * </pre>
             *
             * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
             */
            public Builder setMaterial(com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material value) {
                if (materialBuilder_ == null) {
                    if (value == null) {
                        throw new NullPointerException();
                    }
                    material_ = value;
                    onChanged();
                } else {
                    materialBuilder_.setMessage(value);
                }
                bitField0_ |= 0x00000040;
                return this;
            }

            /**
             * <pre>
             * 物料
             * </pre>
             *
             * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
             */
            public Builder setMaterial(
                    com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.Builder builderForValue) {
                if (materialBuilder_ == null) {
                    material_ = builderForValue.build();
                    onChanged();
                } else {
                    materialBuilder_.setMessage(builderForValue.build());
                }
                bitField0_ |= 0x00000040;
                return this;
            }

            /**
             * <pre>
             * 物料
             * </pre>
             *
             * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
             */
            public Builder mergeMaterial(com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material value) {
                if (materialBuilder_ == null) {
                    if (((bitField0_ & 0x00000040) != 0) &&
                            material_ != null &&
                            material_ != com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.getDefaultInstance()) {
                        material_ =
                                com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.newBuilder(material_).mergeFrom(value).buildPartial();
                    } else {
                        material_ = value;
                    }
                    onChanged();
                } else {
                    materialBuilder_.mergeFrom(value);
                }
                bitField0_ |= 0x00000040;
                return this;
            }

            /**
             * <pre>
             * 物料
             * </pre>
             *
             * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
             */
            public Builder clearMaterial() {
                if (materialBuilder_ == null) {
                    material_ = null;
                    onChanged();
                } else {
                    materialBuilder_.clear();
                }
                bitField0_ = (bitField0_ & ~0x00000040);
                return this;
            }

            /**
             * <pre>
             * 物料
             * </pre>
             *
             * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
             */
            public com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.Builder getMaterialBuilder() {
                bitField0_ |= 0x00000040;
                onChanged();
                return getMaterialFieldBuilder().getBuilder();
            }

            /**
             * <pre>
             * 物料
             * </pre>
             *
             * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
             */
            public com.iflytek.traffic.log.MaterialLogProto.MaterialLog.MaterialOrBuilder getMaterialOrBuilder() {
                if (materialBuilder_ != null) {
                    return materialBuilder_.getMessageOrBuilder();
                } else {
                    return material_ == null ?
                            com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.getDefaultInstance() : material_;
                }
            }

            /**
             * <pre>
             * 物料
             * </pre>
             *
             * <code>required .com.iflytek.traffic.log.MaterialLog.Material material = 8;</code>
             */
            private com.google.protobuf.SingleFieldBuilderV3<
                    com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material, com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.Builder, com.iflytek.traffic.log.MaterialLogProto.MaterialLog.MaterialOrBuilder>
            getMaterialFieldBuilder() {
                if (materialBuilder_ == null) {
                    materialBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
                            com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material, com.iflytek.traffic.log.MaterialLogProto.MaterialLog.Material.Builder, com.iflytek.traffic.log.MaterialLogProto.MaterialLog.MaterialOrBuilder>(
                            getMaterial(),
                            getParentForChildren(),
                            isClean());
                    material_ = null;
                }
                return materialBuilder_;
            }

            private boolean sspAudit_;

            /**
             * <pre>
             * 如果为 true 可能需要往 t_ssp_material_info 插入数据
             * </pre>
             *
             * <code>required bool ssp_audit = 9;</code>
             * @return Whether the sspAudit field is set.
             */
            @java.lang.Override
            public boolean hasSspAudit() {
                return ((bitField0_ & 0x00000080) != 0);
            }

            /**
             * <pre>
             * 如果为 true 可能需要往 t_ssp_material_info 插入数据
             * </pre>
             *
             * <code>required bool ssp_audit = 9;</code>
             * @return The sspAudit.
             */
            @java.lang.Override
            public boolean getSspAudit() {
                return sspAudit_;
            }

            /**
             * <pre>
             * 如果为 true 可能需要往 t_ssp_material_info 插入数据
             * </pre>
             *
             * <code>required bool ssp_audit = 9;</code>
             * @param value The sspAudit to set.
             * @return This builder for chaining.
             */
            public Builder setSspAudit(boolean value) {
                bitField0_ |= 0x00000080;
                sspAudit_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 如果为 true 可能需要往 t_ssp_material_info 插入数据
             * </pre>
             *
             * <code>required bool ssp_audit = 9;</code>
             * @return This builder for chaining.
             */
            public Builder clearSspAudit() {
                bitField0_ = (bitField0_ & ~0x00000080);
                sspAudit_ = false;
                onChanged();
                return this;
            }

            private java.lang.Object sspAuditHashId_ = "";

            /**
             * <code>optional string ssp_audit_hash_id = 11;</code>
             * @return Whether the sspAuditHashId field is set.
             */
            public boolean hasSspAuditHashId() {
                return ((bitField0_ & 0x00000100) != 0);
            }

            /**
             * <code>optional string ssp_audit_hash_id = 11;</code>
             * @return The sspAuditHashId.
             */
            public java.lang.String getSspAuditHashId() {
                java.lang.Object ref = sspAuditHashId_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    if (bs.isValidUtf8()) {
                        sspAuditHashId_ = s;
                    }
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <code>optional string ssp_audit_hash_id = 11;</code>
             * @return The bytes for sspAuditHashId.
             */
            public com.google.protobuf.ByteString
            getSspAuditHashIdBytes() {
                java.lang.Object ref = sspAuditHashId_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    sspAuditHashId_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <code>optional string ssp_audit_hash_id = 11;</code>
             * @param value The sspAuditHashId to set.
             * @return This builder for chaining.
             */
            public Builder setSspAuditHashId(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                bitField0_ |= 0x00000100;
                sspAuditHashId_ = value;
                onChanged();
                return this;
            }

            /**
             * <code>optional string ssp_audit_hash_id = 11;</code>
             * @return This builder for chaining.
             */
            public Builder clearSspAuditHashId() {
                bitField0_ = (bitField0_ & ~0x00000100);
                sspAuditHashId_ = getDefaultInstance().getSspAuditHashId();
                onChanged();
                return this;
            }

            /**
             * <code>optional string ssp_audit_hash_id = 11;</code>
             * @param value The bytes for sspAuditHashId to set.
             * @return This builder for chaining.
             */
            public Builder setSspAuditHashIdBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                bitField0_ |= 0x00000100;
                sspAuditHashId_ = value;
                onChanged();
                return this;
            }

            private boolean machineAudit_;

            /**
             * <pre>
             * 如果为 true 可能需要往 t_machine_audit_material_info  插入数据
             * </pre>
             *
             * <code>required bool machine_audit = 10;</code>
             * @return Whether the machineAudit field is set.
             */
            @java.lang.Override
            public boolean hasMachineAudit() {
                return ((bitField0_ & 0x00000200) != 0);
            }

            /**
             * <pre>
             * 如果为 true 可能需要往 t_machine_audit_material_info  插入数据
             * </pre>
             *
             * <code>required bool machine_audit = 10;</code>
             * @return The machineAudit.
             */
            @java.lang.Override
            public boolean getMachineAudit() {
                return machineAudit_;
            }

            /**
             * <pre>
             * 如果为 true 可能需要往 t_machine_audit_material_info  插入数据
             * </pre>
             *
             * <code>required bool machine_audit = 10;</code>
             * @param value The machineAudit to set.
             * @return This builder for chaining.
             */
            public Builder setMachineAudit(boolean value) {
                bitField0_ |= 0x00000200;
                machineAudit_ = value;
                onChanged();
                return this;
            }

            /**
             * <pre>
             * 如果为 true 可能需要往 t_machine_audit_material_info  插入数据
             * </pre>
             *
             * <code>required bool machine_audit = 10;</code>
             * @return This builder for chaining.
             */
            public Builder clearMachineAudit() {
                bitField0_ = (bitField0_ & ~0x00000200);
                machineAudit_ = false;
                onChanged();
                return this;
            }

            private java.lang.Object machineAuditHashId_ = "";

            /**
             * <code>optional string machine_audit_hash_id = 12;</code>
             * @return Whether the machineAuditHashId field is set.
             */
            public boolean hasMachineAuditHashId() {
                return ((bitField0_ & 0x00000400) != 0);
            }

            /**
             * <code>optional string machine_audit_hash_id = 12;</code>
             * @return The machineAuditHashId.
             */
            public java.lang.String getMachineAuditHashId() {
                java.lang.Object ref = machineAuditHashId_;
                if (!(ref instanceof java.lang.String)) {
                    com.google.protobuf.ByteString bs =
                            (com.google.protobuf.ByteString) ref;
                    java.lang.String s = bs.toStringUtf8();
                    if (bs.isValidUtf8()) {
                        machineAuditHashId_ = s;
                    }
                    return s;
                } else {
                    return (java.lang.String) ref;
                }
            }

            /**
             * <code>optional string machine_audit_hash_id = 12;</code>
             * @return The bytes for machineAuditHashId.
             */
            public com.google.protobuf.ByteString
            getMachineAuditHashIdBytes() {
                java.lang.Object ref = machineAuditHashId_;
                if (ref instanceof String) {
                    com.google.protobuf.ByteString b =
                            com.google.protobuf.ByteString.copyFromUtf8(
                                    (java.lang.String) ref);
                    machineAuditHashId_ = b;
                    return b;
                } else {
                    return (com.google.protobuf.ByteString) ref;
                }
            }

            /**
             * <code>optional string machine_audit_hash_id = 12;</code>
             * @param value The machineAuditHashId to set.
             * @return This builder for chaining.
             */
            public Builder setMachineAuditHashId(
                    java.lang.String value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                bitField0_ |= 0x00000400;
                machineAuditHashId_ = value;
                onChanged();
                return this;
            }

            /**
             * <code>optional string machine_audit_hash_id = 12;</code>
             * @return This builder for chaining.
             */
            public Builder clearMachineAuditHashId() {
                bitField0_ = (bitField0_ & ~0x00000400);
                machineAuditHashId_ = getDefaultInstance().getMachineAuditHashId();
                onChanged();
                return this;
            }

            /**
             * <code>optional string machine_audit_hash_id = 12;</code>
             * @param value The bytes for machineAuditHashId to set.
             * @return This builder for chaining.
             */
            public Builder setMachineAuditHashIdBytes(
                    com.google.protobuf.ByteString value) {
                if (value == null) {
                    throw new NullPointerException();
                }
                bitField0_ |= 0x00000400;
                machineAuditHashId_ = value;
                onChanged();
                return this;
            }

            private int os_;

            /**
             * <code>required int32 os = 13;</code>
             * @return Whether the os field is set.
             */
            @java.lang.Override
            public boolean hasOs() {
                return ((bitField0_ & 0x00000800) != 0);
            }

            /**
             * <code>required int32 os = 13;</code>
             * @return The os.
             */
            @java.lang.Override
            public int getOs() {
                return os_;
            }

            /**
             * <code>required int32 os = 13;</code>
             * @param value The os to set.
             * @return This builder for chaining.
             */
            public Builder setOs(int value) {
                bitField0_ |= 0x00000800;
                os_ = value;
                onChanged();
                return this;
            }

            /**
             * <code>required int32 os = 13;</code>
             * @return This builder for chaining.
             */
            public Builder clearOs() {
                bitField0_ = (bitField0_ & ~0x00000800);
                os_ = 0;
                onChanged();
                return this;
            }

            @java.lang.Override
            public final Builder setUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.setUnknownFields(unknownFields);
            }

            @java.lang.Override
            public final Builder mergeUnknownFields(
                    final com.google.protobuf.UnknownFieldSet unknownFields) {
                return super.mergeUnknownFields(unknownFields);
            }


            // @@protoc_insertion_point(builder_scope:com.iflytek.traffic.log.MaterialLog)
        }

        // @@protoc_insertion_point(class_scope:com.iflytek.traffic.log.MaterialLog)
        private static final com.iflytek.traffic.log.MaterialLogProto.MaterialLog DEFAULT_INSTANCE;

        static {
            DEFAULT_INSTANCE = new com.iflytek.traffic.log.MaterialLogProto.MaterialLog();
        }

        public static com.iflytek.traffic.log.MaterialLogProto.MaterialLog getDefaultInstance() {
            return DEFAULT_INSTANCE;
        }

        @java.lang.Deprecated
        public static final com.google.protobuf.Parser<MaterialLog>
                PARSER = new com.google.protobuf.AbstractParser<MaterialLog>() {
            @java.lang.Override
            public MaterialLog parsePartialFrom(
                    com.google.protobuf.CodedInputStream input,
                    com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                    throws com.google.protobuf.InvalidProtocolBufferException {
                Builder builder = newBuilder();
                try {
                    builder.mergeFrom(input, extensionRegistry);
                } catch (com.google.protobuf.InvalidProtocolBufferException e) {
                    throw e.setUnfinishedMessage(builder.buildPartial());
                } catch (com.google.protobuf.UninitializedMessageException e) {
                    throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
                } catch (java.io.IOException e) {
                    throw new com.google.protobuf.InvalidProtocolBufferException(e)
                            .setUnfinishedMessage(builder.buildPartial());
                }
                return builder.buildPartial();
            }
        };

        public static com.google.protobuf.Parser<MaterialLog> parser() {
            return PARSER;
        }

        @java.lang.Override
        public com.google.protobuf.Parser<MaterialLog> getParserForType() {
            return PARSER;
        }

        @java.lang.Override
        public com.iflytek.traffic.log.MaterialLogProto.MaterialLog getDefaultInstanceForType() {
            return DEFAULT_INSTANCE;
        }

    }

    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_com_iflytek_traffic_log_MaterialLog_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_com_iflytek_traffic_log_MaterialLog_fieldAccessorTable;
    private static final com.google.protobuf.Descriptors.Descriptor
            internal_static_com_iflytek_traffic_log_MaterialLog_Material_descriptor;
    private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internal_static_com_iflytek_traffic_log_MaterialLog_Material_fieldAccessorTable;

    public static com.google.protobuf.Descriptors.FileDescriptor
    getDescriptor() {
        return descriptor;
    }

    private static com.google.protobuf.Descriptors.FileDescriptor
            descriptor;

    static {
        java.lang.String[] descriptorData = {
                "\n\022material_log.proto\022\027com.iflytek.traffi" +
                        "c.log\"\275\004\n\013MaterialLog\022\016\n\006dsp_id\030\001 \002(\003\022\021\n" +
                        "\tdsp_ep_id\030\002 \002(\003\022\016\n\006region\030\004 \002(\t\022\016\n\006ssp_" +
                        "id\030\005 \002(\003\022\021\n\tssp_ep_id\030\006 \002(\003\022\023\n\013ssp_hash_" +
                        "id\030\007 \001(\t\022?\n\010material\030\010 \002(\0132-.com.iflytek" +
                        ".traffic.log.MaterialLog.Material\022\021\n\tssp" +
                        "_audit\030\t \002(\010\022\031\n\021ssp_audit_hash_id\030\013 \001(\t\022" +
                        "\025\n\rmachine_audit\030\n \002(\010\022\035\n\025machine_audit_" +
                        "hash_id\030\014 \001(\t\022\n\n\002os\030\r \002(\005\032\221\002\n\010Material\022\017" +
                        "\n\007hash_id\030\001 \002(\t\022\013\n\003adm\030\002 \001(\t\022\016\n\006bundle\030\003" +
                        " \001(\t\022\014\n\004iurl\030\004 \001(\t\022\013\n\003cid\030\005 \001(\t\022\013\n\003cat\030\006" +
                        " \003(\t\022\014\n\004crid\030\007 \002(\t\022\014\n\004attr\030\010 \003(\005\022\013\n\003api\030" +
                        "\t \001(\005\022\020\n\010protocol\030\n \001(\005\022\016\n\006dealId\030\013 \001(\t\022" +
                        "\t\n\001w\030\014 \001(\005\022\t\n\001h\030\r \001(\005\022\016\n\006wratio\030\016 \001(\005\022\016\n" +
                        "\006hratio\030\017 \001(\005\022\013\n\003ext\030\020 \001(\t\022\021\n\tadmHashId\030" +
                        "\021 \001(\t\022\016\n\006adType\030\022 \001(\005B+\n\027com.iflytek.tra" +
                        "ffic.logB\020MaterialLogProto"
        };
        descriptor = com.google.protobuf.Descriptors.FileDescriptor
                .internalBuildGeneratedFileFrom(descriptorData,
                        new com.google.protobuf.Descriptors.FileDescriptor[]{
                        });
        internal_static_com_iflytek_traffic_log_MaterialLog_descriptor =
                getDescriptor().getMessageTypes().get(0);
        internal_static_com_iflytek_traffic_log_MaterialLog_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_com_iflytek_traffic_log_MaterialLog_descriptor,
                new java.lang.String[]{"DspId", "DspEpId", "Region", "SspId", "SspEpId", "SspHashId", "Material", "SspAudit", "SspAuditHashId", "MachineAudit", "MachineAuditHashId", "Os",});
        internal_static_com_iflytek_traffic_log_MaterialLog_Material_descriptor =
                internal_static_com_iflytek_traffic_log_MaterialLog_descriptor.getNestedTypes().get(0);
        internal_static_com_iflytek_traffic_log_MaterialLog_Material_fieldAccessorTable = new
                com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_com_iflytek_traffic_log_MaterialLog_Material_descriptor,
                new java.lang.String[]{"HashId", "Adm", "Bundle", "Iurl", "Cid", "Cat", "Crid", "Attr", "Api", "Protocol", "DealId", "W", "H", "Wratio", "Hratio", "Ext", "AdmHashId", "AdType",});
    }

    // @@protoc_insertion_point(outer_class_scope)
}
