package com.iflytek.traffic.log;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.iflytek.traffic.data.provider.FusionProvider;
import com.iflytek.traffic.dsp.DspEpObj;
import com.iflytek.traffic.log.urtb.AdxLog;
import com.iflytek.traffic.log.urtb.RetrievalLog;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.session.asset.Asset;
import com.iflytek.traffic.session.request.*;
import com.iflytek.traffic.session.response.Bid;
import com.iflytek.traffic.session.response.UnifiedResponse;
import com.iflytek.traffic.ssp.SspEp;
import com.iflytek.traffic.util.HostUtil;
import com.iflytek.traffic.util.constant.Constants;
import com.iflytek.traffic.util.constant.LogConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @datetime 2025/5/10 12:35
 */
@Component
@Slf4j
public class LogService {

    private static final Logger abroadAdxTraceLog = LoggerFactory.getLogger("abroad_adx_trace_log");
    private static final Logger filebeatAbroadAdxTraceLog = LoggerFactory.getLogger("filebeat_abroad_adx_trace_log");

    private static final Logger filebeatAbroadAdxNoBidLog = LoggerFactory.getLogger("filebeat_abroad_adx_no_bid_log");
    private static final Logger abroadAdxNoBidLog = LoggerFactory.getLogger("abroad_adx_no_bid_log");
    private static final Logger retrievalLog = LoggerFactory.getLogger("abroad_adx_retrieval_log");

    @Value("${adx.trace.log.ratio:100}")
    private int traceLogRatio;

    @Value("${adx.filebeat.trace.log.ratio:100}")
    private int filebeatTraceLogRatio;

    @Value("#{'${log.filebeat.host:}'.split(',')}")
    private List<String> logFilebeatHost;

    @Value("${adx.retrieval.log.bid.ratio:100}")
    private int retrievalLogBidRatio;

    @Value("${adx.retrieval.log.nobid.ratio:100}")
    private int retrievalLogNoBidRatio;

    @Autowired
    private HostUtil hostUtil;

    public void logAdxTrace(SessionContext sessionContext) {
        if (sessionContext == null || sessionContext.getUnifiedRequest() == null) {
            return;
        }
        UnifiedRequest unifiedRequest = sessionContext.getUnifiedRequest();
        UnifiedResponse winUResp = sessionContext.getWinUResp();

        boolean unique = false;
        int uniqueNum = 0;
        //Map<String, String> bidTrace = new HashMap<>();
        //Map<String, String> noBid = new HashMap<>();
        Map<String, Impression> imps = unifiedRequest.getImps();

        // 针对整个请求，有竞价就全记，没有竞价就采样全记或全不记
        boolean isBid = winUResp != null && CollUtil.isNotEmpty(winUResp.getImpId2Bid());
	    boolean traceLogFlag = needTraceLog(isBid);
        boolean filebeatTraceLogFlag = needFilebeatTraceLog(isBid);
        if (!traceLogFlag && !filebeatTraceLogFlag) {
            log.debug("don't need adx trace log.");
            return;
        }

        for (String impId : imps.keySet()) {
            AdxLog.AdxTrace.Builder adxTrace = AdxLog.AdxTrace.newBuilder();
            adxTrace.setEventType(AdxLog.AdxTrace.TraceEvent.TRACEEVENT_REQ);
            Impression imp = imps.get(impId);
            AdxLog.AdxTrace.BidInfo.Builder bidInfo = logBidInfo(sessionContext, imp);
            adxTrace.setBidInfo(bidInfo);

            // 如果请求有响应，那么isEventUniq要放在某一个有响应的日志中。保证了响应数是正常的
            if (isBid) {
                if (bidInfo.getIsBid() && uniqueNum == 0) {
                    unique = true;
                }
            } else if (uniqueNum == 0) {
                unique = true;
            }
            AdxLog.AdxTrace.EventInfo.Builder eventInfo = logEventInfo(sessionContext, unique);
            if (unique) {
                unique = false;
                uniqueNum ++;
            }

            if (eventInfo != null) {
                adxTrace.setEventInfo(eventInfo);
            }
            AdxLog.AdxTrace.SSPInfo.Builder sspInfo = logSspInfo(unifiedRequest, sessionContext.getSspEp(), imp);
            adxTrace.setSspInfo(sspInfo);

            if (CollUtil.isNotEmpty(sessionContext.getRequestSucDspEp())) {
                for (Map.Entry<Integer, DspEpObj> entry : sessionContext.getRequestSucDspEp().entrySet()) {
                    DspEpObj dspEpObj = entry.getValue();
                    AdxLog.AdxTrace.EpInfo.Builder epInfo = logEpInfo(sessionContext, dspEpObj, imp);
                    adxTrace.addEpInfoList(epInfo);
                }
            }

            AdxLog.AdxTrace.AdInfo.Builder adInfo = logAdInfo(sessionContext.getWinUResp(), imp);
            if (adInfo != null) {
                adxTrace.setAdInfo(adInfo);
            }
            AdxLog.AdxTrace.RequestInfo.Builder requestInfo = logRequestInfo(unifiedRequest, imp);
            if (requestInfo != null) {
                adxTrace.setRequestInfo(requestInfo);
            }

            if (bidInfo.getIsBid()) {
                //bidTrace.put(impId, msg);
                if (traceLogFlag){
                    adxTrace.getEventInfoBuilder().setEventRatio(traceLogRatio/100);
                    String msg = Base64.encode(adxTrace.build().toByteArray());
                    abroadAdxTraceLog.info("{} {}", sessionContext.getStartTime4Log(), msg);
                }
                if (filebeatTraceLogFlag && logFilebeatHost != null && (logFilebeatHost.contains("*") || logFilebeatHost.contains(hostUtil.getIpPort()))){
                    adxTrace.getEventInfoBuilder().setEventRatio(filebeatTraceLogRatio/100);
                    String msg = Base64.encode(adxTrace.build().toByteArray());
                    filebeatAbroadAdxTraceLog.info("{} {}", sessionContext.getStartTime4Log(), msg);
                }
            } else {
                //noBid.put(impId, msg);
                if (traceLogFlag){
                    adxTrace.getEventInfoBuilder().setEventRatio(traceLogRatio/100);
                    String msg = Base64.encode(adxTrace.build().toByteArray());
                    abroadAdxNoBidLog.info("{} {}", sessionContext.getStartTime4Log(), msg);
                }
                if (filebeatTraceLogFlag && logFilebeatHost != null && (logFilebeatHost.contains("*") || logFilebeatHost.contains(hostUtil.getIpPort()))){
                    adxTrace.getEventInfoBuilder().setEventRatio(filebeatTraceLogRatio/100);
                    String msg = Base64.encode(adxTrace.build().toByteArray());
                    filebeatAbroadAdxNoBidLog.info("{} {}", sessionContext.getStartTime4Log(), msg);
                }
            }
        }

        //for (String log : bidTrace.values()) {
        //    abroadAdxTraceLog.info("{} {}", sessionContext.getStartTime4Log(), log);
        //    if (logFilebeatHost != null && (logFilebeatHost.contains("*") || logFilebeatHost.contains(hostUtil.getIpPort()))){
        //        filebeatAbroadAdxTraceLog.info("{} {}", sessionContext.getStartTime4Log(), log);
        //    }
        //}
        //
        //for (String log : noBid.values()) {
        //    abroadAdxNoBidLog.info("{} {}", sessionContext.getStartTime4Log(), log);
        //    if (logFilebeatHost != null && (logFilebeatHost.contains("*") || logFilebeatHost.contains(hostUtil.getIpPort()))){
        //        filebeatAbroadAdxTraceLog.info("{} {}", sessionContext.getStartTime4Log(), log);
        //    }
        //}
    }

    private AdxLog.AdxTrace.EventInfo.Builder logEventInfo(SessionContext sessionContext, boolean unique) {
        AdxLog.AdxTrace.EventInfo.Builder eventInfo = AdxLog.AdxTrace.EventInfo.newBuilder();
        if (sessionContext != null) {
            eventInfo.setEventTime(sessionContext.getStartTime() / 1000);
            AdxLog.AdxTrace.EventInfo.HttpInfo.Builder httpInfo = AdxLog.AdxTrace.EventInfo.HttpInfo.newBuilder();
            httpInfo.setArriveTime(sessionContext.getStartTime() / 1000);
            eventInfo.setEventNum(1);
            eventInfo.setIsEventUniq(unique);
            eventInfo.setHttpInfo(httpInfo);
            return eventInfo;
        }
        return null;
    }

    private AdxLog.AdxTrace.SSPInfo.Builder logSspInfo(UnifiedRequest uReq, SspEp sspEp, Impression imp) {
        AdxLog.AdxTrace.SSPInfo.Builder sspInfo = AdxLog.AdxTrace.SSPInfo.newBuilder();
        if (sspEp != null) {
            sspInfo.setSspId(sspEp.getSspId());
            sspInfo.setSspEpId(sspEp.getSspEpId());
        }
        // todo ssp_adx_id?
        AdxLog.AdxTrace.MediaInfo.Builder mediaInfo = logMediaInfo(uReq, imp);
        if (mediaInfo != null) {
            sspInfo.setMediaInfo(mediaInfo);
        }

        if (imp != null) {
            if (imp.getPmp() != null && CollUtil.isNotEmpty(imp.getPmp().getDeals())) {
                for (Deal deal : imp.getPmp().getDeals()) {
                    AdxLog.AdxTrace.SSPInfo.SSPPMPInfo.Builder sspPmpInfo = logSspPmpInfo(deal);
                    if (sspPmpInfo != null) {
                        sspInfo.addSspPmpInfo(sspPmpInfo);
                    }
                }
            }
        }

        return sspInfo;
    }

    private static AdxLog.AdxTrace.MediaInfo.Builder logMediaInfo(UnifiedRequest uReq, Impression imp) {
        if (uReq == null) {
            return null;
        }
        AdxLog.AdxTrace.MediaInfo.Builder mediaInfo = AdxLog.AdxTrace.MediaInfo.newBuilder();

        App app = uReq.getApp();
        if (app != null) {
            if (app.getInnerId() != null) {
                mediaInfo.setAppId(app.getInnerId());
            }
            if (app.getName() != null) {
                mediaInfo.setAppName(app.getName());
            }
            if (app.getVer() != null) {
                mediaInfo.setVersion(app.getVer());
            }
            if (app.getBundle() != null) {
                mediaInfo.setPkgName(app.getBundle());
            }
            if (app.getDomain() != null) {
                mediaInfo.setDomain(app.getBundle());
            }
            if (app.getAdxApp() != null) {
                mediaInfo.setAdxApp(app.getAdxApp());
            }
            if (app.getId() != null) {
                mediaInfo.setSspAppId(app.getId());
            }
        }
        // todo 流量类型
        Device device = uReq.getDevice();
        if (device != null) {
            if (device.getOs() != null) {
                switch (device.getOs()) {
                    case ANDROID:
                        mediaInfo.setPlatformType(LogConstants.PLATFORM_TYPE_ANDROID);
                        break;
                    case IOS:
                        mediaInfo.setPlatformType(LogConstants.PLATFORM_TYPE_IOS);
                        break;
                    default:
                        mediaInfo.setPlatformType(LogConstants.PLATFORM_TYPE_UNKNOW);
                        break;
                }
            }
        }
        if (imp != null) {
            if (imp.getTagId() != null) {
                mediaInfo.setSspAdunitId(imp.getTagId());
                mediaInfo.setTagId(imp.getTagId());
            }
            if (imp.getAdUnitId() != null) {
                mediaInfo.setAdunitId(imp.getAdUnitId());
            }
            if (imp.getAdUnitShowId() != null) {
                mediaInfo.setDspAdunitId(imp.getAdUnitShowId());
            }
            if (imp.getAdxTag() != null) {
                mediaInfo.setAdxTag(imp.getAdxTag());
            }
            mediaInfo.setAdunitForm(imp.getAdType().getValue());
            if (CollUtil.isNotEmpty(imp.getSubSlotType())) {
                for (Asset.AdType adType : imp.getSubSlotType()) {
                    mediaInfo.addSubAdunitForm(adType.getValue());
                }
            }
            if (imp.getInstl() != null) {
                if (imp.getInstl() == 1) {
                    mediaInfo.setIsInstl(AdxLog.AdxTrace.MediaInfo.Instl.INSTL);
                } else if (imp.getInstl() == 0) {
                    mediaInfo.setIsInstl(AdxLog.AdxTrace.MediaInfo.Instl.NOT_INSTL);
                }
            }

            // todo transaction_mode
            if (!imp.getAdUnitSize().isEmpty()) {
                mediaInfo.setAduintSize(imp.getAdUnitSize().get(0));
                for (String size : imp.getAdUnitSize()) {
                    mediaInfo.addAduintSizeList(size);
                }
            }

            if (imp.getDisplaymanager() != null) {
                mediaInfo.setDisplaymanager(imp.getDisplaymanager());
            }
        }
        if (uReq.getSource() != null && uReq.getSource().getExt() != null && uReq.getSource().getExt().getSchain() != null) {
            Source.SChain schain = uReq.getSource().getExt().getSchain();
            if (CollUtil.isNotEmpty(schain.getNodes())) {
                for (Source.Node node : schain.getNodes()) {
                    if (node.getAsi() != null) {
                        mediaInfo.addSChainList(node.getAsi());
                    }
                }
            }
        }

        return mediaInfo;
    }

    private AdxLog.AdxTrace.SSPInfo.SSPPMPInfo.Builder logSspPmpInfo(Deal deal) {
        if (deal == null) {
            return null;
        }
        AdxLog.AdxTrace.SSPInfo.SSPPMPInfo.Builder sspPmpInfo = AdxLog.AdxTrace.SSPInfo.SSPPMPInfo.newBuilder();
        if (deal.getId() != null) {
            sspPmpInfo.setSspDealId(deal.getId());
        }
        if (deal.getBidfloor() != null) {
            sspPmpInfo.setBidFloor(deal.getBidfloor());
        }
        return sspPmpInfo;
    }

    private AdxLog.AdxTrace.EpInfo.Builder logEpInfo(SessionContext sessionContext, DspEpObj dspEpObj, Impression imp) {
        AdxLog.AdxTrace.EpInfo.Builder epInfo = AdxLog.AdxTrace.EpInfo.newBuilder();
        epInfo.setDspId(dspEpObj.getDspId());
        epInfo.setEpId(dspEpObj.getDspEpId());
        if (dspEpObj.getTmax() != null) {
            epInfo.setEpReqTMax(dspEpObj.getTmax());
        }
        UnifiedResponse response = sessionContext.getDspUnifiedResp().get(dspEpObj.getDspEpId());
        Integer winner = sessionContext.getWinner();
        // todo pmp信息
        AdxLog.AdxTrace.EpInfo.EpResponse.Builder epRes = AdxLog.AdxTrace.EpInfo.EpResponse.newBuilder();
        if (response != null) {
	        Map<String, Bid> impIdToBidMap = response.getImpId2Bid();
	        if (!response.isFill()) {
                epRes.setRespStatus(AdxLog.AdxTrace.EpInfo.EpResponse.RespStatus.RESP_NO_FILL);
            } else if (response.isParseError()) {
                epRes.setRespStatus(AdxLog.AdxTrace.EpInfo.EpResponse.RespStatus.RESP_EXCEPTION);
            }else {
                Bid bid;
                if (CollUtil.isNotEmpty(impIdToBidMap) && impIdToBidMap.get(imp.getImpId()) != null) {
                    bid = impIdToBidMap.get(imp.getImpId());
                } else {
                    bid = response.getImp2FilteredBid().get(imp.getImpId());
                }
                if (bid != null) {
                    epRes.setRespStatus(AdxLog.AdxTrace.EpInfo.EpResponse.RespStatus.RESP_FILL);

                    AdxLog.AdxTrace.EpInfo.EpAdInfo.Builder epAdInfo = logEpAdInfo(bid);
                    if (epAdInfo != null) {
                        epInfo.setEpAdInfo(epAdInfo);
                    }
                    if (bid.getAdomain() != null && !bid.getAdomain().isEmpty()) {
                        for (String adomain : bid.getAdomain()) {
                            epRes.addAdomain(adomain);
                        }
                    }
                    if (Objects.equals(dspEpObj.getDspEpId(), winner)) {
                        epRes.setFilterReason(AdxLog.AdxTrace.EpInfo.EpResponse.FilterReason.R_SUCCESS);
                    } else {
                        if (bid.getFilterReason() != null) {
                            epRes.setFilterReason(AdxLog.AdxTrace.EpInfo.EpResponse.FilterReason.valueOf(bid.getFilterReason()));
                        }
                    }
                    epRes.setEpOfferPrice(bid.getOriginPrice() / Constants.COST_PER_MILLE);
                } else {
                    epRes.setRespStatus(AdxLog.AdxTrace.EpInfo.EpResponse.RespStatus.RESP_NO_FILL);
                }
            }
        } else {
            epRes.setRespStatus(AdxLog.AdxTrace.EpInfo.EpResponse.RespStatus.RESP_TIMEOUT);
        }
        epInfo.setEpResponse(epRes.build());

        if (imp != null) {
            Long price = imp.getDspFloorPrice().get(dspEpObj.getDspEpId());
            if (price != null) {
                epInfo.setEpFloorPrice(price / Constants.COST_PER_MILLE);
            }
        }
        // 融量
        if (MapUtil.isNotEmpty(sessionContext.getDspFusionTarget()) && sessionContext.getDspFusionTarget().containsKey(dspEpObj.getDspId())) {
            Map<String, FusionProvider.TargetTraffic> fusionMap = sessionContext.getDspFusionTarget().get(dspEpObj.getDspId());
            FusionProvider.TargetTraffic targetTraffic;
            if (MapUtil.isNotEmpty(fusionMap)) {
                epInfo.setIsFusion(1l);
                if (imp != null && fusionMap.containsKey(imp.getImpId())) {
                    targetTraffic = fusionMap.get(imp.getImpId());
                } else {
                    targetTraffic = fusionMap.values().iterator().next();
                }
                AdxLog.AdxTrace.FusionInfo.Builder fusionInfo = AdxLog.AdxTrace.FusionInfo.newBuilder();
                fusionInfo.setFusionType(targetTraffic.getType());
                if (StrUtil.isNotBlank(targetTraffic.getTarTagId())) {
                    fusionInfo.setFusionTagId(targetTraffic.getTarTagId());
                }
                if (StrUtil.isNotBlank(targetTraffic.getTarPkg())) {
                    fusionInfo.setFusionPkgName(targetTraffic.getTarPkg());
                }
                if (StrUtil.isNotBlank(targetTraffic.getTarAppId())) {
                    fusionInfo.setFusionAppId(targetTraffic.getTarAppId());
                }
                epInfo.setFusionInfo(fusionInfo);
            }
        }
        // todo 请求时间戳/响应时间戳
        return epInfo;
    }

    private AdxLog.AdxTrace.EpInfo.EpAdInfo.Builder logEpAdInfo(Bid bid) {
        if (bid == null) {
            return null;
        }
        AdxLog.AdxTrace.EpInfo.EpAdInfo.Builder epAdInfo = AdxLog.AdxTrace.EpInfo.EpAdInfo.newBuilder();
        if (bid.getCrid() != null) {
            epAdInfo.setEpCreativeId(bid.getCrid());
        }
        if (bid.getTemplateId() != null) {
            epAdInfo.setEpTemplateId(bid.getTemplateId());
        }
        Bid.Ext ext = bid.getExt();
        if (ext != null) {
            if (ext.deeplink != null) {
                epAdInfo.setEpDeeplink(ext.deeplink);
            }
            if (ext.fallback != null) {
                epAdInfo.setEpLandingUrl(ext.fallback);
            }
        }
        return epAdInfo;
    }

    private AdxLog.AdxTrace.AdInfo.Builder logAdInfo(UnifiedResponse winUResp, Impression imp) {
        if (winUResp == null) {
            return null;
        }
        Map<String, Bid> impIdToBidMap = winUResp.getImpId2Bid();
        if (!CollUtil.isNotEmpty(impIdToBidMap) || impIdToBidMap.get(imp.getImpId()) == null) {
            return null;
        }
        Bid bid = impIdToBidMap.get(imp.getImpId());
        AdxLog.AdxTrace.AdInfo.Builder adInfo = AdxLog.AdxTrace.AdInfo.newBuilder();
        if (bid.getSspHashId() != null) {
            adInfo.setSspHashId(bid.getSspHashId());
        }
        if (bid.getMaterialHashId() != null) {
            adInfo.setHashId(bid.getMaterialHashId());
        }
        if (bid.getMaterialId() != null) {
            adInfo.setMaterialId(bid.getMaterialId());
        }
        if (bid.getCrid() != null) {
            adInfo.setDspCreativeId(bid.getCrid());
        }
        if (bid.getAdid() != null) {
            adInfo.setSspAdId(bid.getAdid());
        }
        if (bid.getAdvertiserId() != null) {
            adInfo.setAdvertiserId(bid.getAdvertiserId());
        }
        if (bid.getTemplateId() != null) {
            adInfo.setTemplateId(bid.getTemplateId());
        }

        return adInfo;
    }

    private AdxLog.AdxTrace.RequestInfo.Builder logRequestInfo(UnifiedRequest uReq, Impression imp) {
        if (uReq == null) {
            return null;
        }
        AdxLog.AdxTrace.RequestInfo.Builder requestInfo = AdxLog.AdxTrace.RequestInfo.newBuilder();
        if (uReq.getMediaReqId() != null) {
            requestInfo.setReqId(uReq.getMediaReqId());
        }
        requestInfo.setInnerReqId(uReq.getInnerReqId());
        requestInfo.setReqTime(uReq.getReqTime() / 1000);
        Device device = uReq.getDevice();
        if (device != null) {
            if (device.getRegionInfo() != null && device.getRegionInfo().getRegion() != null) {
                requestInfo.setRegion(device.getRegionInfo().getRegion());
            }
            AdxLog.AdxTrace.RequestInfo.Device.Builder deviceInfo = logDevice(device);
            if (deviceInfo != null) {
                requestInfo.setDevice(deviceInfo);
            }
        }
        if (imp != null) {
            if (imp.getImpId() != null) {
                requestInfo.setImpId(imp.getImpId());
            }
            if (imp.getAdUnitId() != null) {
                requestInfo.setAdunitId(imp.getAdUnitId());
            }
        }
        if (uReq.getTmax() != null && uReq.getTmax() != Constants.T_MAX_DEFAULT) {
            requestInfo.setTMax(uReq.getTmax());
        }

        return requestInfo;
    }

    private AdxLog.AdxTrace.RequestInfo.Device.Builder logDevice(Device device) {
        if (device == null) {
            return null;
        }
        AdxLog.AdxTrace.RequestInfo.Device.Builder deviceInfo = AdxLog.AdxTrace.RequestInfo.Device.newBuilder();
        if (device.getUa() != null) {
            deviceInfo.setUa(device.getUa());
        }
        if (device.getIp() != null) {
            deviceInfo.setIp(device.getIp());
        }
        Geo geo = device.getGeo();
        AdxLog.AdxTrace.RequestInfo.Device.Geo.Builder geoInfo = AdxLog.AdxTrace.RequestInfo.Device.Geo.newBuilder();
        if (device.getRegionInfo() != null && device.getRegionInfo().getRegion() != null) {
            geoInfo.setRegionId(device.getRegionInfo().getRegion());
        }
        if (geo != null) {
            if (geo.getLat() != null) {
                geoInfo.setLat(geo.getLat());
            }
            if (geo.getLon() != null) {
                geoInfo.setLon(geo.getLon());
            }
        }
        deviceInfo.setGeo(geoInfo);
        if (device.getCarrier() != null) {
            switch (device.getCarrier()) {
                case UNKNOWN:
                    deviceInfo.setCarrier(AdxLog.AdxTrace.RequestInfo.Device.Carrier.CARRIER_UNKNOWN);
                    break;
                case MOBILE:
                    deviceInfo.setCarrier(AdxLog.AdxTrace.RequestInfo.Device.Carrier.CARRIER_CM);
                    break;
                case UNICOM:
                    deviceInfo.setCarrier(AdxLog.AdxTrace.RequestInfo.Device.Carrier.CARRIER_CU);
                    break;
                case TELECOM:
                    deviceInfo.setCarrier(AdxLog.AdxTrace.RequestInfo.Device.Carrier.CARRIER_CT);
                    break;
                default:
                    break;
            }
        }
        if (device.getMake() != null) {
            deviceInfo.setMake(device.getMake());
        }
        if (device.getModel() != null) {
            deviceInfo.setModel(device.getModel());
        }
        if (device.getOs() != null) {
            switch (device.getOs()) {
                case UNKNOWN:
                    deviceInfo.setOs(AdxLog.AdxTrace.RequestInfo.Device.Os.UNKNOWN);
                    break;
                case ANDROID:
                    deviceInfo.setOs(AdxLog.AdxTrace.RequestInfo.Device.Os.ANDROID);
                    break;
                case IOS:
                    deviceInfo.setOs(AdxLog.AdxTrace.RequestInfo.Device.Os.IOS);
                    break;
                case WINDOWS:
                    deviceInfo.setOs(AdxLog.AdxTrace.RequestInfo.Device.Os.WINDOWS);
                    break;
                default:
                    break;
            }
        }
        if (device.getOsv() != null) {
            deviceInfo.setOsv(device.getOsv());
        }
        if (device.getNetwork() != null) {
            switch (device.getNetwork()) {
                case UNKNOWN:
                    deviceInfo.setConnectiontype(AdxLog.AdxTrace.RequestInfo.Device.ConnectionType.CONNECTIONTYPE_UNKNOWN);
                    break;
                case M2G:
                    deviceInfo.setConnectiontype(AdxLog.AdxTrace.RequestInfo.Device.ConnectionType.CONNECTIONTYPE_CELL_2G);
                    break;
                case M3G:
                    deviceInfo.setConnectiontype(AdxLog.AdxTrace.RequestInfo.Device.ConnectionType.CONNECTIONTYPE_CELL_3G);
                    break;
                case M4G:
                    deviceInfo.setConnectiontype(AdxLog.AdxTrace.RequestInfo.Device.ConnectionType.CONNECTIONTYPE_CELL_4G);
                    break;
                case M5G:
                    deviceInfo.setConnectiontype(AdxLog.AdxTrace.RequestInfo.Device.ConnectionType.CONNECTIONTYPE_CELL_5G);
                    break;
                case WIFI:
                    deviceInfo.setConnectiontype(AdxLog.AdxTrace.RequestInfo.Device.ConnectionType.CONNECTIONTYPE_WIFI);
                    break;
                case ETHERNET:
                    deviceInfo.setConnectiontype(AdxLog.AdxTrace.RequestInfo.Device.ConnectionType.CONNECTIONTYPE_ETHERNET);
                    break;
                default:
                    break;
            }
        }
        if (device.getDeviceType() != null) {
            switch (device.getDeviceType()) {
                case UNKNOWN:
                    deviceInfo.setDeviceType(AdxLog.AdxTrace.RequestInfo.Device.DeviceType.DEVICETYPE_UNKNOW);
                    break;
                case PHONE:
                    deviceInfo.setDeviceType(AdxLog.AdxTrace.RequestInfo.Device.DeviceType.DEVICETYPE_PHONE);
                    break;
                case PAD:
                    deviceInfo.setDeviceType(AdxLog.AdxTrace.RequestInfo.Device.DeviceType.DEVICETYPE_PAD);
                    break;
                case TV:
                    deviceInfo.setDeviceType(AdxLog.AdxTrace.RequestInfo.Device.DeviceType.DEVICETYPE_TV);
                    break;
                default:
                    break;
            }
        }
        if (device.getH() != null) {
            deviceInfo.setH(device.getH());
        }
        if (device.getW() != null) {
            deviceInfo.setW(device.getW());
        }
        if (device.getPpi() != null) {
            deviceInfo.setPpi(device.getPpi());
        }
        AdxLog.AdxTrace.RequestInfo.Device.DeviceId.Builder deviceId = AdxLog.AdxTrace.RequestInfo.Device.DeviceId.newBuilder();

        if (device.getIfa() != null) {
            deviceId.setIdfa(device.getIfa());
        }
        if (device.getDidmd5() != null) {
            deviceId.setImeiMd5(device.getDidmd5());
        }
        if (device.getDidsha1() != null) {
            deviceId.setImeiSha1(device.getDidsha1());
        }
        if (device.getDpidmd5() != null) {
            deviceId.setAndroididMd5(device.getDpidmd5());
        }
        if (device.getDpidsha1() != null) {
            deviceId.setAndroididSha1(device.getDpidsha1());
        }
        if (device.getMacmd5() != null) {
            deviceId.setMacMd5(device.getMacmd5());
        }
        if (device.getMacsha1() != null) {
            deviceId.setMacSha1(device.getMacsha1());
        }
        deviceInfo.setDeviceId(deviceId);
        if (StrUtil.isNotBlank(device.getIpv4())) {
            deviceInfo.setIpv4(device.getIpv4());
        }
        if (StrUtil.isNotBlank(device.getIpv6())) {
            deviceInfo.setIpv6(device.getIpv6());
        }
        if (StrUtil.isNotBlank(device.getIp())) {
            deviceInfo.setReqIp(device.getIp());
        }
        return deviceInfo;
    }

    private AdxLog.AdxTrace.BidInfo.Builder logBidInfo(SessionContext sessionContext, Impression imp) {
        AdxLog.AdxTrace.BidInfo.Builder bidInfo = AdxLog.AdxTrace.BidInfo.newBuilder();
        bidInfo.setBidId(sessionContext.getSessionId());
        if (sessionContext.getWinner() != null && imp != null) {
            DspEpObj dspEpObj = sessionContext.getResponseDspEpObj().get(sessionContext.getWinner());
            if (sessionContext.getDspUnifiedResp().get(dspEpObj.getDspEpId()).getImpId2Bid().containsKey(imp.getImpId())) {
                bidInfo.setWinDspId(dspEpObj.getDspId());
                bidInfo.setDspWinSettlementType(dspEpObj.getSettlementType());
                Long dspFloorPrice = imp.getDspFloorPrice().get(dspEpObj.getDspEpId());
                if (dspFloorPrice != null) {
                    bidInfo.setDspFloorPrice(dspFloorPrice / Constants.COST_PER_MILLE);
                }
                bidInfo.setWinEpId(dspEpObj.getDspEpId());
            }
            // todo 内部价格？
        }
	    if (imp != null) {
            bidInfo.setImpId(imp.getImpId());
        }
        bidInfo.setSspSettlementType(sessionContext.getSspEp().getSettlementType());
        bidInfo.setIsDspInvalid(false);
        bidInfo.setIsSspInvalid(false);
        bidInfo.setBidTime(sessionContext.getStartTime() / 1000);
        // todo 出价
        UnifiedResponse unifiedResponse = sessionContext.getWinUResp();
        if (unifiedResponse != null ) {
            Map<String, Bid> impIdToBidMap = unifiedResponse.getImpId2Bid();
            if (CollUtil.isNotEmpty(impIdToBidMap) && impIdToBidMap.get(imp.getImpId()) != null) {
                Bid bid = impIdToBidMap.get(imp.getImpId());
                bidInfo.setDspPrice(bid.getOriginPrice() / Constants.COST_PER_MILLE);
                bidInfo.setDspWinPrice(bid.getWinPrice() / Constants.COST_PER_MILLE);
                String dealId = bid.getDealId();
                if (dealId != null) {
                    bidInfo.setDspWinDealId(dealId);
                }
                bidInfo.setIsBid(true);
            } else {
                bidInfo.setIsBid(false);
            }
        } else {
            bidInfo.setIsBid(false);
        }
        if (sessionContext.getBidDealId() != null) {
            bidInfo.setSspDealId(sessionContext.getBidDealId());
        }
        return bidInfo;
    }

    public static AdxLog.AdxTrace.ParamInfo.Builder genParamInfo(SessionContext sessionContext, DspEpObj dspEpObj, Impression imp) {
        UnifiedRequest req = sessionContext.getUnifiedRequest();
        UnifiedResponse resp = sessionContext.getWinUResp();
        AdxLog.AdxTrace.ParamInfo.Builder paramInfo = AdxLog.AdxTrace.ParamInfo.newBuilder();
        SspEp sspEp = sessionContext.getSspEp();
        if (sspEp != null) {
            paramInfo.setSspId(sspEp.getSspId());
            paramInfo.setSspTimeout(sspEp.getImpTtl());
            paramInfo.setSspEpId(sspEp.getSspEpId());
        }
        if (req != null) {
            App app = req.getApp();
            if (app != null) {
                if (app.getInnerId() != null) {
                    paramInfo.setAppId(app.getInnerId());
                }
                if (app.getId() != null) {
                    paramInfo.setSspAppId(app.getId());
                }
                if (app.getBundle() != null) {
                    paramInfo.setPkgName(app.getBundle());
                }
            }
            if (imp != null) {
                if (imp.getAdUnitId() != null) {
                    paramInfo.setAdunitId(imp.getAdUnitId());
                }
                paramInfo.setAdunitForm(imp.getAdType().getValue());
                paramInfo.setImpId(imp.getImpId());

                Long dspFloorPrice = imp.getDspFloorPrice().get(dspEpObj.getDspEpId());
                if (dspFloorPrice != null) {
                    paramInfo.setDspFloorPrice(dspFloorPrice / Constants.COST_PER_MILLE);
                }
            }
            // todo sspDealid
            paramInfo.setDspId(dspEpObj.getDspId());
            paramInfo.setDspTimeout(dspEpObj.getImpTtl());
            paramInfo.setEpId(dspEpObj.getDspEpId());
            paramInfo.setDspWinSettlementType(dspEpObj.getSettlementType());
            // todo dsp_deal_id

            paramInfo.setReqId(req.getMediaReqId());
            paramInfo.setInnerReqId(req.getInnerReqId());
            paramInfo.setReqTime(req.getReqTime() / 1000);

            Device device = req.getDevice();
            if (device != null) {
                if (device.getRegionInfo() != null && device.getRegionInfo().getRegion() != null) {
                    paramInfo.setRegion(device.getRegionInfo().getRegion());
                }
                if (device.getOs() != null) {
                    switch (device.getOs()) {
                        case UNKNOWN:
                            paramInfo.setOs(AdxLog.AdxTrace.RequestInfo.Device.Os.UNKNOWN.getNumber());
                            break;
                        case ANDROID:
                            paramInfo.setOs(AdxLog.AdxTrace.RequestInfo.Device.Os.ANDROID.getNumber());
                            break;
                        case IOS:
                            paramInfo.setOs(AdxLog.AdxTrace.RequestInfo.Device.Os.IOS.getNumber());
                            break;
                        case WINDOWS:
                            paramInfo.setOs(AdxLog.AdxTrace.RequestInfo.Device.Os.WINDOWS.getNumber());
                            break;
                        default:
                            break;
                    }
                }
            }
            AdxLog.AdxTrace.MediaInfo.Builder mediaInfo = logMediaInfo(req, imp);
            if (mediaInfo != null) {
                paramInfo.setMediaInfo(mediaInfo);
            }
            // 融量
            if (MapUtil.isNotEmpty(sessionContext.getDspFusionTarget()) && sessionContext.getDspFusionTarget().containsKey(dspEpObj.getDspId())) {
                Map<String, FusionProvider.TargetTraffic> fusionMap = sessionContext.getDspFusionTarget().get(dspEpObj.getDspId());
                FusionProvider.TargetTraffic targetTraffic;
                if (MapUtil.isNotEmpty(fusionMap)) {
                    if (imp != null && fusionMap.containsKey(imp.getImpId())) {
                        targetTraffic = fusionMap.get(imp.getImpId());
                        paramInfo.setWinEpIsFusion(1l);
                        AdxLog.AdxTrace.FusionInfo.Builder fusionInfo = AdxLog.AdxTrace.FusionInfo.newBuilder();
                        fusionInfo.setFusionType(targetTraffic.getType());
                        if (StrUtil.isNotBlank(targetTraffic.getTarTagId())) {
                            fusionInfo.setFusionTagId(targetTraffic.getTarTagId());
                        }
                        if (StrUtil.isNotBlank(targetTraffic.getTarPkg())) {
                            fusionInfo.setFusionPkgName(targetTraffic.getTarPkg());
                        }
                        if (StrUtil.isNotBlank(targetTraffic.getTarAppId())) {
                            fusionInfo.setFusionAppId(targetTraffic.getTarAppId());
                        }
                        paramInfo.setWinEpFusionInfo(fusionInfo);
                    }
                }
            }

        }
        if (resp != null ) {
            Map<String, Bid> impIdToBidMap = resp.getImpId2Bid();
            if (CollUtil.isNotEmpty(impIdToBidMap) && impIdToBidMap.get(imp.getImpId()) != null) {
                Bid bid = impIdToBidMap.get(imp.getImpId());
                if (bid.getMaterialId() != null) {
                    paramInfo.setMaterialId(bid.getMaterialId());
                }
                if (bid.getCrid() != null) {
                    paramInfo.setCreativeId(bid.getCrid());
                }
                if (bid.getAdvertiserId() != null) {
                    paramInfo.setAdvertiserId(bid.getAdvertiserId());
                }
                if (bid.getAdid() != null) {
                    paramInfo.setSspAdId(bid.getAdid());
                }
                paramInfo.setBidId(sessionContext.getSessionId());
                paramInfo.setDspWinPrice(bid.getWinPrice() / Constants.COST_PER_MILLE);
                if (bid.getAdomain() != null && !bid.getAdomain().isEmpty()) {
                    for (String adomain : bid.getAdomain()) {
                        paramInfo.addAdomain(adomain);
                    }
                }
            }

        }
        paramInfo.setBidTime(sessionContext.getStartTime() / 1000);
        return paramInfo;

    }

    private boolean needTraceLog(boolean isBid) {
        if (isBid) {
            return true;
        }
        // ratio, base 10000
        ThreadLocalRandom random = ThreadLocalRandom.current();
        int r = random.nextInt(10000);
        return r < traceLogRatio;
    }

    private boolean needFilebeatTraceLog(boolean isBid) {
        if (isBid) {
            return true;
        }
        // ratio, base 10000
        ThreadLocalRandom random = ThreadLocalRandom.current();
        int r = random.nextInt(10000);
        return r < filebeatTraceLogRatio;
    }

    // 以下为检索日志

    private boolean needRetrievalLog(boolean isBid) {
        if (isBid) {
            // ratio, base 10000
            ThreadLocalRandom random = ThreadLocalRandom.current();
            int r = random.nextInt(10000);
            return r < retrievalLogBidRatio;
        }
        // ratio, base 10000
        ThreadLocalRandom random = ThreadLocalRandom.current();
        int r = random.nextInt(10000);
        return r < retrievalLogNoBidRatio;
    }

    public void logRetrievalLog(SessionContext sessionContext){
        if (sessionContext == null || sessionContext.getUnifiedRequest() == null) {
            return;
        }
        if (!needRetrievalLog(sessionContext.getWinner()!=null)) {
            log.debug("don't need adx retrieval log.");
            return;
        }
        UnifiedRequest uReq = sessionContext.getUnifiedRequest();
        RetrievalLog.OriginLog.Builder originLogBuilder = RetrievalLog.OriginLog.newBuilder();
        originLogBuilder.setReqTime(uReq.getReqTime() / 1000);
        if (sessionContext.getReqBody() != null){
            originLogBuilder.setSspOriginReq(new String(sessionContext.getReqBody()));
        }
        App app = uReq.getApp();
        if (app != null){
            if (app.getBundle() != null) {
                originLogBuilder.setPkg(app.getBundle());
            }
        }
        originLogBuilder.setReqId(uReq.getMediaReqId());
        originLogBuilder.setBidId(sessionContext.getSessionId());
        if (sessionContext.getSspEp() != null){
            SspEp sspEp = sessionContext.getSspEp();
            if (sspEp != null) {
                originLogBuilder.setSspId(sspEp.getSspId());
                originLogBuilder.setSspEpId(sspEp.getSspEpId());
            }
        }
        //schain 信息
        if (uReq.getSource() != null && uReq.getSource().getExt() != null && uReq.getSource().getExt().getSchain() != null) {
            Source.SChain schain = uReq.getSource().getExt().getSchain();
            if (CollUtil.isNotEmpty(schain.getNodes())) {
                int layer = 0;
                for (Source.Node node : schain.getNodes()) {
                    layer++;
                    RetrievalLog.OriginLog.SchainNode.Builder schainNodeBuilder = RetrievalLog.OriginLog.SchainNode.newBuilder();
                    schainNodeBuilder.setLayer(layer);
                    schainNodeBuilder.setAsi(StringUtils.isNotEmpty(node.getAsi())?node.getAsi():"");
                    originLogBuilder.addSchainList(schainNodeBuilder);
                }
            }
        }
        //ImpCore核心内容
        Map<String, Impression> imps = uReq.getImps();
        for (String impId : imps.keySet()) {
            RetrievalLog.OriginLog.ImpCore.Builder impCoreBuilder = RetrievalLog.OriginLog.ImpCore.newBuilder();
            Impression imp = imps.get(impId);
            if (imp.getTagId() != null) {
                impCoreBuilder.setTagId(imp.getTagId());
            }
            if (imp.getAdUnitId() != null) {
                impCoreBuilder.setAdunitId(imp.getAdUnitId());
            }
            if (imp.getAdxTag() != null) {
                impCoreBuilder.setAdxTag(imp.getAdxTag());
            }
            if (app != null && app.getAdxApp() != null) {
                impCoreBuilder.setAdxApp(app.getAdxApp());
            }
            originLogBuilder.addImpCoreList(impCoreBuilder);
        }
        if (CollUtil.isNotEmpty(sessionContext.getRequestSucDspEp())) {
            Map<Integer, byte[]> dspReqBody = sessionContext.getDspReqBody();
            Map<Integer, byte[]> dspRespBody = sessionContext.getDspRespBody();
            for (Map.Entry<Integer, DspEpObj> entry : sessionContext.getRequestSucDspEp().entrySet()) {
                DspEpObj dspEpObj = entry.getValue();
                UnifiedResponse response = sessionContext.getDspUnifiedResp().get(dspEpObj.getDspEpId());
                RetrievalLog.OriginLog.EpSession.Builder epSessionBuilder = RetrievalLog.OriginLog.EpSession.newBuilder();
                epSessionBuilder.setDspId(dspEpObj.getDspId() != null? dspEpObj.getDspId() : 0);
                epSessionBuilder.setDspEpId(dspEpObj.getDspEpId() != null? dspEpObj.getDspEpId() : 0);
                if (response != null) {
                    if (!response.isFill()) {
                        epSessionBuilder.setRespStatus(RetrievalLog.OriginLog.EpSession.RespStatus.RESP_NO_FILL);
                    } else if (response.isParseError()) {
                        epSessionBuilder.setRespStatus(RetrievalLog.OriginLog.EpSession.RespStatus.RESP_EXCEPTION);
                    }else {
                        epSessionBuilder.setRespStatus(RetrievalLog.OriginLog.EpSession.RespStatus.RESP_FILL);
                    }
                } else {
                    epSessionBuilder.setRespStatus(RetrievalLog.OriginLog.EpSession.RespStatus.RESP_TIMEOUT);
                }
                if (dspReqBody != null && dspReqBody.get(dspEpObj.getDspEpId()) != null){
                    epSessionBuilder.setEpOriginReq(new String(dspReqBody.get(dspEpObj.getDspEpId())));
                }
                if (dspRespBody != null && dspRespBody.get(dspEpObj.getDspEpId()) != null){
                    epSessionBuilder.setEpOriginResp(new String(dspRespBody.get(dspEpObj.getDspEpId())));
                }
                originLogBuilder.addEpSessionList(epSessionBuilder);
            }
        }
        DspEpObj winDspEp = sessionContext.getResponseDspEpObj().get(sessionContext.getWinner());
        if (winDspEp != null){
            originLogBuilder.setWinEpId(winDspEp.getDspEpId());
        }
        Device device = uReq.getDevice();
        if (device != null){
            if (device.getOs() == Device.Os.ANDROID && device.getIfa() != null){
                originLogBuilder.setGaid(device.getIfa());
            }
            if (device.getOs() == Device.Os.IOS && device.getIfa() != null){
                originLogBuilder.setIdfa(device.getIfa());
            }
        }
        if (sessionContext.getRespBody() != null){
            originLogBuilder.setSspOriginResp(new String( sessionContext.getRespBody()));
        }
        String msg = Base64.encode(originLogBuilder.build().toByteArray());
        retrievalLog.info("{} {}", sessionContext.getStartTime4Log(), msg);
    }
}
