// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: retrieval_log.proto

package com.iflytek.traffic.log.urtb;

public final class RetrievalLog {
  private RetrievalLog() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface OriginLogOrBuilder extends
      // @@protoc_insertion_point(interface_extends:urtb.OriginLog)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 请求核心内容
     * 原始请求
     * </pre>
     *
     * <code>optional string ssp_origin_req = 1;</code>
     */
    boolean hasSspOriginReq();
    /**
     * <pre>
     * 请求核心内容
     * 原始请求
     * </pre>
     *
     * <code>optional string ssp_origin_req = 1;</code>
     */
    java.lang.String getSspOriginReq();
    /**
     * <pre>
     * 请求核心内容
     * 原始请求
     * </pre>
     *
     * <code>optional string ssp_origin_req = 1;</code>
     */
    com.google.protobuf.ByteString
        getSspOriginReqBytes();

    /**
     * <pre>
     * 原始请求时间
     * </pre>
     *
     * <code>optional int64 req_time = 2;</code>
     */
    boolean hasReqTime();
    /**
     * <pre>
     * 原始请求时间
     * </pre>
     *
     * <code>optional int64 req_time = 2;</code>
     */
    long getReqTime();

    /**
     * <pre>
     * 包名
     * </pre>
     *
     * <code>optional string pkg = 3;</code>
     */
    boolean hasPkg();
    /**
     * <pre>
     * 包名
     * </pre>
     *
     * <code>optional string pkg = 3;</code>
     */
    java.lang.String getPkg();
    /**
     * <pre>
     * 包名
     * </pre>
     *
     * <code>optional string pkg = 3;</code>
     */
    com.google.protobuf.ByteString
        getPkgBytes();

    /**
     * <pre>
     * 请求id
     * </pre>
     *
     * <code>optional string req_id = 4;</code>
     */
    boolean hasReqId();
    /**
     * <pre>
     * 请求id
     * </pre>
     *
     * <code>optional string req_id = 4;</code>
     */
    java.lang.String getReqId();
    /**
     * <pre>
     * 请求id
     * </pre>
     *
     * <code>optional string req_id = 4;</code>
     */
    com.google.protobuf.ByteString
        getReqIdBytes();

    /**
     * <pre>
     * 竞价id
     * </pre>
     *
     * <code>optional string bid_id = 5;</code>
     */
    boolean hasBidId();
    /**
     * <pre>
     * 竞价id
     * </pre>
     *
     * <code>optional string bid_id = 5;</code>
     */
    java.lang.String getBidId();
    /**
     * <pre>
     * 竞价id
     * </pre>
     *
     * <code>optional string bid_id = 5;</code>
     */
    com.google.protobuf.ByteString
        getBidIdBytes();

    /**
     * <pre>
     * ssp_id
     * </pre>
     *
     * <code>optional int32 ssp_id = 6;</code>
     */
    boolean hasSspId();
    /**
     * <pre>
     * ssp_id
     * </pre>
     *
     * <code>optional int32 ssp_id = 6;</code>
     */
    int getSspId();

    /**
     * <pre>
     * ssp_ep_id
     * </pre>
     *
     * <code>optional int32 ssp_ep_id = 7;</code>
     */
    boolean hasSspEpId();
    /**
     * <pre>
     * ssp_ep_id
     * </pre>
     *
     * <code>optional int32 ssp_ep_id = 7;</code>
     */
    int getSspEpId();

    /**
     * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
     */
    java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode> 
        getSchainListList();
    /**
     * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
     */
    com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode getSchainList(int index);
    /**
     * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
     */
    int getSchainListCount();
    /**
     * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
     */
    java.util.List<? extends com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNodeOrBuilder> 
        getSchainListOrBuilderList();
    /**
     * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
     */
    com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNodeOrBuilder getSchainListOrBuilder(
        int index);

    /**
     * <pre>
     * imp list
     * </pre>
     *
     * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
     */
    java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore> 
        getImpCoreListList();
    /**
     * <pre>
     * imp list
     * </pre>
     *
     * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
     */
    com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore getImpCoreList(int index);
    /**
     * <pre>
     * imp list
     * </pre>
     *
     * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
     */
    int getImpCoreListCount();
    /**
     * <pre>
     * imp list
     * </pre>
     *
     * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
     */
    java.util.List<? extends com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCoreOrBuilder> 
        getImpCoreListOrBuilderList();
    /**
     * <pre>
     * imp list
     * </pre>
     *
     * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
     */
    com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCoreOrBuilder getImpCoreListOrBuilder(
        int index);

    /**
     * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
     */
    java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession> 
        getEpSessionListList();
    /**
     * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
     */
    com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession getEpSessionList(int index);
    /**
     * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
     */
    int getEpSessionListCount();
    /**
     * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
     */
    java.util.List<? extends com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSessionOrBuilder> 
        getEpSessionListOrBuilderList();
    /**
     * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
     */
    com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSessionOrBuilder getEpSessionListOrBuilder(
        int index);

    /**
     * <pre>
     *竞价核心内容
     *获胜ep_id
     * </pre>
     *
     * <code>optional int32 win_ep_id = 11;</code>
     */
    boolean hasWinEpId();
    /**
     * <pre>
     *竞价核心内容
     *获胜ep_id
     * </pre>
     *
     * <code>optional int32 win_ep_id = 11;</code>
     */
    int getWinEpId();

    /**
     * <pre>
     *设备信息
     * </pre>
     *
     * <code>optional string gaid = 12;</code>
     */
    boolean hasGaid();
    /**
     * <pre>
     *设备信息
     * </pre>
     *
     * <code>optional string gaid = 12;</code>
     */
    java.lang.String getGaid();
    /**
     * <pre>
     *设备信息
     * </pre>
     *
     * <code>optional string gaid = 12;</code>
     */
    com.google.protobuf.ByteString
        getGaidBytes();

    /**
     * <code>optional string idfa = 13;</code>
     */
    boolean hasIdfa();
    /**
     * <code>optional string idfa = 13;</code>
     */
    java.lang.String getIdfa();
    /**
     * <code>optional string idfa = 13;</code>
     */
    com.google.protobuf.ByteString
        getIdfaBytes();

    /**
     * <pre>
     *下发给ssp响应
     * </pre>
     *
     * <code>optional string ssp_origin_resp = 14;</code>
     */
    boolean hasSspOriginResp();
    /**
     * <pre>
     *下发给ssp响应
     * </pre>
     *
     * <code>optional string ssp_origin_resp = 14;</code>
     */
    java.lang.String getSspOriginResp();
    /**
     * <pre>
     *下发给ssp响应
     * </pre>
     *
     * <code>optional string ssp_origin_resp = 14;</code>
     */
    com.google.protobuf.ByteString
        getSspOriginRespBytes();
  }
  /**
   * Protobuf type {@code urtb.OriginLog}
   */
  public  static final class OriginLog extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:urtb.OriginLog)
      OriginLogOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OriginLog.newBuilder() to construct.
    private OriginLog(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OriginLog() {
      sspOriginReq_ = "";
      pkg_ = "";
      reqId_ = "";
      bidId_ = "";
      schainList_ = java.util.Collections.emptyList();
      impCoreList_ = java.util.Collections.emptyList();
      epSessionList_ = java.util.Collections.emptyList();
      gaid_ = "";
      idfa_ = "";
      sspOriginResp_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OriginLog();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OriginLog(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              sspOriginReq_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              reqTime_ = input.readInt64();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              pkg_ = bs;
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              reqId_ = bs;
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              bidId_ = bs;
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              sspId_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              sspEpId_ = input.readInt32();
              break;
            }
            case 66: {
              if (!((mutable_bitField0_ & 0x00000080) != 0)) {
                schainList_ = new java.util.ArrayList<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode>();
                mutable_bitField0_ |= 0x00000080;
              }
              schainList_.add(
                  input.readMessage(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.PARSER, extensionRegistry));
              break;
            }
            case 74: {
              if (!((mutable_bitField0_ & 0x00000100) != 0)) {
                impCoreList_ = new java.util.ArrayList<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore>();
                mutable_bitField0_ |= 0x00000100;
              }
              impCoreList_.add(
                  input.readMessage(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.PARSER, extensionRegistry));
              break;
            }
            case 82: {
              if (!((mutable_bitField0_ & 0x00000200) != 0)) {
                epSessionList_ = new java.util.ArrayList<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession>();
                mutable_bitField0_ |= 0x00000200;
              }
              epSessionList_.add(
                  input.readMessage(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.PARSER, extensionRegistry));
              break;
            }
            case 88: {
              bitField0_ |= 0x00000080;
              winEpId_ = input.readInt32();
              break;
            }
            case 98: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000100;
              gaid_ = bs;
              break;
            }
            case 106: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000200;
              idfa_ = bs;
              break;
            }
            case 114: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000400;
              sspOriginResp_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000080) != 0)) {
          schainList_ = java.util.Collections.unmodifiableList(schainList_);
        }
        if (((mutable_bitField0_ & 0x00000100) != 0)) {
          impCoreList_ = java.util.Collections.unmodifiableList(impCoreList_);
        }
        if (((mutable_bitField0_ & 0x00000200) != 0)) {
          epSessionList_ = java.util.Collections.unmodifiableList(epSessionList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.class, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.Builder.class);
    }

    public interface SchainNodeOrBuilder extends
        // @@protoc_insertion_point(interface_extends:urtb.OriginLog.SchainNode)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>optional int32 layer = 1;</code>
       */
      boolean hasLayer();
      /**
       * <code>optional int32 layer = 1;</code>
       */
      int getLayer();

      /**
       * <code>optional string asi = 2;</code>
       */
      boolean hasAsi();
      /**
       * <code>optional string asi = 2;</code>
       */
      java.lang.String getAsi();
      /**
       * <code>optional string asi = 2;</code>
       */
      com.google.protobuf.ByteString
          getAsiBytes();
    }
    /**
     * Protobuf type {@code urtb.OriginLog.SchainNode}
     */
    public  static final class SchainNode extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:urtb.OriginLog.SchainNode)
        SchainNodeOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use SchainNode.newBuilder() to construct.
      private SchainNode(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private SchainNode() {
        asi_ = "";
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new SchainNode();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      private SchainNode(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        this();
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        int mutable_bitField0_ = 0;
        com.google.protobuf.UnknownFieldSet.Builder unknownFields =
            com.google.protobuf.UnknownFieldSet.newBuilder();
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                bitField0_ |= 0x00000001;
                layer_ = input.readInt32();
                break;
              }
              case 18: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00000002;
                asi_ = bs;
                break;
              }
              default: {
                if (!parseUnknownField(
                    input, unknownFields, extensionRegistry, tag)) {
                  done = true;
                }
                break;
              }
            }
          }
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(this);
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e).setUnfinishedMessage(this);
        } finally {
          this.unknownFields = unknownFields.build();
          makeExtensionsImmutable();
        }
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_SchainNode_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_SchainNode_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.class, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.Builder.class);
      }

      private int bitField0_;
      public static final int LAYER_FIELD_NUMBER = 1;
      private int layer_;
      /**
       * <code>optional int32 layer = 1;</code>
       */
      public boolean hasLayer() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 layer = 1;</code>
       */
      public int getLayer() {
        return layer_;
      }

      public static final int ASI_FIELD_NUMBER = 2;
      private volatile java.lang.Object asi_;
      /**
       * <code>optional string asi = 2;</code>
       */
      public boolean hasAsi() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string asi = 2;</code>
       */
      public java.lang.String getAsi() {
        java.lang.Object ref = asi_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            asi_ = s;
          }
          return s;
        }
      }
      /**
       * <code>optional string asi = 2;</code>
       */
      public com.google.protobuf.ByteString
          getAsiBytes() {
        java.lang.Object ref = asi_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          asi_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeInt32(1, layer_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 2, asi_);
        }
        unknownFields.writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(1, layer_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, asi_);
        }
        size += unknownFields.getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode)) {
          return super.equals(obj);
        }
        com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode other = (com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode) obj;

        if (hasLayer() != other.hasLayer()) return false;
        if (hasLayer()) {
          if (getLayer()
              != other.getLayer()) return false;
        }
        if (hasAsi() != other.hasAsi()) return false;
        if (hasAsi()) {
          if (!getAsi()
              .equals(other.getAsi())) return false;
        }
        if (!unknownFields.equals(other.unknownFields)) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasLayer()) {
          hash = (37 * hash) + LAYER_FIELD_NUMBER;
          hash = (53 * hash) + getLayer();
        }
        if (hasAsi()) {
          hash = (37 * hash) + ASI_FIELD_NUMBER;
          hash = (53 * hash) + getAsi().hashCode();
        }
        hash = (29 * hash) + unknownFields.hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code urtb.OriginLog.SchainNode}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:urtb.OriginLog.SchainNode)
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNodeOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_SchainNode_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_SchainNode_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.class, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.Builder.class);
        }

        // Construct using com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessageV3
                  .alwaysUseFieldBuilders) {
          }
        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          layer_ = 0;
          bitField0_ = (bitField0_ & ~0x00000001);
          asi_ = "";
          bitField0_ = (bitField0_ & ~0x00000002);
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_SchainNode_descriptor;
        }

        @java.lang.Override
        public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode getDefaultInstanceForType() {
          return com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.getDefaultInstance();
        }

        @java.lang.Override
        public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode build() {
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode buildPartial() {
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode result = new com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode(this);
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.layer_ = layer_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            to_bitField0_ |= 0x00000002;
          }
          result.asi_ = asi_;
          result.bitField0_ = to_bitField0_;
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode) {
            return mergeFrom((com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode other) {
          if (other == com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.getDefaultInstance()) return this;
          if (other.hasLayer()) {
            setLayer(other.getLayer());
          }
          if (other.hasAsi()) {
            bitField0_ |= 0x00000002;
            asi_ = other.asi_;
            onChanged();
          }
          this.mergeUnknownFields(other.unknownFields);
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode parsedMessage = null;
          try {
            parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            parsedMessage = (com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode) e.getUnfinishedMessage();
            throw e.unwrapIOException();
          } finally {
            if (parsedMessage != null) {
              mergeFrom(parsedMessage);
            }
          }
          return this;
        }
        private int bitField0_;

        private int layer_ ;
        /**
         * <code>optional int32 layer = 1;</code>
         */
        public boolean hasLayer() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <code>optional int32 layer = 1;</code>
         */
        public int getLayer() {
          return layer_;
        }
        /**
         * <code>optional int32 layer = 1;</code>
         */
        public Builder setLayer(int value) {
          bitField0_ |= 0x00000001;
          layer_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional int32 layer = 1;</code>
         */
        public Builder clearLayer() {
          bitField0_ = (bitField0_ & ~0x00000001);
          layer_ = 0;
          onChanged();
          return this;
        }

        private java.lang.Object asi_ = "";
        /**
         * <code>optional string asi = 2;</code>
         */
        public boolean hasAsi() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <code>optional string asi = 2;</code>
         */
        public java.lang.String getAsi() {
          java.lang.Object ref = asi_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              asi_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>optional string asi = 2;</code>
         */
        public com.google.protobuf.ByteString
            getAsiBytes() {
          java.lang.Object ref = asi_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            asi_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>optional string asi = 2;</code>
         */
        public Builder setAsi(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
          asi_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional string asi = 2;</code>
         */
        public Builder clearAsi() {
          bitField0_ = (bitField0_ & ~0x00000002);
          asi_ = getDefaultInstance().getAsi();
          onChanged();
          return this;
        }
        /**
         * <code>optional string asi = 2;</code>
         */
        public Builder setAsiBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
          asi_ = value;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:urtb.OriginLog.SchainNode)
      }

      // @@protoc_insertion_point(class_scope:urtb.OriginLog.SchainNode)
      private static final com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode();
      }

      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      @java.lang.Deprecated public static final com.google.protobuf.Parser<SchainNode>
          PARSER = new com.google.protobuf.AbstractParser<SchainNode>() {
        @java.lang.Override
        public SchainNode parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return new SchainNode(input, extensionRegistry);
        }
      };

      public static com.google.protobuf.Parser<SchainNode> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<SchainNode> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface ImpCoreOrBuilder extends
        // @@protoc_insertion_point(interface_extends:urtb.OriginLog.ImpCore)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       * 外部广告位id
       * </pre>
       *
       * <code>optional string tag_id = 1;</code>
       */
      boolean hasTagId();
      /**
       * <pre>
       * 外部广告位id
       * </pre>
       *
       * <code>optional string tag_id = 1;</code>
       */
      java.lang.String getTagId();
      /**
       * <pre>
       * 外部广告位id
       * </pre>
       *
       * <code>optional string tag_id = 1;</code>
       */
      com.google.protobuf.ByteString
          getTagIdBytes();

      /**
       * <pre>
       * 内部广告位id
       * </pre>
       *
       * <code>optional int32 adunit_id = 2;</code>
       */
      boolean hasAdunitId();
      /**
       * <pre>
       * 内部广告位id
       * </pre>
       *
       * <code>optional int32 adunit_id = 2;</code>
       */
      int getAdunitId();

      /**
       * <pre>
       *广告位形式 1横幅、2开屏
       * </pre>
       *
       * <code>optional int32 adunit_form = 3;</code>
       */
      boolean hasAdunitForm();
      /**
       * <pre>
       *广告位形式 1横幅、2开屏
       * </pre>
       *
       * <code>optional int32 adunit_form = 3;</code>
       */
      int getAdunitForm();

      /**
       * <pre>
       *应用adx_app（生成规则：ssp id 和 ssp 包名/budbule 进行hash md5。SSP_ID， APP_TYPE， md5(16位)，用点分隔 ）
       * </pre>
       *
       * <code>optional string adx_app = 4;</code>
       */
      boolean hasAdxApp();
      /**
       * <pre>
       *应用adx_app（生成规则：ssp id 和 ssp 包名/budbule 进行hash md5。SSP_ID， APP_TYPE， md5(16位)，用点分隔 ）
       * </pre>
       *
       * <code>optional string adx_app = 4;</code>
       */
      java.lang.String getAdxApp();
      /**
       * <pre>
       *应用adx_app（生成规则：ssp id 和 ssp 包名/budbule 进行hash md5。SSP_ID， APP_TYPE， md5(16位)，用点分隔 ）
       * </pre>
       *
       * <code>optional string adx_app = 4;</code>
       */
      com.google.protobuf.ByteString
          getAdxAppBytes();

      /**
       * <pre>
       *广告位 adx_tag（生成规则：ssp  id 和 ssp tag 进行hash md5。 SSP ID，SLOT TYPE，md5(16)，用点分隔）
       * </pre>
       *
       * <code>optional string adx_tag = 5;</code>
       */
      boolean hasAdxTag();
      /**
       * <pre>
       *广告位 adx_tag（生成规则：ssp  id 和 ssp tag 进行hash md5。 SSP ID，SLOT TYPE，md5(16)，用点分隔）
       * </pre>
       *
       * <code>optional string adx_tag = 5;</code>
       */
      java.lang.String getAdxTag();
      /**
       * <pre>
       *广告位 adx_tag（生成规则：ssp  id 和 ssp tag 进行hash md5。 SSP ID，SLOT TYPE，md5(16)，用点分隔）
       * </pre>
       *
       * <code>optional string adx_tag = 5;</code>
       */
      com.google.protobuf.ByteString
          getAdxTagBytes();
    }
    /**
     * <pre>
     * imp核心内容
     * </pre>
     *
     * Protobuf type {@code urtb.OriginLog.ImpCore}
     */
    public  static final class ImpCore extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:urtb.OriginLog.ImpCore)
        ImpCoreOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use ImpCore.newBuilder() to construct.
      private ImpCore(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private ImpCore() {
        tagId_ = "";
        adxApp_ = "";
        adxTag_ = "";
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new ImpCore();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      private ImpCore(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        this();
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        int mutable_bitField0_ = 0;
        com.google.protobuf.UnknownFieldSet.Builder unknownFields =
            com.google.protobuf.UnknownFieldSet.newBuilder();
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00000001;
                tagId_ = bs;
                break;
              }
              case 16: {
                bitField0_ |= 0x00000002;
                adunitId_ = input.readInt32();
                break;
              }
              case 24: {
                bitField0_ |= 0x00000004;
                adunitForm_ = input.readInt32();
                break;
              }
              case 34: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00000008;
                adxApp_ = bs;
                break;
              }
              case 42: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00000010;
                adxTag_ = bs;
                break;
              }
              default: {
                if (!parseUnknownField(
                    input, unknownFields, extensionRegistry, tag)) {
                  done = true;
                }
                break;
              }
            }
          }
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(this);
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e).setUnfinishedMessage(this);
        } finally {
          this.unknownFields = unknownFields.build();
          makeExtensionsImmutable();
        }
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_ImpCore_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_ImpCore_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.class, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.Builder.class);
      }

      private int bitField0_;
      public static final int TAG_ID_FIELD_NUMBER = 1;
      private volatile java.lang.Object tagId_;
      /**
       * <pre>
       * 外部广告位id
       * </pre>
       *
       * <code>optional string tag_id = 1;</code>
       */
      public boolean hasTagId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 外部广告位id
       * </pre>
       *
       * <code>optional string tag_id = 1;</code>
       */
      public java.lang.String getTagId() {
        java.lang.Object ref = tagId_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            tagId_ = s;
          }
          return s;
        }
      }
      /**
       * <pre>
       * 外部广告位id
       * </pre>
       *
       * <code>optional string tag_id = 1;</code>
       */
      public com.google.protobuf.ByteString
          getTagIdBytes() {
        java.lang.Object ref = tagId_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          tagId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int ADUNIT_ID_FIELD_NUMBER = 2;
      private int adunitId_;
      /**
       * <pre>
       * 内部广告位id
       * </pre>
       *
       * <code>optional int32 adunit_id = 2;</code>
       */
      public boolean hasAdunitId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 内部广告位id
       * </pre>
       *
       * <code>optional int32 adunit_id = 2;</code>
       */
      public int getAdunitId() {
        return adunitId_;
      }

      public static final int ADUNIT_FORM_FIELD_NUMBER = 3;
      private int adunitForm_;
      /**
       * <pre>
       *广告位形式 1横幅、2开屏
       * </pre>
       *
       * <code>optional int32 adunit_form = 3;</code>
       */
      public boolean hasAdunitForm() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       *广告位形式 1横幅、2开屏
       * </pre>
       *
       * <code>optional int32 adunit_form = 3;</code>
       */
      public int getAdunitForm() {
        return adunitForm_;
      }

      public static final int ADX_APP_FIELD_NUMBER = 4;
      private volatile java.lang.Object adxApp_;
      /**
       * <pre>
       *应用adx_app（生成规则：ssp id 和 ssp 包名/budbule 进行hash md5。SSP_ID， APP_TYPE， md5(16位)，用点分隔 ）
       * </pre>
       *
       * <code>optional string adx_app = 4;</code>
       */
      public boolean hasAdxApp() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       *应用adx_app（生成规则：ssp id 和 ssp 包名/budbule 进行hash md5。SSP_ID， APP_TYPE， md5(16位)，用点分隔 ）
       * </pre>
       *
       * <code>optional string adx_app = 4;</code>
       */
      public java.lang.String getAdxApp() {
        java.lang.Object ref = adxApp_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            adxApp_ = s;
          }
          return s;
        }
      }
      /**
       * <pre>
       *应用adx_app（生成规则：ssp id 和 ssp 包名/budbule 进行hash md5。SSP_ID， APP_TYPE， md5(16位)，用点分隔 ）
       * </pre>
       *
       * <code>optional string adx_app = 4;</code>
       */
      public com.google.protobuf.ByteString
          getAdxAppBytes() {
        java.lang.Object ref = adxApp_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          adxApp_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int ADX_TAG_FIELD_NUMBER = 5;
      private volatile java.lang.Object adxTag_;
      /**
       * <pre>
       *广告位 adx_tag（生成规则：ssp  id 和 ssp tag 进行hash md5。 SSP ID，SLOT TYPE，md5(16)，用点分隔）
       * </pre>
       *
       * <code>optional string adx_tag = 5;</code>
       */
      public boolean hasAdxTag() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       *广告位 adx_tag（生成规则：ssp  id 和 ssp tag 进行hash md5。 SSP ID，SLOT TYPE，md5(16)，用点分隔）
       * </pre>
       *
       * <code>optional string adx_tag = 5;</code>
       */
      public java.lang.String getAdxTag() {
        java.lang.Object ref = adxTag_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            adxTag_ = s;
          }
          return s;
        }
      }
      /**
       * <pre>
       *广告位 adx_tag（生成规则：ssp  id 和 ssp tag 进行hash md5。 SSP ID，SLOT TYPE，md5(16)，用点分隔）
       * </pre>
       *
       * <code>optional string adx_tag = 5;</code>
       */
      public com.google.protobuf.ByteString
          getAdxTagBytes() {
        java.lang.Object ref = adxTag_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          adxTag_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 1, tagId_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeInt32(2, adunitId_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          output.writeInt32(3, adunitForm_);
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 4, adxApp_);
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 5, adxTag_);
        }
        unknownFields.writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, tagId_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(2, adunitId_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(3, adunitForm_);
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, adxApp_);
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, adxTag_);
        }
        size += unknownFields.getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore)) {
          return super.equals(obj);
        }
        com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore other = (com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore) obj;

        if (hasTagId() != other.hasTagId()) return false;
        if (hasTagId()) {
          if (!getTagId()
              .equals(other.getTagId())) return false;
        }
        if (hasAdunitId() != other.hasAdunitId()) return false;
        if (hasAdunitId()) {
          if (getAdunitId()
              != other.getAdunitId()) return false;
        }
        if (hasAdunitForm() != other.hasAdunitForm()) return false;
        if (hasAdunitForm()) {
          if (getAdunitForm()
              != other.getAdunitForm()) return false;
        }
        if (hasAdxApp() != other.hasAdxApp()) return false;
        if (hasAdxApp()) {
          if (!getAdxApp()
              .equals(other.getAdxApp())) return false;
        }
        if (hasAdxTag() != other.hasAdxTag()) return false;
        if (hasAdxTag()) {
          if (!getAdxTag()
              .equals(other.getAdxTag())) return false;
        }
        if (!unknownFields.equals(other.unknownFields)) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasTagId()) {
          hash = (37 * hash) + TAG_ID_FIELD_NUMBER;
          hash = (53 * hash) + getTagId().hashCode();
        }
        if (hasAdunitId()) {
          hash = (37 * hash) + ADUNIT_ID_FIELD_NUMBER;
          hash = (53 * hash) + getAdunitId();
        }
        if (hasAdunitForm()) {
          hash = (37 * hash) + ADUNIT_FORM_FIELD_NUMBER;
          hash = (53 * hash) + getAdunitForm();
        }
        if (hasAdxApp()) {
          hash = (37 * hash) + ADX_APP_FIELD_NUMBER;
          hash = (53 * hash) + getAdxApp().hashCode();
        }
        if (hasAdxTag()) {
          hash = (37 * hash) + ADX_TAG_FIELD_NUMBER;
          hash = (53 * hash) + getAdxTag().hashCode();
        }
        hash = (29 * hash) + unknownFields.hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * imp核心内容
       * </pre>
       *
       * Protobuf type {@code urtb.OriginLog.ImpCore}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:urtb.OriginLog.ImpCore)
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCoreOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_ImpCore_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_ImpCore_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.class, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.Builder.class);
        }

        // Construct using com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessageV3
                  .alwaysUseFieldBuilders) {
          }
        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          tagId_ = "";
          bitField0_ = (bitField0_ & ~0x00000001);
          adunitId_ = 0;
          bitField0_ = (bitField0_ & ~0x00000002);
          adunitForm_ = 0;
          bitField0_ = (bitField0_ & ~0x00000004);
          adxApp_ = "";
          bitField0_ = (bitField0_ & ~0x00000008);
          adxTag_ = "";
          bitField0_ = (bitField0_ & ~0x00000010);
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_ImpCore_descriptor;
        }

        @java.lang.Override
        public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore getDefaultInstanceForType() {
          return com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.getDefaultInstance();
        }

        @java.lang.Override
        public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore build() {
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore buildPartial() {
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore result = new com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore(this);
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            to_bitField0_ |= 0x00000001;
          }
          result.tagId_ = tagId_;
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.adunitId_ = adunitId_;
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.adunitForm_ = adunitForm_;
            to_bitField0_ |= 0x00000004;
          }
          if (((from_bitField0_ & 0x00000008) != 0)) {
            to_bitField0_ |= 0x00000008;
          }
          result.adxApp_ = adxApp_;
          if (((from_bitField0_ & 0x00000010) != 0)) {
            to_bitField0_ |= 0x00000010;
          }
          result.adxTag_ = adxTag_;
          result.bitField0_ = to_bitField0_;
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore) {
            return mergeFrom((com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore other) {
          if (other == com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.getDefaultInstance()) return this;
          if (other.hasTagId()) {
            bitField0_ |= 0x00000001;
            tagId_ = other.tagId_;
            onChanged();
          }
          if (other.hasAdunitId()) {
            setAdunitId(other.getAdunitId());
          }
          if (other.hasAdunitForm()) {
            setAdunitForm(other.getAdunitForm());
          }
          if (other.hasAdxApp()) {
            bitField0_ |= 0x00000008;
            adxApp_ = other.adxApp_;
            onChanged();
          }
          if (other.hasAdxTag()) {
            bitField0_ |= 0x00000010;
            adxTag_ = other.adxTag_;
            onChanged();
          }
          this.mergeUnknownFields(other.unknownFields);
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore parsedMessage = null;
          try {
            parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            parsedMessage = (com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore) e.getUnfinishedMessage();
            throw e.unwrapIOException();
          } finally {
            if (parsedMessage != null) {
              mergeFrom(parsedMessage);
            }
          }
          return this;
        }
        private int bitField0_;

        private java.lang.Object tagId_ = "";
        /**
         * <pre>
         * 外部广告位id
         * </pre>
         *
         * <code>optional string tag_id = 1;</code>
         */
        public boolean hasTagId() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <pre>
         * 外部广告位id
         * </pre>
         *
         * <code>optional string tag_id = 1;</code>
         */
        public java.lang.String getTagId() {
          java.lang.Object ref = tagId_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              tagId_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 外部广告位id
         * </pre>
         *
         * <code>optional string tag_id = 1;</code>
         */
        public com.google.protobuf.ByteString
            getTagIdBytes() {
          java.lang.Object ref = tagId_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            tagId_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 外部广告位id
         * </pre>
         *
         * <code>optional string tag_id = 1;</code>
         */
        public Builder setTagId(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
          tagId_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 外部广告位id
         * </pre>
         *
         * <code>optional string tag_id = 1;</code>
         */
        public Builder clearTagId() {
          bitField0_ = (bitField0_ & ~0x00000001);
          tagId_ = getDefaultInstance().getTagId();
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 外部广告位id
         * </pre>
         *
         * <code>optional string tag_id = 1;</code>
         */
        public Builder setTagIdBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
          tagId_ = value;
          onChanged();
          return this;
        }

        private int adunitId_ ;
        /**
         * <pre>
         * 内部广告位id
         * </pre>
         *
         * <code>optional int32 adunit_id = 2;</code>
         */
        public boolean hasAdunitId() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <pre>
         * 内部广告位id
         * </pre>
         *
         * <code>optional int32 adunit_id = 2;</code>
         */
        public int getAdunitId() {
          return adunitId_;
        }
        /**
         * <pre>
         * 内部广告位id
         * </pre>
         *
         * <code>optional int32 adunit_id = 2;</code>
         */
        public Builder setAdunitId(int value) {
          bitField0_ |= 0x00000002;
          adunitId_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 内部广告位id
         * </pre>
         *
         * <code>optional int32 adunit_id = 2;</code>
         */
        public Builder clearAdunitId() {
          bitField0_ = (bitField0_ & ~0x00000002);
          adunitId_ = 0;
          onChanged();
          return this;
        }

        private int adunitForm_ ;
        /**
         * <pre>
         *广告位形式 1横幅、2开屏
         * </pre>
         *
         * <code>optional int32 adunit_form = 3;</code>
         */
        public boolean hasAdunitForm() {
          return ((bitField0_ & 0x00000004) != 0);
        }
        /**
         * <pre>
         *广告位形式 1横幅、2开屏
         * </pre>
         *
         * <code>optional int32 adunit_form = 3;</code>
         */
        public int getAdunitForm() {
          return adunitForm_;
        }
        /**
         * <pre>
         *广告位形式 1横幅、2开屏
         * </pre>
         *
         * <code>optional int32 adunit_form = 3;</code>
         */
        public Builder setAdunitForm(int value) {
          bitField0_ |= 0x00000004;
          adunitForm_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *广告位形式 1横幅、2开屏
         * </pre>
         *
         * <code>optional int32 adunit_form = 3;</code>
         */
        public Builder clearAdunitForm() {
          bitField0_ = (bitField0_ & ~0x00000004);
          adunitForm_ = 0;
          onChanged();
          return this;
        }

        private java.lang.Object adxApp_ = "";
        /**
         * <pre>
         *应用adx_app（生成规则：ssp id 和 ssp 包名/budbule 进行hash md5。SSP_ID， APP_TYPE， md5(16位)，用点分隔 ）
         * </pre>
         *
         * <code>optional string adx_app = 4;</code>
         */
        public boolean hasAdxApp() {
          return ((bitField0_ & 0x00000008) != 0);
        }
        /**
         * <pre>
         *应用adx_app（生成规则：ssp id 和 ssp 包名/budbule 进行hash md5。SSP_ID， APP_TYPE， md5(16位)，用点分隔 ）
         * </pre>
         *
         * <code>optional string adx_app = 4;</code>
         */
        public java.lang.String getAdxApp() {
          java.lang.Object ref = adxApp_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              adxApp_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *应用adx_app（生成规则：ssp id 和 ssp 包名/budbule 进行hash md5。SSP_ID， APP_TYPE， md5(16位)，用点分隔 ）
         * </pre>
         *
         * <code>optional string adx_app = 4;</code>
         */
        public com.google.protobuf.ByteString
            getAdxAppBytes() {
          java.lang.Object ref = adxApp_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            adxApp_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *应用adx_app（生成规则：ssp id 和 ssp 包名/budbule 进行hash md5。SSP_ID， APP_TYPE， md5(16位)，用点分隔 ）
         * </pre>
         *
         * <code>optional string adx_app = 4;</code>
         */
        public Builder setAdxApp(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
          adxApp_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *应用adx_app（生成规则：ssp id 和 ssp 包名/budbule 进行hash md5。SSP_ID， APP_TYPE， md5(16位)，用点分隔 ）
         * </pre>
         *
         * <code>optional string adx_app = 4;</code>
         */
        public Builder clearAdxApp() {
          bitField0_ = (bitField0_ & ~0x00000008);
          adxApp_ = getDefaultInstance().getAdxApp();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *应用adx_app（生成规则：ssp id 和 ssp 包名/budbule 进行hash md5。SSP_ID， APP_TYPE， md5(16位)，用点分隔 ）
         * </pre>
         *
         * <code>optional string adx_app = 4;</code>
         */
        public Builder setAdxAppBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
          adxApp_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object adxTag_ = "";
        /**
         * <pre>
         *广告位 adx_tag（生成规则：ssp  id 和 ssp tag 进行hash md5。 SSP ID，SLOT TYPE，md5(16)，用点分隔）
         * </pre>
         *
         * <code>optional string adx_tag = 5;</code>
         */
        public boolean hasAdxTag() {
          return ((bitField0_ & 0x00000010) != 0);
        }
        /**
         * <pre>
         *广告位 adx_tag（生成规则：ssp  id 和 ssp tag 进行hash md5。 SSP ID，SLOT TYPE，md5(16)，用点分隔）
         * </pre>
         *
         * <code>optional string adx_tag = 5;</code>
         */
        public java.lang.String getAdxTag() {
          java.lang.Object ref = adxTag_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              adxTag_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *广告位 adx_tag（生成规则：ssp  id 和 ssp tag 进行hash md5。 SSP ID，SLOT TYPE，md5(16)，用点分隔）
         * </pre>
         *
         * <code>optional string adx_tag = 5;</code>
         */
        public com.google.protobuf.ByteString
            getAdxTagBytes() {
          java.lang.Object ref = adxTag_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            adxTag_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *广告位 adx_tag（生成规则：ssp  id 和 ssp tag 进行hash md5。 SSP ID，SLOT TYPE，md5(16)，用点分隔）
         * </pre>
         *
         * <code>optional string adx_tag = 5;</code>
         */
        public Builder setAdxTag(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
          adxTag_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *广告位 adx_tag（生成规则：ssp  id 和 ssp tag 进行hash md5。 SSP ID，SLOT TYPE，md5(16)，用点分隔）
         * </pre>
         *
         * <code>optional string adx_tag = 5;</code>
         */
        public Builder clearAdxTag() {
          bitField0_ = (bitField0_ & ~0x00000010);
          adxTag_ = getDefaultInstance().getAdxTag();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *广告位 adx_tag（生成规则：ssp  id 和 ssp tag 进行hash md5。 SSP ID，SLOT TYPE，md5(16)，用点分隔）
         * </pre>
         *
         * <code>optional string adx_tag = 5;</code>
         */
        public Builder setAdxTagBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
          adxTag_ = value;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:urtb.OriginLog.ImpCore)
      }

      // @@protoc_insertion_point(class_scope:urtb.OriginLog.ImpCore)
      private static final com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore();
      }

      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      @java.lang.Deprecated public static final com.google.protobuf.Parser<ImpCore>
          PARSER = new com.google.protobuf.AbstractParser<ImpCore>() {
        @java.lang.Override
        public ImpCore parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return new ImpCore(input, extensionRegistry);
        }
      };

      public static com.google.protobuf.Parser<ImpCore> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<ImpCore> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface EpSessionOrBuilder extends
        // @@protoc_insertion_point(interface_extends:urtb.OriginLog.EpSession)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>optional int32 dsp_id = 1;</code>
       */
      boolean hasDspId();
      /**
       * <code>optional int32 dsp_id = 1;</code>
       */
      int getDspId();

      /**
       * <code>optional int32 dsp_ep_id = 2;</code>
       */
      boolean hasDspEpId();
      /**
       * <code>optional int32 dsp_ep_id = 2;</code>
       */
      int getDspEpId();

      /**
       * <code>optional string ep_origin_req = 3;</code>
       */
      boolean hasEpOriginReq();
      /**
       * <code>optional string ep_origin_req = 3;</code>
       */
      java.lang.String getEpOriginReq();
      /**
       * <code>optional string ep_origin_req = 3;</code>
       */
      com.google.protobuf.ByteString
          getEpOriginReqBytes();

      /**
       * <code>optional int32 http_status_code = 4;</code>
       */
      boolean hasHttpStatusCode();
      /**
       * <code>optional int32 http_status_code = 4;</code>
       */
      int getHttpStatusCode();

      /**
       * <code>optional string ep_origin_resp = 5;</code>
       */
      boolean hasEpOriginResp();
      /**
       * <code>optional string ep_origin_resp = 5;</code>
       */
      java.lang.String getEpOriginResp();
      /**
       * <code>optional string ep_origin_resp = 5;</code>
       */
      com.google.protobuf.ByteString
          getEpOriginRespBytes();

      /**
       * <code>optional .urtb.OriginLog.EpSession.RespStatus resp_status = 6;</code>
       */
      boolean hasRespStatus();
      /**
       * <code>optional .urtb.OriginLog.EpSession.RespStatus resp_status = 6;</code>
       */
      com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.RespStatus getRespStatus();
    }
    /**
     * <pre>
     *响应核心内容
     * </pre>
     *
     * Protobuf type {@code urtb.OriginLog.EpSession}
     */
    public  static final class EpSession extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:urtb.OriginLog.EpSession)
        EpSessionOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use EpSession.newBuilder() to construct.
      private EpSession(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private EpSession() {
        epOriginReq_ = "";
        epOriginResp_ = "";
        respStatus_ = 1;
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new EpSession();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      private EpSession(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        this();
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        int mutable_bitField0_ = 0;
        com.google.protobuf.UnknownFieldSet.Builder unknownFields =
            com.google.protobuf.UnknownFieldSet.newBuilder();
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                bitField0_ |= 0x00000001;
                dspId_ = input.readInt32();
                break;
              }
              case 16: {
                bitField0_ |= 0x00000002;
                dspEpId_ = input.readInt32();
                break;
              }
              case 26: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00000004;
                epOriginReq_ = bs;
                break;
              }
              case 32: {
                bitField0_ |= 0x00000008;
                httpStatusCode_ = input.readInt32();
                break;
              }
              case 42: {
                com.google.protobuf.ByteString bs = input.readBytes();
                bitField0_ |= 0x00000010;
                epOriginResp_ = bs;
                break;
              }
              case 48: {
                int rawValue = input.readEnum();
                  @SuppressWarnings("deprecation")
                com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.RespStatus value = com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.RespStatus.valueOf(rawValue);
                if (value == null) {
                  unknownFields.mergeVarintField(6, rawValue);
                } else {
                  bitField0_ |= 0x00000020;
                  respStatus_ = rawValue;
                }
                break;
              }
              default: {
                if (!parseUnknownField(
                    input, unknownFields, extensionRegistry, tag)) {
                  done = true;
                }
                break;
              }
            }
          }
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(this);
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e).setUnfinishedMessage(this);
        } finally {
          this.unknownFields = unknownFields.build();
          makeExtensionsImmutable();
        }
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_EpSession_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_EpSession_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.class, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.Builder.class);
      }

      /**
       * <pre>
       *响应状态
       * </pre>
       *
       * Protobuf enum {@code urtb.OriginLog.EpSession.RespStatus}
       */
      public enum RespStatus
          implements com.google.protobuf.ProtocolMessageEnum {
        /**
         * <pre>
         *超时
         * </pre>
         *
         * <code>RESP_TIMEOUT = 1;</code>
         */
        RESP_TIMEOUT(1),
        /**
         * <pre>
         *已填充
         * </pre>
         *
         * <code>RESP_FILL = 2;</code>
         */
        RESP_FILL(2),
        /**
         * <pre>
         *未填充
         * </pre>
         *
         * <code>RESP_NO_FILL = 3;</code>
         */
        RESP_NO_FILL(3),
        /**
         * <pre>
         *异常
         * </pre>
         *
         * <code>RESP_EXCEPTION = 4;</code>
         */
        RESP_EXCEPTION(4),
        ;

        /**
         * <pre>
         *超时
         * </pre>
         *
         * <code>RESP_TIMEOUT = 1;</code>
         */
        public static final int RESP_TIMEOUT_VALUE = 1;
        /**
         * <pre>
         *已填充
         * </pre>
         *
         * <code>RESP_FILL = 2;</code>
         */
        public static final int RESP_FILL_VALUE = 2;
        /**
         * <pre>
         *未填充
         * </pre>
         *
         * <code>RESP_NO_FILL = 3;</code>
         */
        public static final int RESP_NO_FILL_VALUE = 3;
        /**
         * <pre>
         *异常
         * </pre>
         *
         * <code>RESP_EXCEPTION = 4;</code>
         */
        public static final int RESP_EXCEPTION_VALUE = 4;


        public final int getNumber() {
          return value;
        }

        /**
         * @deprecated Use {@link #forNumber(int)} instead.
         */
        @java.lang.Deprecated
        public static RespStatus valueOf(int value) {
          return forNumber(value);
        }

        public static RespStatus forNumber(int value) {
          switch (value) {
            case 1: return RESP_TIMEOUT;
            case 2: return RESP_FILL;
            case 3: return RESP_NO_FILL;
            case 4: return RESP_EXCEPTION;
            default: return null;
          }
        }

        public static com.google.protobuf.Internal.EnumLiteMap<RespStatus>
            internalGetValueMap() {
          return internalValueMap;
        }
        private static final com.google.protobuf.Internal.EnumLiteMap<
            RespStatus> internalValueMap =
              new com.google.protobuf.Internal.EnumLiteMap<RespStatus>() {
                public RespStatus findValueByNumber(int number) {
                  return RespStatus.forNumber(number);
                }
              };

        public final com.google.protobuf.Descriptors.EnumValueDescriptor
            getValueDescriptor() {
          return getDescriptor().getValues().get(ordinal());
        }
        public final com.google.protobuf.Descriptors.EnumDescriptor
            getDescriptorForType() {
          return getDescriptor();
        }
        public static final com.google.protobuf.Descriptors.EnumDescriptor
            getDescriptor() {
          return com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.getDescriptor().getEnumTypes().get(0);
        }

        private static final RespStatus[] VALUES = values();

        public static RespStatus valueOf(
            com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
          if (desc.getType() != getDescriptor()) {
            throw new java.lang.IllegalArgumentException(
              "EnumValueDescriptor is not for this type.");
          }
          return VALUES[desc.getIndex()];
        }

        private final int value;

        private RespStatus(int value) {
          this.value = value;
        }

        // @@protoc_insertion_point(enum_scope:urtb.OriginLog.EpSession.RespStatus)
      }

      private int bitField0_;
      public static final int DSP_ID_FIELD_NUMBER = 1;
      private int dspId_;
      /**
       * <code>optional int32 dsp_id = 1;</code>
       */
      public boolean hasDspId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 dsp_id = 1;</code>
       */
      public int getDspId() {
        return dspId_;
      }

      public static final int DSP_EP_ID_FIELD_NUMBER = 2;
      private int dspEpId_;
      /**
       * <code>optional int32 dsp_ep_id = 2;</code>
       */
      public boolean hasDspEpId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 dsp_ep_id = 2;</code>
       */
      public int getDspEpId() {
        return dspEpId_;
      }

      public static final int EP_ORIGIN_REQ_FIELD_NUMBER = 3;
      private volatile java.lang.Object epOriginReq_;
      /**
       * <code>optional string ep_origin_req = 3;</code>
       */
      public boolean hasEpOriginReq() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string ep_origin_req = 3;</code>
       */
      public java.lang.String getEpOriginReq() {
        java.lang.Object ref = epOriginReq_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            epOriginReq_ = s;
          }
          return s;
        }
      }
      /**
       * <code>optional string ep_origin_req = 3;</code>
       */
      public com.google.protobuf.ByteString
          getEpOriginReqBytes() {
        java.lang.Object ref = epOriginReq_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          epOriginReq_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int HTTP_STATUS_CODE_FIELD_NUMBER = 4;
      private int httpStatusCode_;
      /**
       * <code>optional int32 http_status_code = 4;</code>
       */
      public boolean hasHttpStatusCode() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int32 http_status_code = 4;</code>
       */
      public int getHttpStatusCode() {
        return httpStatusCode_;
      }

      public static final int EP_ORIGIN_RESP_FIELD_NUMBER = 5;
      private volatile java.lang.Object epOriginResp_;
      /**
       * <code>optional string ep_origin_resp = 5;</code>
       */
      public boolean hasEpOriginResp() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional string ep_origin_resp = 5;</code>
       */
      public java.lang.String getEpOriginResp() {
        java.lang.Object ref = epOriginResp_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            epOriginResp_ = s;
          }
          return s;
        }
      }
      /**
       * <code>optional string ep_origin_resp = 5;</code>
       */
      public com.google.protobuf.ByteString
          getEpOriginRespBytes() {
        java.lang.Object ref = epOriginResp_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          epOriginResp_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int RESP_STATUS_FIELD_NUMBER = 6;
      private int respStatus_;
      /**
       * <code>optional .urtb.OriginLog.EpSession.RespStatus resp_status = 6;</code>
       */
      public boolean hasRespStatus() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional .urtb.OriginLog.EpSession.RespStatus resp_status = 6;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.RespStatus getRespStatus() {
        @SuppressWarnings("deprecation")
        com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.RespStatus result = com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.RespStatus.valueOf(respStatus_);
        return result == null ? com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.RespStatus.RESP_TIMEOUT : result;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeInt32(1, dspId_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeInt32(2, dspEpId_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 3, epOriginReq_);
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          output.writeInt32(4, httpStatusCode_);
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 5, epOriginResp_);
        }
        if (((bitField0_ & 0x00000020) != 0)) {
          output.writeEnum(6, respStatus_);
        }
        unknownFields.writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(1, dspId_);
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(2, dspEpId_);
        }
        if (((bitField0_ & 0x00000004) != 0)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, epOriginReq_);
        }
        if (((bitField0_ & 0x00000008) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(4, httpStatusCode_);
        }
        if (((bitField0_ & 0x00000010) != 0)) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, epOriginResp_);
        }
        if (((bitField0_ & 0x00000020) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeEnumSize(6, respStatus_);
        }
        size += unknownFields.getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession)) {
          return super.equals(obj);
        }
        com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession other = (com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession) obj;

        if (hasDspId() != other.hasDspId()) return false;
        if (hasDspId()) {
          if (getDspId()
              != other.getDspId()) return false;
        }
        if (hasDspEpId() != other.hasDspEpId()) return false;
        if (hasDspEpId()) {
          if (getDspEpId()
              != other.getDspEpId()) return false;
        }
        if (hasEpOriginReq() != other.hasEpOriginReq()) return false;
        if (hasEpOriginReq()) {
          if (!getEpOriginReq()
              .equals(other.getEpOriginReq())) return false;
        }
        if (hasHttpStatusCode() != other.hasHttpStatusCode()) return false;
        if (hasHttpStatusCode()) {
          if (getHttpStatusCode()
              != other.getHttpStatusCode()) return false;
        }
        if (hasEpOriginResp() != other.hasEpOriginResp()) return false;
        if (hasEpOriginResp()) {
          if (!getEpOriginResp()
              .equals(other.getEpOriginResp())) return false;
        }
        if (hasRespStatus() != other.hasRespStatus()) return false;
        if (hasRespStatus()) {
          if (respStatus_ != other.respStatus_) return false;
        }
        if (!unknownFields.equals(other.unknownFields)) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasDspId()) {
          hash = (37 * hash) + DSP_ID_FIELD_NUMBER;
          hash = (53 * hash) + getDspId();
        }
        if (hasDspEpId()) {
          hash = (37 * hash) + DSP_EP_ID_FIELD_NUMBER;
          hash = (53 * hash) + getDspEpId();
        }
        if (hasEpOriginReq()) {
          hash = (37 * hash) + EP_ORIGIN_REQ_FIELD_NUMBER;
          hash = (53 * hash) + getEpOriginReq().hashCode();
        }
        if (hasHttpStatusCode()) {
          hash = (37 * hash) + HTTP_STATUS_CODE_FIELD_NUMBER;
          hash = (53 * hash) + getHttpStatusCode();
        }
        if (hasEpOriginResp()) {
          hash = (37 * hash) + EP_ORIGIN_RESP_FIELD_NUMBER;
          hash = (53 * hash) + getEpOriginResp().hashCode();
        }
        if (hasRespStatus()) {
          hash = (37 * hash) + RESP_STATUS_FIELD_NUMBER;
          hash = (53 * hash) + respStatus_;
        }
        hash = (29 * hash) + unknownFields.hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       *响应核心内容
       * </pre>
       *
       * Protobuf type {@code urtb.OriginLog.EpSession}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:urtb.OriginLog.EpSession)
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSessionOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_EpSession_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_EpSession_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.class, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.Builder.class);
        }

        // Construct using com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessageV3
                  .alwaysUseFieldBuilders) {
          }
        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          dspId_ = 0;
          bitField0_ = (bitField0_ & ~0x00000001);
          dspEpId_ = 0;
          bitField0_ = (bitField0_ & ~0x00000002);
          epOriginReq_ = "";
          bitField0_ = (bitField0_ & ~0x00000004);
          httpStatusCode_ = 0;
          bitField0_ = (bitField0_ & ~0x00000008);
          epOriginResp_ = "";
          bitField0_ = (bitField0_ & ~0x00000010);
          respStatus_ = 1;
          bitField0_ = (bitField0_ & ~0x00000020);
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_EpSession_descriptor;
        }

        @java.lang.Override
        public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession getDefaultInstanceForType() {
          return com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.getDefaultInstance();
        }

        @java.lang.Override
        public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession build() {
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession buildPartial() {
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession result = new com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession(this);
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.dspId_ = dspId_;
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.dspEpId_ = dspEpId_;
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            to_bitField0_ |= 0x00000004;
          }
          result.epOriginReq_ = epOriginReq_;
          if (((from_bitField0_ & 0x00000008) != 0)) {
            result.httpStatusCode_ = httpStatusCode_;
            to_bitField0_ |= 0x00000008;
          }
          if (((from_bitField0_ & 0x00000010) != 0)) {
            to_bitField0_ |= 0x00000010;
          }
          result.epOriginResp_ = epOriginResp_;
          if (((from_bitField0_ & 0x00000020) != 0)) {
            to_bitField0_ |= 0x00000020;
          }
          result.respStatus_ = respStatus_;
          result.bitField0_ = to_bitField0_;
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession) {
            return mergeFrom((com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession other) {
          if (other == com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.getDefaultInstance()) return this;
          if (other.hasDspId()) {
            setDspId(other.getDspId());
          }
          if (other.hasDspEpId()) {
            setDspEpId(other.getDspEpId());
          }
          if (other.hasEpOriginReq()) {
            bitField0_ |= 0x00000004;
            epOriginReq_ = other.epOriginReq_;
            onChanged();
          }
          if (other.hasHttpStatusCode()) {
            setHttpStatusCode(other.getHttpStatusCode());
          }
          if (other.hasEpOriginResp()) {
            bitField0_ |= 0x00000010;
            epOriginResp_ = other.epOriginResp_;
            onChanged();
          }
          if (other.hasRespStatus()) {
            setRespStatus(other.getRespStatus());
          }
          this.mergeUnknownFields(other.unknownFields);
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession parsedMessage = null;
          try {
            parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            parsedMessage = (com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession) e.getUnfinishedMessage();
            throw e.unwrapIOException();
          } finally {
            if (parsedMessage != null) {
              mergeFrom(parsedMessage);
            }
          }
          return this;
        }
        private int bitField0_;

        private int dspId_ ;
        /**
         * <code>optional int32 dsp_id = 1;</code>
         */
        public boolean hasDspId() {
          return ((bitField0_ & 0x00000001) != 0);
        }
        /**
         * <code>optional int32 dsp_id = 1;</code>
         */
        public int getDspId() {
          return dspId_;
        }
        /**
         * <code>optional int32 dsp_id = 1;</code>
         */
        public Builder setDspId(int value) {
          bitField0_ |= 0x00000001;
          dspId_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional int32 dsp_id = 1;</code>
         */
        public Builder clearDspId() {
          bitField0_ = (bitField0_ & ~0x00000001);
          dspId_ = 0;
          onChanged();
          return this;
        }

        private int dspEpId_ ;
        /**
         * <code>optional int32 dsp_ep_id = 2;</code>
         */
        public boolean hasDspEpId() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <code>optional int32 dsp_ep_id = 2;</code>
         */
        public int getDspEpId() {
          return dspEpId_;
        }
        /**
         * <code>optional int32 dsp_ep_id = 2;</code>
         */
        public Builder setDspEpId(int value) {
          bitField0_ |= 0x00000002;
          dspEpId_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional int32 dsp_ep_id = 2;</code>
         */
        public Builder clearDspEpId() {
          bitField0_ = (bitField0_ & ~0x00000002);
          dspEpId_ = 0;
          onChanged();
          return this;
        }

        private java.lang.Object epOriginReq_ = "";
        /**
         * <code>optional string ep_origin_req = 3;</code>
         */
        public boolean hasEpOriginReq() {
          return ((bitField0_ & 0x00000004) != 0);
        }
        /**
         * <code>optional string ep_origin_req = 3;</code>
         */
        public java.lang.String getEpOriginReq() {
          java.lang.Object ref = epOriginReq_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              epOriginReq_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>optional string ep_origin_req = 3;</code>
         */
        public com.google.protobuf.ByteString
            getEpOriginReqBytes() {
          java.lang.Object ref = epOriginReq_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            epOriginReq_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>optional string ep_origin_req = 3;</code>
         */
        public Builder setEpOriginReq(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
          epOriginReq_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional string ep_origin_req = 3;</code>
         */
        public Builder clearEpOriginReq() {
          bitField0_ = (bitField0_ & ~0x00000004);
          epOriginReq_ = getDefaultInstance().getEpOriginReq();
          onChanged();
          return this;
        }
        /**
         * <code>optional string ep_origin_req = 3;</code>
         */
        public Builder setEpOriginReqBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
          epOriginReq_ = value;
          onChanged();
          return this;
        }

        private int httpStatusCode_ ;
        /**
         * <code>optional int32 http_status_code = 4;</code>
         */
        public boolean hasHttpStatusCode() {
          return ((bitField0_ & 0x00000008) != 0);
        }
        /**
         * <code>optional int32 http_status_code = 4;</code>
         */
        public int getHttpStatusCode() {
          return httpStatusCode_;
        }
        /**
         * <code>optional int32 http_status_code = 4;</code>
         */
        public Builder setHttpStatusCode(int value) {
          bitField0_ |= 0x00000008;
          httpStatusCode_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional int32 http_status_code = 4;</code>
         */
        public Builder clearHttpStatusCode() {
          bitField0_ = (bitField0_ & ~0x00000008);
          httpStatusCode_ = 0;
          onChanged();
          return this;
        }

        private java.lang.Object epOriginResp_ = "";
        /**
         * <code>optional string ep_origin_resp = 5;</code>
         */
        public boolean hasEpOriginResp() {
          return ((bitField0_ & 0x00000010) != 0);
        }
        /**
         * <code>optional string ep_origin_resp = 5;</code>
         */
        public java.lang.String getEpOriginResp() {
          java.lang.Object ref = epOriginResp_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              epOriginResp_ = s;
            }
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <code>optional string ep_origin_resp = 5;</code>
         */
        public com.google.protobuf.ByteString
            getEpOriginRespBytes() {
          java.lang.Object ref = epOriginResp_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            epOriginResp_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>optional string ep_origin_resp = 5;</code>
         */
        public Builder setEpOriginResp(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
          epOriginResp_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>optional string ep_origin_resp = 5;</code>
         */
        public Builder clearEpOriginResp() {
          bitField0_ = (bitField0_ & ~0x00000010);
          epOriginResp_ = getDefaultInstance().getEpOriginResp();
          onChanged();
          return this;
        }
        /**
         * <code>optional string ep_origin_resp = 5;</code>
         */
        public Builder setEpOriginRespBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
          epOriginResp_ = value;
          onChanged();
          return this;
        }

        private int respStatus_ = 1;
        /**
         * <code>optional .urtb.OriginLog.EpSession.RespStatus resp_status = 6;</code>
         */
        public boolean hasRespStatus() {
          return ((bitField0_ & 0x00000020) != 0);
        }
        /**
         * <code>optional .urtb.OriginLog.EpSession.RespStatus resp_status = 6;</code>
         */
        public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.RespStatus getRespStatus() {
          @SuppressWarnings("deprecation")
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.RespStatus result = com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.RespStatus.valueOf(respStatus_);
          return result == null ? com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.RespStatus.RESP_TIMEOUT : result;
        }
        /**
         * <code>optional .urtb.OriginLog.EpSession.RespStatus resp_status = 6;</code>
         */
        public Builder setRespStatus(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.RespStatus value) {
          if (value == null) {
            throw new NullPointerException();
          }
          bitField0_ |= 0x00000020;
          respStatus_ = value.getNumber();
          onChanged();
          return this;
        }
        /**
         * <code>optional .urtb.OriginLog.EpSession.RespStatus resp_status = 6;</code>
         */
        public Builder clearRespStatus() {
          bitField0_ = (bitField0_ & ~0x00000020);
          respStatus_ = 1;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:urtb.OriginLog.EpSession)
      }

      // @@protoc_insertion_point(class_scope:urtb.OriginLog.EpSession)
      private static final com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession();
      }

      public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      @java.lang.Deprecated public static final com.google.protobuf.Parser<EpSession>
          PARSER = new com.google.protobuf.AbstractParser<EpSession>() {
        @java.lang.Override
        public EpSession parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return new EpSession(input, extensionRegistry);
        }
      };

      public static com.google.protobuf.Parser<EpSession> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<EpSession> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    private int bitField0_;
    public static final int SSP_ORIGIN_REQ_FIELD_NUMBER = 1;
    private volatile java.lang.Object sspOriginReq_;
    /**
     * <pre>
     * 请求核心内容
     * 原始请求
     * </pre>
     *
     * <code>optional string ssp_origin_req = 1;</code>
     */
    public boolean hasSspOriginReq() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 请求核心内容
     * 原始请求
     * </pre>
     *
     * <code>optional string ssp_origin_req = 1;</code>
     */
    public java.lang.String getSspOriginReq() {
      java.lang.Object ref = sspOriginReq_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sspOriginReq_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 请求核心内容
     * 原始请求
     * </pre>
     *
     * <code>optional string ssp_origin_req = 1;</code>
     */
    public com.google.protobuf.ByteString
        getSspOriginReqBytes() {
      java.lang.Object ref = sspOriginReq_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sspOriginReq_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int REQ_TIME_FIELD_NUMBER = 2;
    private long reqTime_;
    /**
     * <pre>
     * 原始请求时间
     * </pre>
     *
     * <code>optional int64 req_time = 2;</code>
     */
    public boolean hasReqTime() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 原始请求时间
     * </pre>
     *
     * <code>optional int64 req_time = 2;</code>
     */
    public long getReqTime() {
      return reqTime_;
    }

    public static final int PKG_FIELD_NUMBER = 3;
    private volatile java.lang.Object pkg_;
    /**
     * <pre>
     * 包名
     * </pre>
     *
     * <code>optional string pkg = 3;</code>
     */
    public boolean hasPkg() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 包名
     * </pre>
     *
     * <code>optional string pkg = 3;</code>
     */
    public java.lang.String getPkg() {
      java.lang.Object ref = pkg_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          pkg_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 包名
     * </pre>
     *
     * <code>optional string pkg = 3;</code>
     */
    public com.google.protobuf.ByteString
        getPkgBytes() {
      java.lang.Object ref = pkg_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        pkg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int REQ_ID_FIELD_NUMBER = 4;
    private volatile java.lang.Object reqId_;
    /**
     * <pre>
     * 请求id
     * </pre>
     *
     * <code>optional string req_id = 4;</code>
     */
    public boolean hasReqId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 请求id
     * </pre>
     *
     * <code>optional string req_id = 4;</code>
     */
    public java.lang.String getReqId() {
      java.lang.Object ref = reqId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          reqId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 请求id
     * </pre>
     *
     * <code>optional string req_id = 4;</code>
     */
    public com.google.protobuf.ByteString
        getReqIdBytes() {
      java.lang.Object ref = reqId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reqId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BID_ID_FIELD_NUMBER = 5;
    private volatile java.lang.Object bidId_;
    /**
     * <pre>
     * 竞价id
     * </pre>
     *
     * <code>optional string bid_id = 5;</code>
     */
    public boolean hasBidId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 竞价id
     * </pre>
     *
     * <code>optional string bid_id = 5;</code>
     */
    public java.lang.String getBidId() {
      java.lang.Object ref = bidId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          bidId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 竞价id
     * </pre>
     *
     * <code>optional string bid_id = 5;</code>
     */
    public com.google.protobuf.ByteString
        getBidIdBytes() {
      java.lang.Object ref = bidId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        bidId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SSP_ID_FIELD_NUMBER = 6;
    private int sspId_;
    /**
     * <pre>
     * ssp_id
     * </pre>
     *
     * <code>optional int32 ssp_id = 6;</code>
     */
    public boolean hasSspId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * ssp_id
     * </pre>
     *
     * <code>optional int32 ssp_id = 6;</code>
     */
    public int getSspId() {
      return sspId_;
    }

    public static final int SSP_EP_ID_FIELD_NUMBER = 7;
    private int sspEpId_;
    /**
     * <pre>
     * ssp_ep_id
     * </pre>
     *
     * <code>optional int32 ssp_ep_id = 7;</code>
     */
    public boolean hasSspEpId() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * ssp_ep_id
     * </pre>
     *
     * <code>optional int32 ssp_ep_id = 7;</code>
     */
    public int getSspEpId() {
      return sspEpId_;
    }

    public static final int SCHAIN_LIST_FIELD_NUMBER = 8;
    private java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode> schainList_;
    /**
     * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
     */
    public java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode> getSchainListList() {
      return schainList_;
    }
    /**
     * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
     */
    public java.util.List<? extends com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNodeOrBuilder> 
        getSchainListOrBuilderList() {
      return schainList_;
    }
    /**
     * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
     */
    public int getSchainListCount() {
      return schainList_.size();
    }
    /**
     * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
     */
    public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode getSchainList(int index) {
      return schainList_.get(index);
    }
    /**
     * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
     */
    public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNodeOrBuilder getSchainListOrBuilder(
        int index) {
      return schainList_.get(index);
    }

    public static final int IMP_CORE_LIST_FIELD_NUMBER = 9;
    private java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore> impCoreList_;
    /**
     * <pre>
     * imp list
     * </pre>
     *
     * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
     */
    public java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore> getImpCoreListList() {
      return impCoreList_;
    }
    /**
     * <pre>
     * imp list
     * </pre>
     *
     * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
     */
    public java.util.List<? extends com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCoreOrBuilder> 
        getImpCoreListOrBuilderList() {
      return impCoreList_;
    }
    /**
     * <pre>
     * imp list
     * </pre>
     *
     * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
     */
    public int getImpCoreListCount() {
      return impCoreList_.size();
    }
    /**
     * <pre>
     * imp list
     * </pre>
     *
     * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
     */
    public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore getImpCoreList(int index) {
      return impCoreList_.get(index);
    }
    /**
     * <pre>
     * imp list
     * </pre>
     *
     * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
     */
    public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCoreOrBuilder getImpCoreListOrBuilder(
        int index) {
      return impCoreList_.get(index);
    }

    public static final int EP_SESSION_LIST_FIELD_NUMBER = 10;
    private java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession> epSessionList_;
    /**
     * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
     */
    public java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession> getEpSessionListList() {
      return epSessionList_;
    }
    /**
     * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
     */
    public java.util.List<? extends com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSessionOrBuilder> 
        getEpSessionListOrBuilderList() {
      return epSessionList_;
    }
    /**
     * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
     */
    public int getEpSessionListCount() {
      return epSessionList_.size();
    }
    /**
     * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
     */
    public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession getEpSessionList(int index) {
      return epSessionList_.get(index);
    }
    /**
     * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
     */
    public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSessionOrBuilder getEpSessionListOrBuilder(
        int index) {
      return epSessionList_.get(index);
    }

    public static final int WIN_EP_ID_FIELD_NUMBER = 11;
    private int winEpId_;
    /**
     * <pre>
     *竞价核心内容
     *获胜ep_id
     * </pre>
     *
     * <code>optional int32 win_ep_id = 11;</code>
     */
    public boolean hasWinEpId() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     *竞价核心内容
     *获胜ep_id
     * </pre>
     *
     * <code>optional int32 win_ep_id = 11;</code>
     */
    public int getWinEpId() {
      return winEpId_;
    }

    public static final int GAID_FIELD_NUMBER = 12;
    private volatile java.lang.Object gaid_;
    /**
     * <pre>
     *设备信息
     * </pre>
     *
     * <code>optional string gaid = 12;</code>
     */
    public boolean hasGaid() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     *设备信息
     * </pre>
     *
     * <code>optional string gaid = 12;</code>
     */
    public java.lang.String getGaid() {
      java.lang.Object ref = gaid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          gaid_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *设备信息
     * </pre>
     *
     * <code>optional string gaid = 12;</code>
     */
    public com.google.protobuf.ByteString
        getGaidBytes() {
      java.lang.Object ref = gaid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gaid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IDFA_FIELD_NUMBER = 13;
    private volatile java.lang.Object idfa_;
    /**
     * <code>optional string idfa = 13;</code>
     */
    public boolean hasIdfa() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional string idfa = 13;</code>
     */
    public java.lang.String getIdfa() {
      java.lang.Object ref = idfa_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          idfa_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string idfa = 13;</code>
     */
    public com.google.protobuf.ByteString
        getIdfaBytes() {
      java.lang.Object ref = idfa_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        idfa_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SSP_ORIGIN_RESP_FIELD_NUMBER = 14;
    private volatile java.lang.Object sspOriginResp_;
    /**
     * <pre>
     *下发给ssp响应
     * </pre>
     *
     * <code>optional string ssp_origin_resp = 14;</code>
     */
    public boolean hasSspOriginResp() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     *下发给ssp响应
     * </pre>
     *
     * <code>optional string ssp_origin_resp = 14;</code>
     */
    public java.lang.String getSspOriginResp() {
      java.lang.Object ref = sspOriginResp_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sspOriginResp_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *下发给ssp响应
     * </pre>
     *
     * <code>optional string ssp_origin_resp = 14;</code>
     */
    public com.google.protobuf.ByteString
        getSspOriginRespBytes() {
      java.lang.Object ref = sspOriginResp_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sspOriginResp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, sspOriginReq_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, reqTime_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, pkg_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, reqId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, bidId_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(6, sspId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt32(7, sspEpId_);
      }
      for (int i = 0; i < schainList_.size(); i++) {
        output.writeMessage(8, schainList_.get(i));
      }
      for (int i = 0; i < impCoreList_.size(); i++) {
        output.writeMessage(9, impCoreList_.get(i));
      }
      for (int i = 0; i < epSessionList_.size(); i++) {
        output.writeMessage(10, epSessionList_.get(i));
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeInt32(11, winEpId_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, gaid_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 13, idfa_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 14, sspOriginResp_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, sspOriginReq_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, reqTime_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, pkg_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, reqId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, bidId_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, sspId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, sspEpId_);
      }
      for (int i = 0; i < schainList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, schainList_.get(i));
      }
      for (int i = 0; i < impCoreList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, impCoreList_.get(i));
      }
      for (int i = 0; i < epSessionList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(10, epSessionList_.get(i));
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, winEpId_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, gaid_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, idfa_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, sspOriginResp_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog)) {
        return super.equals(obj);
      }
      com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog other = (com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog) obj;

      if (hasSspOriginReq() != other.hasSspOriginReq()) return false;
      if (hasSspOriginReq()) {
        if (!getSspOriginReq()
            .equals(other.getSspOriginReq())) return false;
      }
      if (hasReqTime() != other.hasReqTime()) return false;
      if (hasReqTime()) {
        if (getReqTime()
            != other.getReqTime()) return false;
      }
      if (hasPkg() != other.hasPkg()) return false;
      if (hasPkg()) {
        if (!getPkg()
            .equals(other.getPkg())) return false;
      }
      if (hasReqId() != other.hasReqId()) return false;
      if (hasReqId()) {
        if (!getReqId()
            .equals(other.getReqId())) return false;
      }
      if (hasBidId() != other.hasBidId()) return false;
      if (hasBidId()) {
        if (!getBidId()
            .equals(other.getBidId())) return false;
      }
      if (hasSspId() != other.hasSspId()) return false;
      if (hasSspId()) {
        if (getSspId()
            != other.getSspId()) return false;
      }
      if (hasSspEpId() != other.hasSspEpId()) return false;
      if (hasSspEpId()) {
        if (getSspEpId()
            != other.getSspEpId()) return false;
      }
      if (!getSchainListList()
          .equals(other.getSchainListList())) return false;
      if (!getImpCoreListList()
          .equals(other.getImpCoreListList())) return false;
      if (!getEpSessionListList()
          .equals(other.getEpSessionListList())) return false;
      if (hasWinEpId() != other.hasWinEpId()) return false;
      if (hasWinEpId()) {
        if (getWinEpId()
            != other.getWinEpId()) return false;
      }
      if (hasGaid() != other.hasGaid()) return false;
      if (hasGaid()) {
        if (!getGaid()
            .equals(other.getGaid())) return false;
      }
      if (hasIdfa() != other.hasIdfa()) return false;
      if (hasIdfa()) {
        if (!getIdfa()
            .equals(other.getIdfa())) return false;
      }
      if (hasSspOriginResp() != other.hasSspOriginResp()) return false;
      if (hasSspOriginResp()) {
        if (!getSspOriginResp()
            .equals(other.getSspOriginResp())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSspOriginReq()) {
        hash = (37 * hash) + SSP_ORIGIN_REQ_FIELD_NUMBER;
        hash = (53 * hash) + getSspOriginReq().hashCode();
      }
      if (hasReqTime()) {
        hash = (37 * hash) + REQ_TIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getReqTime());
      }
      if (hasPkg()) {
        hash = (37 * hash) + PKG_FIELD_NUMBER;
        hash = (53 * hash) + getPkg().hashCode();
      }
      if (hasReqId()) {
        hash = (37 * hash) + REQ_ID_FIELD_NUMBER;
        hash = (53 * hash) + getReqId().hashCode();
      }
      if (hasBidId()) {
        hash = (37 * hash) + BID_ID_FIELD_NUMBER;
        hash = (53 * hash) + getBidId().hashCode();
      }
      if (hasSspId()) {
        hash = (37 * hash) + SSP_ID_FIELD_NUMBER;
        hash = (53 * hash) + getSspId();
      }
      if (hasSspEpId()) {
        hash = (37 * hash) + SSP_EP_ID_FIELD_NUMBER;
        hash = (53 * hash) + getSspEpId();
      }
      if (getSchainListCount() > 0) {
        hash = (37 * hash) + SCHAIN_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getSchainListList().hashCode();
      }
      if (getImpCoreListCount() > 0) {
        hash = (37 * hash) + IMP_CORE_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getImpCoreListList().hashCode();
      }
      if (getEpSessionListCount() > 0) {
        hash = (37 * hash) + EP_SESSION_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getEpSessionListList().hashCode();
      }
      if (hasWinEpId()) {
        hash = (37 * hash) + WIN_EP_ID_FIELD_NUMBER;
        hash = (53 * hash) + getWinEpId();
      }
      if (hasGaid()) {
        hash = (37 * hash) + GAID_FIELD_NUMBER;
        hash = (53 * hash) + getGaid().hashCode();
      }
      if (hasIdfa()) {
        hash = (37 * hash) + IDFA_FIELD_NUMBER;
        hash = (53 * hash) + getIdfa().hashCode();
      }
      if (hasSspOriginResp()) {
        hash = (37 * hash) + SSP_ORIGIN_RESP_FIELD_NUMBER;
        hash = (53 * hash) + getSspOriginResp().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code urtb.OriginLog}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:urtb.OriginLog)
        com.iflytek.traffic.log.urtb.RetrievalLog.OriginLogOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.class, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.Builder.class);
      }

      // Construct using com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSchainListFieldBuilder();
          getImpCoreListFieldBuilder();
          getEpSessionListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        sspOriginReq_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        reqTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        pkg_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        reqId_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        bidId_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        sspId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        sspEpId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        if (schainListBuilder_ == null) {
          schainList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000080);
        } else {
          schainListBuilder_.clear();
        }
        if (impCoreListBuilder_ == null) {
          impCoreList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000100);
        } else {
          impCoreListBuilder_.clear();
        }
        if (epSessionListBuilder_ == null) {
          epSessionList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000200);
        } else {
          epSessionListBuilder_.clear();
        }
        winEpId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000400);
        gaid_ = "";
        bitField0_ = (bitField0_ & ~0x00000800);
        idfa_ = "";
        bitField0_ = (bitField0_ & ~0x00001000);
        sspOriginResp_ = "";
        bitField0_ = (bitField0_ & ~0x00002000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.iflytek.traffic.log.urtb.RetrievalLog.internal_static_urtb_OriginLog_descriptor;
      }

      @java.lang.Override
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog getDefaultInstanceForType() {
        return com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.getDefaultInstance();
      }

      @java.lang.Override
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog build() {
        com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog buildPartial() {
        com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog result = new com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.sspOriginReq_ = sspOriginReq_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.reqTime_ = reqTime_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.pkg_ = pkg_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.reqId_ = reqId_;
        if (((from_bitField0_ & 0x00000010) != 0)) {
          to_bitField0_ |= 0x00000010;
        }
        result.bidId_ = bidId_;
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.sspId_ = sspId_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.sspEpId_ = sspEpId_;
          to_bitField0_ |= 0x00000040;
        }
        if (schainListBuilder_ == null) {
          if (((bitField0_ & 0x00000080) != 0)) {
            schainList_ = java.util.Collections.unmodifiableList(schainList_);
            bitField0_ = (bitField0_ & ~0x00000080);
          }
          result.schainList_ = schainList_;
        } else {
          result.schainList_ = schainListBuilder_.build();
        }
        if (impCoreListBuilder_ == null) {
          if (((bitField0_ & 0x00000100) != 0)) {
            impCoreList_ = java.util.Collections.unmodifiableList(impCoreList_);
            bitField0_ = (bitField0_ & ~0x00000100);
          }
          result.impCoreList_ = impCoreList_;
        } else {
          result.impCoreList_ = impCoreListBuilder_.build();
        }
        if (epSessionListBuilder_ == null) {
          if (((bitField0_ & 0x00000200) != 0)) {
            epSessionList_ = java.util.Collections.unmodifiableList(epSessionList_);
            bitField0_ = (bitField0_ & ~0x00000200);
          }
          result.epSessionList_ = epSessionList_;
        } else {
          result.epSessionList_ = epSessionListBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.winEpId_ = winEpId_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          to_bitField0_ |= 0x00000100;
        }
        result.gaid_ = gaid_;
        if (((from_bitField0_ & 0x00001000) != 0)) {
          to_bitField0_ |= 0x00000200;
        }
        result.idfa_ = idfa_;
        if (((from_bitField0_ & 0x00002000) != 0)) {
          to_bitField0_ |= 0x00000400;
        }
        result.sspOriginResp_ = sspOriginResp_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog) {
          return mergeFrom((com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog other) {
        if (other == com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.getDefaultInstance()) return this;
        if (other.hasSspOriginReq()) {
          bitField0_ |= 0x00000001;
          sspOriginReq_ = other.sspOriginReq_;
          onChanged();
        }
        if (other.hasReqTime()) {
          setReqTime(other.getReqTime());
        }
        if (other.hasPkg()) {
          bitField0_ |= 0x00000004;
          pkg_ = other.pkg_;
          onChanged();
        }
        if (other.hasReqId()) {
          bitField0_ |= 0x00000008;
          reqId_ = other.reqId_;
          onChanged();
        }
        if (other.hasBidId()) {
          bitField0_ |= 0x00000010;
          bidId_ = other.bidId_;
          onChanged();
        }
        if (other.hasSspId()) {
          setSspId(other.getSspId());
        }
        if (other.hasSspEpId()) {
          setSspEpId(other.getSspEpId());
        }
        if (schainListBuilder_ == null) {
          if (!other.schainList_.isEmpty()) {
            if (schainList_.isEmpty()) {
              schainList_ = other.schainList_;
              bitField0_ = (bitField0_ & ~0x00000080);
            } else {
              ensureSchainListIsMutable();
              schainList_.addAll(other.schainList_);
            }
            onChanged();
          }
        } else {
          if (!other.schainList_.isEmpty()) {
            if (schainListBuilder_.isEmpty()) {
              schainListBuilder_.dispose();
              schainListBuilder_ = null;
              schainList_ = other.schainList_;
              bitField0_ = (bitField0_ & ~0x00000080);
              schainListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSchainListFieldBuilder() : null;
            } else {
              schainListBuilder_.addAllMessages(other.schainList_);
            }
          }
        }
        if (impCoreListBuilder_ == null) {
          if (!other.impCoreList_.isEmpty()) {
            if (impCoreList_.isEmpty()) {
              impCoreList_ = other.impCoreList_;
              bitField0_ = (bitField0_ & ~0x00000100);
            } else {
              ensureImpCoreListIsMutable();
              impCoreList_.addAll(other.impCoreList_);
            }
            onChanged();
          }
        } else {
          if (!other.impCoreList_.isEmpty()) {
            if (impCoreListBuilder_.isEmpty()) {
              impCoreListBuilder_.dispose();
              impCoreListBuilder_ = null;
              impCoreList_ = other.impCoreList_;
              bitField0_ = (bitField0_ & ~0x00000100);
              impCoreListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getImpCoreListFieldBuilder() : null;
            } else {
              impCoreListBuilder_.addAllMessages(other.impCoreList_);
            }
          }
        }
        if (epSessionListBuilder_ == null) {
          if (!other.epSessionList_.isEmpty()) {
            if (epSessionList_.isEmpty()) {
              epSessionList_ = other.epSessionList_;
              bitField0_ = (bitField0_ & ~0x00000200);
            } else {
              ensureEpSessionListIsMutable();
              epSessionList_.addAll(other.epSessionList_);
            }
            onChanged();
          }
        } else {
          if (!other.epSessionList_.isEmpty()) {
            if (epSessionListBuilder_.isEmpty()) {
              epSessionListBuilder_.dispose();
              epSessionListBuilder_ = null;
              epSessionList_ = other.epSessionList_;
              bitField0_ = (bitField0_ & ~0x00000200);
              epSessionListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getEpSessionListFieldBuilder() : null;
            } else {
              epSessionListBuilder_.addAllMessages(other.epSessionList_);
            }
          }
        }
        if (other.hasWinEpId()) {
          setWinEpId(other.getWinEpId());
        }
        if (other.hasGaid()) {
          bitField0_ |= 0x00000800;
          gaid_ = other.gaid_;
          onChanged();
        }
        if (other.hasIdfa()) {
          bitField0_ |= 0x00001000;
          idfa_ = other.idfa_;
          onChanged();
        }
        if (other.hasSspOriginResp()) {
          bitField0_ |= 0x00002000;
          sspOriginResp_ = other.sspOriginResp_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object sspOriginReq_ = "";
      /**
       * <pre>
       * 请求核心内容
       * 原始请求
       * </pre>
       *
       * <code>optional string ssp_origin_req = 1;</code>
       */
      public boolean hasSspOriginReq() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 请求核心内容
       * 原始请求
       * </pre>
       *
       * <code>optional string ssp_origin_req = 1;</code>
       */
      public java.lang.String getSspOriginReq() {
        java.lang.Object ref = sspOriginReq_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            sspOriginReq_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 请求核心内容
       * 原始请求
       * </pre>
       *
       * <code>optional string ssp_origin_req = 1;</code>
       */
      public com.google.protobuf.ByteString
          getSspOriginReqBytes() {
        java.lang.Object ref = sspOriginReq_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sspOriginReq_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 请求核心内容
       * 原始请求
       * </pre>
       *
       * <code>optional string ssp_origin_req = 1;</code>
       */
      public Builder setSspOriginReq(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        sspOriginReq_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求核心内容
       * 原始请求
       * </pre>
       *
       * <code>optional string ssp_origin_req = 1;</code>
       */
      public Builder clearSspOriginReq() {
        bitField0_ = (bitField0_ & ~0x00000001);
        sspOriginReq_ = getDefaultInstance().getSspOriginReq();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求核心内容
       * 原始请求
       * </pre>
       *
       * <code>optional string ssp_origin_req = 1;</code>
       */
      public Builder setSspOriginReqBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        sspOriginReq_ = value;
        onChanged();
        return this;
      }

      private long reqTime_ ;
      /**
       * <pre>
       * 原始请求时间
       * </pre>
       *
       * <code>optional int64 req_time = 2;</code>
       */
      public boolean hasReqTime() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 原始请求时间
       * </pre>
       *
       * <code>optional int64 req_time = 2;</code>
       */
      public long getReqTime() {
        return reqTime_;
      }
      /**
       * <pre>
       * 原始请求时间
       * </pre>
       *
       * <code>optional int64 req_time = 2;</code>
       */
      public Builder setReqTime(long value) {
        bitField0_ |= 0x00000002;
        reqTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 原始请求时间
       * </pre>
       *
       * <code>optional int64 req_time = 2;</code>
       */
      public Builder clearReqTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        reqTime_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object pkg_ = "";
      /**
       * <pre>
       * 包名
       * </pre>
       *
       * <code>optional string pkg = 3;</code>
       */
      public boolean hasPkg() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 包名
       * </pre>
       *
       * <code>optional string pkg = 3;</code>
       */
      public java.lang.String getPkg() {
        java.lang.Object ref = pkg_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            pkg_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 包名
       * </pre>
       *
       * <code>optional string pkg = 3;</code>
       */
      public com.google.protobuf.ByteString
          getPkgBytes() {
        java.lang.Object ref = pkg_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          pkg_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 包名
       * </pre>
       *
       * <code>optional string pkg = 3;</code>
       */
      public Builder setPkg(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        pkg_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 包名
       * </pre>
       *
       * <code>optional string pkg = 3;</code>
       */
      public Builder clearPkg() {
        bitField0_ = (bitField0_ & ~0x00000004);
        pkg_ = getDefaultInstance().getPkg();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 包名
       * </pre>
       *
       * <code>optional string pkg = 3;</code>
       */
      public Builder setPkgBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        pkg_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object reqId_ = "";
      /**
       * <pre>
       * 请求id
       * </pre>
       *
       * <code>optional string req_id = 4;</code>
       */
      public boolean hasReqId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 请求id
       * </pre>
       *
       * <code>optional string req_id = 4;</code>
       */
      public java.lang.String getReqId() {
        java.lang.Object ref = reqId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            reqId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 请求id
       * </pre>
       *
       * <code>optional string req_id = 4;</code>
       */
      public com.google.protobuf.ByteString
          getReqIdBytes() {
        java.lang.Object ref = reqId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          reqId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 请求id
       * </pre>
       *
       * <code>optional string req_id = 4;</code>
       */
      public Builder setReqId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        reqId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求id
       * </pre>
       *
       * <code>optional string req_id = 4;</code>
       */
      public Builder clearReqId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        reqId_ = getDefaultInstance().getReqId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求id
       * </pre>
       *
       * <code>optional string req_id = 4;</code>
       */
      public Builder setReqIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        reqId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object bidId_ = "";
      /**
       * <pre>
       * 竞价id
       * </pre>
       *
       * <code>optional string bid_id = 5;</code>
       */
      public boolean hasBidId() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 竞价id
       * </pre>
       *
       * <code>optional string bid_id = 5;</code>
       */
      public java.lang.String getBidId() {
        java.lang.Object ref = bidId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            bidId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 竞价id
       * </pre>
       *
       * <code>optional string bid_id = 5;</code>
       */
      public com.google.protobuf.ByteString
          getBidIdBytes() {
        java.lang.Object ref = bidId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          bidId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 竞价id
       * </pre>
       *
       * <code>optional string bid_id = 5;</code>
       */
      public Builder setBidId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        bidId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价id
       * </pre>
       *
       * <code>optional string bid_id = 5;</code>
       */
      public Builder clearBidId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        bidId_ = getDefaultInstance().getBidId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 竞价id
       * </pre>
       *
       * <code>optional string bid_id = 5;</code>
       */
      public Builder setBidIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        bidId_ = value;
        onChanged();
        return this;
      }

      private int sspId_ ;
      /**
       * <pre>
       * ssp_id
       * </pre>
       *
       * <code>optional int32 ssp_id = 6;</code>
       */
      public boolean hasSspId() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * ssp_id
       * </pre>
       *
       * <code>optional int32 ssp_id = 6;</code>
       */
      public int getSspId() {
        return sspId_;
      }
      /**
       * <pre>
       * ssp_id
       * </pre>
       *
       * <code>optional int32 ssp_id = 6;</code>
       */
      public Builder setSspId(int value) {
        bitField0_ |= 0x00000020;
        sspId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * ssp_id
       * </pre>
       *
       * <code>optional int32 ssp_id = 6;</code>
       */
      public Builder clearSspId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        sspId_ = 0;
        onChanged();
        return this;
      }

      private int sspEpId_ ;
      /**
       * <pre>
       * ssp_ep_id
       * </pre>
       *
       * <code>optional int32 ssp_ep_id = 7;</code>
       */
      public boolean hasSspEpId() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * ssp_ep_id
       * </pre>
       *
       * <code>optional int32 ssp_ep_id = 7;</code>
       */
      public int getSspEpId() {
        return sspEpId_;
      }
      /**
       * <pre>
       * ssp_ep_id
       * </pre>
       *
       * <code>optional int32 ssp_ep_id = 7;</code>
       */
      public Builder setSspEpId(int value) {
        bitField0_ |= 0x00000040;
        sspEpId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * ssp_ep_id
       * </pre>
       *
       * <code>optional int32 ssp_ep_id = 7;</code>
       */
      public Builder clearSspEpId() {
        bitField0_ = (bitField0_ & ~0x00000040);
        sspEpId_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode> schainList_ =
        java.util.Collections.emptyList();
      private void ensureSchainListIsMutable() {
        if (!((bitField0_ & 0x00000080) != 0)) {
          schainList_ = new java.util.ArrayList<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode>(schainList_);
          bitField0_ |= 0x00000080;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.Builder, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNodeOrBuilder> schainListBuilder_;

      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode> getSchainListList() {
        if (schainListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(schainList_);
        } else {
          return schainListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public int getSchainListCount() {
        if (schainListBuilder_ == null) {
          return schainList_.size();
        } else {
          return schainListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode getSchainList(int index) {
        if (schainListBuilder_ == null) {
          return schainList_.get(index);
        } else {
          return schainListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public Builder setSchainList(
          int index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode value) {
        if (schainListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSchainListIsMutable();
          schainList_.set(index, value);
          onChanged();
        } else {
          schainListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public Builder setSchainList(
          int index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.Builder builderForValue) {
        if (schainListBuilder_ == null) {
          ensureSchainListIsMutable();
          schainList_.set(index, builderForValue.build());
          onChanged();
        } else {
          schainListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public Builder addSchainList(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode value) {
        if (schainListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSchainListIsMutable();
          schainList_.add(value);
          onChanged();
        } else {
          schainListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public Builder addSchainList(
          int index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode value) {
        if (schainListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSchainListIsMutable();
          schainList_.add(index, value);
          onChanged();
        } else {
          schainListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public Builder addSchainList(
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.Builder builderForValue) {
        if (schainListBuilder_ == null) {
          ensureSchainListIsMutable();
          schainList_.add(builderForValue.build());
          onChanged();
        } else {
          schainListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public Builder addSchainList(
          int index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.Builder builderForValue) {
        if (schainListBuilder_ == null) {
          ensureSchainListIsMutable();
          schainList_.add(index, builderForValue.build());
          onChanged();
        } else {
          schainListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public Builder addAllSchainList(
          java.lang.Iterable<? extends com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode> values) {
        if (schainListBuilder_ == null) {
          ensureSchainListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, schainList_);
          onChanged();
        } else {
          schainListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public Builder clearSchainList() {
        if (schainListBuilder_ == null) {
          schainList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000080);
          onChanged();
        } else {
          schainListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public Builder removeSchainList(int index) {
        if (schainListBuilder_ == null) {
          ensureSchainListIsMutable();
          schainList_.remove(index);
          onChanged();
        } else {
          schainListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.Builder getSchainListBuilder(
          int index) {
        return getSchainListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNodeOrBuilder getSchainListOrBuilder(
          int index) {
        if (schainListBuilder_ == null) {
          return schainList_.get(index);  } else {
          return schainListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public java.util.List<? extends com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNodeOrBuilder> 
           getSchainListOrBuilderList() {
        if (schainListBuilder_ != null) {
          return schainListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(schainList_);
        }
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.Builder addSchainListBuilder() {
        return getSchainListFieldBuilder().addBuilder(
            com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.getDefaultInstance());
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.Builder addSchainListBuilder(
          int index) {
        return getSchainListFieldBuilder().addBuilder(
            index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.getDefaultInstance());
      }
      /**
       * <code>repeated .urtb.OriginLog.SchainNode schain_list = 8;</code>
       */
      public java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.Builder> 
           getSchainListBuilderList() {
        return getSchainListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.Builder, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNodeOrBuilder> 
          getSchainListFieldBuilder() {
        if (schainListBuilder_ == null) {
          schainListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNode.Builder, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.SchainNodeOrBuilder>(
                  schainList_,
                  ((bitField0_ & 0x00000080) != 0),
                  getParentForChildren(),
                  isClean());
          schainList_ = null;
        }
        return schainListBuilder_;
      }

      private java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore> impCoreList_ =
        java.util.Collections.emptyList();
      private void ensureImpCoreListIsMutable() {
        if (!((bitField0_ & 0x00000100) != 0)) {
          impCoreList_ = new java.util.ArrayList<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore>(impCoreList_);
          bitField0_ |= 0x00000100;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.Builder, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCoreOrBuilder> impCoreListBuilder_;

      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore> getImpCoreListList() {
        if (impCoreListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(impCoreList_);
        } else {
          return impCoreListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public int getImpCoreListCount() {
        if (impCoreListBuilder_ == null) {
          return impCoreList_.size();
        } else {
          return impCoreListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore getImpCoreList(int index) {
        if (impCoreListBuilder_ == null) {
          return impCoreList_.get(index);
        } else {
          return impCoreListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public Builder setImpCoreList(
          int index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore value) {
        if (impCoreListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureImpCoreListIsMutable();
          impCoreList_.set(index, value);
          onChanged();
        } else {
          impCoreListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public Builder setImpCoreList(
          int index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.Builder builderForValue) {
        if (impCoreListBuilder_ == null) {
          ensureImpCoreListIsMutable();
          impCoreList_.set(index, builderForValue.build());
          onChanged();
        } else {
          impCoreListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public Builder addImpCoreList(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore value) {
        if (impCoreListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureImpCoreListIsMutable();
          impCoreList_.add(value);
          onChanged();
        } else {
          impCoreListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public Builder addImpCoreList(
          int index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore value) {
        if (impCoreListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureImpCoreListIsMutable();
          impCoreList_.add(index, value);
          onChanged();
        } else {
          impCoreListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public Builder addImpCoreList(
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.Builder builderForValue) {
        if (impCoreListBuilder_ == null) {
          ensureImpCoreListIsMutable();
          impCoreList_.add(builderForValue.build());
          onChanged();
        } else {
          impCoreListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public Builder addImpCoreList(
          int index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.Builder builderForValue) {
        if (impCoreListBuilder_ == null) {
          ensureImpCoreListIsMutable();
          impCoreList_.add(index, builderForValue.build());
          onChanged();
        } else {
          impCoreListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public Builder addAllImpCoreList(
          java.lang.Iterable<? extends com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore> values) {
        if (impCoreListBuilder_ == null) {
          ensureImpCoreListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, impCoreList_);
          onChanged();
        } else {
          impCoreListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public Builder clearImpCoreList() {
        if (impCoreListBuilder_ == null) {
          impCoreList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000100);
          onChanged();
        } else {
          impCoreListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public Builder removeImpCoreList(int index) {
        if (impCoreListBuilder_ == null) {
          ensureImpCoreListIsMutable();
          impCoreList_.remove(index);
          onChanged();
        } else {
          impCoreListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.Builder getImpCoreListBuilder(
          int index) {
        return getImpCoreListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCoreOrBuilder getImpCoreListOrBuilder(
          int index) {
        if (impCoreListBuilder_ == null) {
          return impCoreList_.get(index);  } else {
          return impCoreListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public java.util.List<? extends com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCoreOrBuilder> 
           getImpCoreListOrBuilderList() {
        if (impCoreListBuilder_ != null) {
          return impCoreListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(impCoreList_);
        }
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.Builder addImpCoreListBuilder() {
        return getImpCoreListFieldBuilder().addBuilder(
            com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.getDefaultInstance());
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.Builder addImpCoreListBuilder(
          int index) {
        return getImpCoreListFieldBuilder().addBuilder(
            index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.getDefaultInstance());
      }
      /**
       * <pre>
       * imp list
       * </pre>
       *
       * <code>repeated .urtb.OriginLog.ImpCore imp_core_list = 9;</code>
       */
      public java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.Builder> 
           getImpCoreListBuilderList() {
        return getImpCoreListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.Builder, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCoreOrBuilder> 
          getImpCoreListFieldBuilder() {
        if (impCoreListBuilder_ == null) {
          impCoreListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCore.Builder, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.ImpCoreOrBuilder>(
                  impCoreList_,
                  ((bitField0_ & 0x00000100) != 0),
                  getParentForChildren(),
                  isClean());
          impCoreList_ = null;
        }
        return impCoreListBuilder_;
      }

      private java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession> epSessionList_ =
        java.util.Collections.emptyList();
      private void ensureEpSessionListIsMutable() {
        if (!((bitField0_ & 0x00000200) != 0)) {
          epSessionList_ = new java.util.ArrayList<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession>(epSessionList_);
          bitField0_ |= 0x00000200;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.Builder, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSessionOrBuilder> epSessionListBuilder_;

      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession> getEpSessionListList() {
        if (epSessionListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(epSessionList_);
        } else {
          return epSessionListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public int getEpSessionListCount() {
        if (epSessionListBuilder_ == null) {
          return epSessionList_.size();
        } else {
          return epSessionListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession getEpSessionList(int index) {
        if (epSessionListBuilder_ == null) {
          return epSessionList_.get(index);
        } else {
          return epSessionListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public Builder setEpSessionList(
          int index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession value) {
        if (epSessionListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEpSessionListIsMutable();
          epSessionList_.set(index, value);
          onChanged();
        } else {
          epSessionListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public Builder setEpSessionList(
          int index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.Builder builderForValue) {
        if (epSessionListBuilder_ == null) {
          ensureEpSessionListIsMutable();
          epSessionList_.set(index, builderForValue.build());
          onChanged();
        } else {
          epSessionListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public Builder addEpSessionList(com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession value) {
        if (epSessionListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEpSessionListIsMutable();
          epSessionList_.add(value);
          onChanged();
        } else {
          epSessionListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public Builder addEpSessionList(
          int index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession value) {
        if (epSessionListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEpSessionListIsMutable();
          epSessionList_.add(index, value);
          onChanged();
        } else {
          epSessionListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public Builder addEpSessionList(
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.Builder builderForValue) {
        if (epSessionListBuilder_ == null) {
          ensureEpSessionListIsMutable();
          epSessionList_.add(builderForValue.build());
          onChanged();
        } else {
          epSessionListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public Builder addEpSessionList(
          int index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.Builder builderForValue) {
        if (epSessionListBuilder_ == null) {
          ensureEpSessionListIsMutable();
          epSessionList_.add(index, builderForValue.build());
          onChanged();
        } else {
          epSessionListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public Builder addAllEpSessionList(
          java.lang.Iterable<? extends com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession> values) {
        if (epSessionListBuilder_ == null) {
          ensureEpSessionListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, epSessionList_);
          onChanged();
        } else {
          epSessionListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public Builder clearEpSessionList() {
        if (epSessionListBuilder_ == null) {
          epSessionList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000200);
          onChanged();
        } else {
          epSessionListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public Builder removeEpSessionList(int index) {
        if (epSessionListBuilder_ == null) {
          ensureEpSessionListIsMutable();
          epSessionList_.remove(index);
          onChanged();
        } else {
          epSessionListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.Builder getEpSessionListBuilder(
          int index) {
        return getEpSessionListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSessionOrBuilder getEpSessionListOrBuilder(
          int index) {
        if (epSessionListBuilder_ == null) {
          return epSessionList_.get(index);  } else {
          return epSessionListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public java.util.List<? extends com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSessionOrBuilder> 
           getEpSessionListOrBuilderList() {
        if (epSessionListBuilder_ != null) {
          return epSessionListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(epSessionList_);
        }
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.Builder addEpSessionListBuilder() {
        return getEpSessionListFieldBuilder().addBuilder(
            com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.getDefaultInstance());
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.Builder addEpSessionListBuilder(
          int index) {
        return getEpSessionListFieldBuilder().addBuilder(
            index, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.getDefaultInstance());
      }
      /**
       * <code>repeated .urtb.OriginLog.EpSession ep_session_list = 10;</code>
       */
      public java.util.List<com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.Builder> 
           getEpSessionListBuilderList() {
        return getEpSessionListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.Builder, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSessionOrBuilder> 
          getEpSessionListFieldBuilder() {
        if (epSessionListBuilder_ == null) {
          epSessionListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSession.Builder, com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog.EpSessionOrBuilder>(
                  epSessionList_,
                  ((bitField0_ & 0x00000200) != 0),
                  getParentForChildren(),
                  isClean());
          epSessionList_ = null;
        }
        return epSessionListBuilder_;
      }

      private int winEpId_ ;
      /**
       * <pre>
       *竞价核心内容
       *获胜ep_id
       * </pre>
       *
       * <code>optional int32 win_ep_id = 11;</code>
       */
      public boolean hasWinEpId() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       *竞价核心内容
       *获胜ep_id
       * </pre>
       *
       * <code>optional int32 win_ep_id = 11;</code>
       */
      public int getWinEpId() {
        return winEpId_;
      }
      /**
       * <pre>
       *竞价核心内容
       *获胜ep_id
       * </pre>
       *
       * <code>optional int32 win_ep_id = 11;</code>
       */
      public Builder setWinEpId(int value) {
        bitField0_ |= 0x00000400;
        winEpId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *竞价核心内容
       *获胜ep_id
       * </pre>
       *
       * <code>optional int32 win_ep_id = 11;</code>
       */
      public Builder clearWinEpId() {
        bitField0_ = (bitField0_ & ~0x00000400);
        winEpId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object gaid_ = "";
      /**
       * <pre>
       *设备信息
       * </pre>
       *
       * <code>optional string gaid = 12;</code>
       */
      public boolean hasGaid() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       *设备信息
       * </pre>
       *
       * <code>optional string gaid = 12;</code>
       */
      public java.lang.String getGaid() {
        java.lang.Object ref = gaid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            gaid_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *设备信息
       * </pre>
       *
       * <code>optional string gaid = 12;</code>
       */
      public com.google.protobuf.ByteString
          getGaidBytes() {
        java.lang.Object ref = gaid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          gaid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *设备信息
       * </pre>
       *
       * <code>optional string gaid = 12;</code>
       */
      public Builder setGaid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        gaid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *设备信息
       * </pre>
       *
       * <code>optional string gaid = 12;</code>
       */
      public Builder clearGaid() {
        bitField0_ = (bitField0_ & ~0x00000800);
        gaid_ = getDefaultInstance().getGaid();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *设备信息
       * </pre>
       *
       * <code>optional string gaid = 12;</code>
       */
      public Builder setGaidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        gaid_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object idfa_ = "";
      /**
       * <code>optional string idfa = 13;</code>
       */
      public boolean hasIdfa() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <code>optional string idfa = 13;</code>
       */
      public java.lang.String getIdfa() {
        java.lang.Object ref = idfa_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            idfa_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string idfa = 13;</code>
       */
      public com.google.protobuf.ByteString
          getIdfaBytes() {
        java.lang.Object ref = idfa_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          idfa_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string idfa = 13;</code>
       */
      public Builder setIdfa(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        idfa_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string idfa = 13;</code>
       */
      public Builder clearIdfa() {
        bitField0_ = (bitField0_ & ~0x00001000);
        idfa_ = getDefaultInstance().getIdfa();
        onChanged();
        return this;
      }
      /**
       * <code>optional string idfa = 13;</code>
       */
      public Builder setIdfaBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        idfa_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object sspOriginResp_ = "";
      /**
       * <pre>
       *下发给ssp响应
       * </pre>
       *
       * <code>optional string ssp_origin_resp = 14;</code>
       */
      public boolean hasSspOriginResp() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       *下发给ssp响应
       * </pre>
       *
       * <code>optional string ssp_origin_resp = 14;</code>
       */
      public java.lang.String getSspOriginResp() {
        java.lang.Object ref = sspOriginResp_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            sspOriginResp_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *下发给ssp响应
       * </pre>
       *
       * <code>optional string ssp_origin_resp = 14;</code>
       */
      public com.google.protobuf.ByteString
          getSspOriginRespBytes() {
        java.lang.Object ref = sspOriginResp_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sspOriginResp_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *下发给ssp响应
       * </pre>
       *
       * <code>optional string ssp_origin_resp = 14;</code>
       */
      public Builder setSspOriginResp(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        sspOriginResp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *下发给ssp响应
       * </pre>
       *
       * <code>optional string ssp_origin_resp = 14;</code>
       */
      public Builder clearSspOriginResp() {
        bitField0_ = (bitField0_ & ~0x00002000);
        sspOriginResp_ = getDefaultInstance().getSspOriginResp();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *下发给ssp响应
       * </pre>
       *
       * <code>optional string ssp_origin_resp = 14;</code>
       */
      public Builder setSspOriginRespBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        sspOriginResp_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:urtb.OriginLog)
    }

    // @@protoc_insertion_point(class_scope:urtb.OriginLog)
    private static final com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog();
    }

    public static com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OriginLog>
        PARSER = new com.google.protobuf.AbstractParser<OriginLog>() {
      @java.lang.Override
      public OriginLog parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OriginLog(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OriginLog> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OriginLog> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.iflytek.traffic.log.urtb.RetrievalLog.OriginLog getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_urtb_OriginLog_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_urtb_OriginLog_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_urtb_OriginLog_SchainNode_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_urtb_OriginLog_SchainNode_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_urtb_OriginLog_ImpCore_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_urtb_OriginLog_ImpCore_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_urtb_OriginLog_EpSession_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_urtb_OriginLog_EpSession_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023retrieval_log.proto\022\004urtb\"\373\005\n\tOriginLo" +
      "g\022\026\n\016ssp_origin_req\030\001 \001(\t\022\020\n\010req_time\030\002 " +
      "\001(\003\022\013\n\003pkg\030\003 \001(\t\022\016\n\006req_id\030\004 \001(\t\022\016\n\006bid_" +
      "id\030\005 \001(\t\022\016\n\006ssp_id\030\006 \001(\005\022\021\n\tssp_ep_id\030\007 " +
      "\001(\005\022/\n\013schain_list\030\010 \003(\0132\032.urtb.OriginLo" +
      "g.SchainNode\022.\n\rimp_core_list\030\t \003(\0132\027.ur" +
      "tb.OriginLog.ImpCore\0222\n\017ep_session_list\030" +
      "\n \003(\0132\031.urtb.OriginLog.EpSession\022\021\n\twin_" +
      "ep_id\030\013 \001(\005\022\014\n\004gaid\030\014 \001(\t\022\014\n\004idfa\030\r \001(\t\022" +
      "\027\n\017ssp_origin_resp\030\016 \001(\t\032(\n\nSchainNode\022\r" +
      "\n\005layer\030\001 \001(\005\022\013\n\003asi\030\002 \001(\t\032c\n\007ImpCore\022\016\n" +
      "\006tag_id\030\001 \001(\t\022\021\n\tadunit_id\030\002 \001(\005\022\023\n\013adun" +
      "it_form\030\003 \001(\005\022\017\n\007adx_app\030\004 \001(\t\022\017\n\007adx_ta" +
      "g\030\005 \001(\t\032\207\002\n\tEpSession\022\016\n\006dsp_id\030\001 \001(\005\022\021\n" +
      "\tdsp_ep_id\030\002 \001(\005\022\025\n\rep_origin_req\030\003 \001(\t\022" +
      "\030\n\020http_status_code\030\004 \001(\005\022\026\n\016ep_origin_r" +
      "esp\030\005 \001(\t\0229\n\013resp_status\030\006 \001(\0162$.urtb.Or" +
      "iginLog.EpSession.RespStatus\"S\n\nRespStat" +
      "us\022\020\n\014RESP_TIMEOUT\020\001\022\r\n\tRESP_FILL\020\002\022\020\n\014R" +
      "ESP_NO_FILL\020\003\022\022\n\016RESP_EXCEPTION\020\004B\036\n\034com" +
      ".iflytek.traffic.log.urtb"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_urtb_OriginLog_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_urtb_OriginLog_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_urtb_OriginLog_descriptor,
        new java.lang.String[] { "SspOriginReq", "ReqTime", "Pkg", "ReqId", "BidId", "SspId", "SspEpId", "SchainList", "ImpCoreList", "EpSessionList", "WinEpId", "Gaid", "Idfa", "SspOriginResp", });
    internal_static_urtb_OriginLog_SchainNode_descriptor =
      internal_static_urtb_OriginLog_descriptor.getNestedTypes().get(0);
    internal_static_urtb_OriginLog_SchainNode_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_urtb_OriginLog_SchainNode_descriptor,
        new java.lang.String[] { "Layer", "Asi", });
    internal_static_urtb_OriginLog_ImpCore_descriptor =
      internal_static_urtb_OriginLog_descriptor.getNestedTypes().get(1);
    internal_static_urtb_OriginLog_ImpCore_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_urtb_OriginLog_ImpCore_descriptor,
        new java.lang.String[] { "TagId", "AdunitId", "AdunitForm", "AdxApp", "AdxTag", });
    internal_static_urtb_OriginLog_EpSession_descriptor =
      internal_static_urtb_OriginLog_descriptor.getNestedTypes().get(2);
    internal_static_urtb_OriginLog_EpSession_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_urtb_OriginLog_EpSession_descriptor,
        new java.lang.String[] { "DspId", "DspEpId", "EpOriginReq", "HttpStatusCode", "EpOriginResp", "RespStatus", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
