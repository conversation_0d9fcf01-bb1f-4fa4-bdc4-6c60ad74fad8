package com.iflytek.traffic.service;

import cn.hutool.core.util.ArrayUtil;
import com.iflytek.traffic.controller.MediaRequestController;
import com.iflytek.traffic.procedure.*;
import com.iflytek.traffic.procedure.handler.DefaultExceptionHandler;
import com.iflytek.traffic.session.SessionContext;
import com.iflytek.traffic.ssp.SspEp;
import com.iflytek.traffic.util.GZip;
import com.iflytek.traffic.util.Util;
import io.micrometer.core.instrument.Counter;
import io.vertx.core.DeploymentOptions;
import io.vertx.core.Vertx;
import io.vertx.core.VertxOptions;
import io.vertx.core.eventbus.EventBus;
import io.vertx.core.http.HttpServerOptions;
import io.vertx.core.impl.cpu.CpuCoreSensor;
import io.vertx.micrometer.MicrometerMetricsOptions;
import io.vertx.micrometer.VertxPrometheusOptions;
import jakarta.annotation.PreDestroy;
import jakarta.servlet.AsyncContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class MediaRequestPoolService implements InitializingBean {

    @Autowired
    private MeterService meterService;

    @Autowired
    private RequestAsyncListener requestAsyncListener;

    @Autowired
    private QpsControlService qpsControlService;

    @Value("${vertx.event.pool.size}")
    private Integer eventPoolSize;

    @Value("${vertx.worker.pool.size}")
    private Integer workerPoolSize;

    @Value("${async.context.timeout:15000}")
    private Long asyncContextTimeout = 0L; // 设置为0表示不超时

    @Autowired
    private TimeWheel timeWheel;

    private Counter mediaRequestCountTotal;

    private Map<String, Counter> mediaQpsFilterCountMeterMap = new ConcurrentHashMap<>();

    private Vertx vertx;

    @Override
    public void afterPropertiesSet() throws Exception {
        mediaRequestCountTotal = meterService.count("media.request.count.total");
        VertxOptions vertxOptions = new VertxOptions();
        vertxOptions.setEventLoopPoolSize(eventPoolSize);
        vertxOptions.setWorkerPoolSize(workerPoolSize);
        vertxOptions.setMetricsOptions(new MicrometerMetricsOptions()
                .setPrometheusOptions(new VertxPrometheusOptions()
                        .setEnabled(true)
                        .setStartEmbeddedServer(true) // 启动内嵌HTTP服务暴露指标
                        .setEmbeddedServerOptions(new HttpServerOptions().setPort(8080)) // 指标端口
                        .setEmbeddedServerEndpoint("/metrics")) // 指标路径
                .setEnabled(true));
        this.vertx = Vertx.vertx(vertxOptions);
        System.out.println("CPU cores num: " + CpuCoreSensor.availableProcessors());
        DeploymentOptions deploymentOptions = new DeploymentOptions();
        // verticle 数量与eventPool线程数量保持一致
        deploymentOptions.setInstances(eventPoolSize);
        vertx.deployVerticle(ParseSspRequestVerticle.class, deploymentOptions);
        vertx.deployVerticle(ChoseDspVerticle.class, deploymentOptions);
        vertx.deployVerticle(RequestDspVerticle.class, deploymentOptions);
        vertx.deployVerticle(MaterialAuditVerticle.class, deploymentOptions);
        vertx.deployVerticle(PickOneVerticle.class, deploymentOptions);
        vertx.deployVerticle(Response2MediaVerticle.class, deploymentOptions);
        vertx.deployVerticle(ToLogVerticle.class, deploymentOptions);
        vertx.deployVerticle(TrafficFusionVerticle.class, deploymentOptions);

        vertx.eventBus().registerDefaultCodec(SessionContext.class, new SessionContextCodec());
        vertx.exceptionHandler(new DefaultExceptionHandler(vertx));
        // 启动时间轮
        timeWheel.startPeriodicTask(vertx);
    }

    /**
     * 媒体请求计数
     *
     * @param sspEp 下游ep
     */
    private void markMediaQpsFilter(SspEp sspEp) {
        String meterKey = "ssp." + sspEp.getPath() + ".qps.filter.count";
        if (mediaQpsFilterCountMeterMap.containsKey(meterKey)) {
            mediaQpsFilterCountMeterMap.get(meterKey).increment();
        } else {
            synchronized (MediaRequestPoolService.class) {
                if (mediaQpsFilterCountMeterMap.containsKey(meterKey)) {
                    mediaQpsFilterCountMeterMap.get(meterKey).increment();
                    return;
                }
                log.info("config ssp qps filter count meter: {}", meterKey);
                Counter counter = meterService.count(meterKey);
                counter.increment();
                mediaQpsFilterCountMeterMap.put(meterKey, counter);
            }
        }
    }

    public void execute(HttpServletRequest request, HttpServletResponse response, SspEp sspEp) throws UnsupportedEncodingException {
        mediaRequestCountTotal.increment();
        if (sspEp == null) {
            response204(response);
            return;
        }

        if (!qpsControlService.qpsCtrl(sspEp.genQpsCtrlKey(), sspEp.getQps())) {
            log.info("ssp {}, id {},  ep {} QPS limit exceeded", sspEp.getSspName(), sspEp.getSspId(), sspEp.getSspEpId());
            markMediaQpsFilter(sspEp);
            response204(response);
            return;
        }

        byte[] body = null;
        try {
            body = StreamUtils.copyToByteArray(request.getInputStream());
            if (Util.noEmpty(request.getHeader("Content-Encoding"))) {
                if (request.getHeader("Content-Encoding").trim().equals("gzip")) {
                    body = GZip.uncompress(body);
                }
            }
        } catch (IOException e) {
            log.error("parse ssp request body error: {}", e.getMessage(), e);
            response204(response);
            return;
        }
        if (ArrayUtil.isEmpty(body)) {
            log.warn("request body is empty.");
            response204(response);
            return;
        }
        SessionContext sessionContext = new SessionContext();
        sessionContext.setSspEp(sspEp);
        sessionContext.setReqBody(body);
        request.setAttribute("request_time", sessionContext.getStartTime());
        request.setAttribute("media_name", sspEp.getPath());
        AsyncContext asyncContext = request.startAsync();
        asyncContext.setTimeout(asyncContextTimeout);
        asyncContext.addListener(requestAsyncListener);
        sessionContext.setAsyncContext(asyncContext);
        sessionContext.addExecuteTime(System.currentTimeMillis());
        EventBus eventBus = vertx.eventBus();
        eventBus.request(EventBusAddress.PARSE_REQUEST, sessionContext);
    }

    void response204(HttpServletResponse response) {
        response.setStatus(204);
        response.setContentType("text/plain");
    }

    @PreDestroy
    public void destroy() {
        timeWheel.stop();
        vertx.close();
    }
}
