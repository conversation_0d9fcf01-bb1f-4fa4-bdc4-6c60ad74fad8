package com.iflytek.traffic.service;

import io.micrometer.core.instrument.Timer;
import jakarta.servlet.AsyncContext;
import jakarta.servlet.AsyncEvent;
import jakarta.servlet.AsyncListener;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RequestAsyncListener implements AsyncListener, InitializingBean {

    @Autowired
    private MeterService meterService;

    private Timer requestTimeTotal;

    private Timer completeWaitTimer;

    private final Map<String, Timer> mediaRequestTimeMap = new ConcurrentHashMap<>();

    @Override
    public void onComplete(AsyncEvent asyncEvent) throws IOException {
        AsyncContext asyncContext = asyncEvent.getAsyncContext();
        HttpServletRequest request = (HttpServletRequest) asyncContext.getRequest();

        long requestTime = (long) request.getAttribute("request_time");
        long currentTime = System.currentTimeMillis();
        Object completeTime = request.getAttribute("complete_time");
        requestTimeTotal.record(currentTime - requestTime, TimeUnit.MILLISECONDS);
        String mediaName = (String) request.getAttribute("media_name");
        markMediaRequestTime(mediaName, currentTime - requestTime);
        if (completeTime != null) {
            long waitTime = currentTime - (long) completeTime;
            completeWaitTimer.record(waitTime, TimeUnit.MILLISECONDS);
            log.info("request complete, media: {}, wait time: {} ms", mediaName, waitTime);
        } else {
            log.warn("complete time not set for media: {}", mediaName);
        }
    }

    @Override
    public void onTimeout(AsyncEvent asyncEvent) throws IOException {
        AsyncContext asyncContext = asyncEvent.getAsyncContext();
        try {
            HttpServletRequest request = (HttpServletRequest) asyncContext.getRequest();
            String mediaName = (String) request.getAttribute("media_name");
            long requestTime = (long) request.getAttribute("request_time");
            long currentTime = System.currentTimeMillis();
            markMediaRequestTime(mediaName, currentTime - requestTime);
            log.error("async timeout for media: {}, request time: {} ms", mediaName, currentTime - requestTime);
            HttpServletResponse response = (HttpServletResponse) asyncContext.getResponse();
            response.setStatus(HttpServletResponse.SC_GATEWAY_TIMEOUT);
            response.setContentType("text/plain");
        } catch (Exception e) {
            log.error("Error handling async timeout", e);
        } finally {
            asyncContext.complete();
        }
    }

    @Override
    public void onError(AsyncEvent asyncEvent) throws IOException {
        log.error("async error: {}", asyncEvent.toString());
        AsyncContext asyncContext = asyncEvent.getAsyncContext();
        try {
            HttpServletRequest request = (HttpServletRequest) asyncContext.getRequest();
            String mediaName = (String) request.getAttribute("media_name");
            long requestTime = (long) request.getAttribute("request_time");
            long currentTime = System.currentTimeMillis();
            markMediaRequestTime(mediaName, currentTime - requestTime);
            log.error("async error for media: {}, request time: {} ms", mediaName, currentTime - requestTime);
            HttpServletResponse response = (HttpServletResponse) asyncContext.getResponse();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.setContentType("text/plain");
        } catch (Exception e) {
            log.error("Error handling async error", e);
        } finally {
            asyncContext.complete();
        }
    }

    @Override
    public void onStartAsync(AsyncEvent asyncEvent) throws IOException {
        log.error("async start: {}", asyncEvent.toString());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        requestTimeTotal = meterService.time("ssp.request.time.total");
        completeWaitTimer = meterService.time("complete.wait.time.total");
    }

    /**
     * 媒体请求处理耗时
     *
     * @param mediaName 媒体名称
     */
    private void markMediaRequestTime(String mediaName, long mills) {
        if (mediaRequestTimeMap.containsKey(mediaName)) {
            mediaRequestTimeMap.get(mediaName).record(mills, TimeUnit.MILLISECONDS);
        } else {
            synchronized (RequestAsyncListener.class) {
                if (mediaRequestTimeMap.containsKey(mediaName)) {
                    mediaRequestTimeMap.get(mediaName).record(mills, TimeUnit.MILLISECONDS);
                    return;
                }
                String timeKey = "ssp." + mediaName + ".request.time";
                log.info("config request timer: {}", timeKey);
                Timer timer = meterService.time(timeKey);
                timer.record(mills, TimeUnit.MILLISECONDS);
                mediaRequestTimeMap.put(mediaName, timer);
            }
        }
    }
}
