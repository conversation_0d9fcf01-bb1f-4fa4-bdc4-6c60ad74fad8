package com.iflytek.traffic.service;

import cn.hutool.core.map.MapUtil;
import com.iflytek.traffic.procedure.EventBusAddress;
import com.iflytek.traffic.session.SessionContext;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import io.vertx.core.Vertx;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class TimeWheel implements InitializingBean {

    private Vertx vertx;

    @Value("${timewheel.slot.num:2000}")
    private int slot;

    @Value("${timewheel.periodic.delay:1}")
    private int periodicDelay;

    @Autowired
    private MeterService meterService;

    private Counter totalSessionCount;

    private Counter finishSessionCount;

    private Counter timeoutSessionCount;

    private Timer sessionHandleTimer;

    private AtomicInteger currentSlot = new AtomicInteger(0);

    private List<TimeNode> timeNodeList = new ArrayList<>();

    private ScheduledExecutorService timer;

    public void afterPropertiesSet() {
        for (int i = 0; i < slot; i++) {
            timeNodeList.add(new TimeNode());
        }
        totalSessionCount = meterService.count("timewheel.session.total.count");
        finishSessionCount = meterService.count("timewheel.session.finish.count");
        timeoutSessionCount = meterService.count("timewheel.session.timeout.count");
        sessionHandleTimer = meterService.time("timewheel.session.handle.timer");
        log.info("TimeWheel initialized with {} slots.", slot);
    }

    public void startPeriodicTask(Vertx vertx) {
        this.vertx = vertx;
        ScheduledExecutorService timer = Executors.newSingleThreadScheduledExecutor();
        timer.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                TimeNode timeNode = timeNodeList.get(currentSlot.get());
                if (MapUtil.isNotEmpty(timeNode.sessionContextMap)) {
                    Iterator<Map.Entry<String, SessionContext>> iterator = timeNode.sessionContextMap.entrySet().iterator();
                    while (iterator.hasNext()) {
                        Map.Entry<String, SessionContext> entry = iterator.next();
                        SessionContext sessionContext = entry.getValue();
                        if (sessionContext.getRequestDspEndTime() <= System.currentTimeMillis()) {
                            timeNode.removeSessionContext(vertx, sessionContext);
                            timeoutSessionCount.increment();
                            sessionHandleTimer.record(System.currentTimeMillis() - sessionContext.getTimeWheelStartTime(), TimeUnit.MILLISECONDS);
                            iterator.remove();
                        }
                    }
                }
                currentSlot.getAndUpdate(i -> (i + 1) % slot);
            }
        }, 0, periodicDelay, TimeUnit.MILLISECONDS);
        this.timer = timer;
    }

    void stop() {
        timer.shutdown();
        for (TimeNode timeNode : timeNodeList) {
            timeNode.removeAllSession(this.vertx);
        }
    }

    public void addSessionContext(SessionContext sessionContext) {
        long timeGap = sessionContext.getRequestDspEndTime() - System.currentTimeMillis();
        if (timeGap <= 0) {
            log.info("SessionContext with sessionId {} has already expired. RequestDspEndTime: {}, current time: {}",
                    sessionContext.getSessionId(), sessionContext.getRequestDspEndTime(), System.currentTimeMillis());
            timeGap = 10;
        }
        int slotIndex = (int)(currentSlot.get() + timeGap / periodicDelay) % slot;
        TimeNode timeNode = timeNodeList.get(slotIndex);
        sessionContext.setTimeWheelSlot(slotIndex);
        sessionContext.setTimeWheelStartTime(System.currentTimeMillis());
        timeNode.addSessionContext(sessionContext);
        totalSessionCount.increment();
    }

    public void finishSessionContext(SessionContext sessionContext) {
        int slotIndex = sessionContext.getTimeWheelSlot();
        TimeNode timeNode = timeNodeList.get(slotIndex);
        timeNode.removeSessionContext(this.vertx, sessionContext);
        finishSessionCount.increment();
        sessionHandleTimer.record(System.currentTimeMillis() - sessionContext.getTimeWheelStartTime(), TimeUnit.MILLISECONDS);
    }


    static class TimeNode {

        private ConcurrentHashMap<String, SessionContext> sessionContextMap = new ConcurrentHashMap<>();

        void addSessionContext(SessionContext sessionContext) {
            sessionContextMap.put(sessionContext.getSessionId(), sessionContext);
        }

        void removeSessionContext(Vertx vertx, SessionContext sessionContext) {
            sessionContextMap.remove(sessionContext.getSessionId());
            log.info("current time: {}, endTime: {}, complete request {}, finished session.", System.currentTimeMillis(),
                    sessionContext.getRequestDspEndTime(), sessionContext.getCompletedRequests());
            if (sessionContext.isAsyncContextCompleted()) {
                log.info("session context already completed, sessionId: {}", sessionContext.getSessionId());
                return;
            }
            if (MapUtil.isEmpty(sessionContext.getDspUnifiedResp())) {
                log.info("no qualified dsp response.");
                vertx.eventBus().request(EventBusAddress.RESPONSE_TO_MEDIA, sessionContext);
            } else {
                vertx.eventBus().request(EventBusAddress.FILTER_CREATIVE, sessionContext);
            }
        }

        void removeAllSession(Vertx vertx) {
            if (MapUtil.isNotEmpty(sessionContextMap)) {
                sessionContextMap.forEach((sessionId, sessionContext) -> {
                    removeSessionContext(vertx, sessionContext);
                });
                sessionContextMap.clear();
            }
        }
    }
}
