package com.iflytek.traffic.service;


import io.micrometer.core.instrument.*;
import io.micrometer.core.instrument.config.MeterFilter;
import io.micrometer.core.instrument.distribution.DistributionStatisticConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * 统计指标数据-新
 */
@Component("MeterService")
@Slf4j
public class MeterService {

    @Bean
    MeterRegistryCustomizer<MeterRegistry> configurer(@Value("${spring.application.name}") String applicationName) {
        return registry -> {
            registry.config().meterFilter(
                    new MeterFilter() {
                        @Override
                        public DistributionStatisticConfig configure(Meter.Id id, DistributionStatisticConfig config) {
                            //匹配http开头并且是timer类型的监控指标
                            if (id.getType() == Meter.Type.TIMER) {
                                return DistributionStatisticConfig.builder()
                                        .percentilesHistogram(true)
                                        .percentiles(0.5, 0.90, 0.95, 0.99)
//                                        .sla(Duration.ofMillis(50).toNanos(),
//                                                Duration.ofMillis(100).toNanos(),
//                                                Duration.ofMillis(200).toNanos(),
//                                                Duration.ofSeconds(1).toNanos(),
//                                                Duration.ofSeconds(5).toNanos())
//                                        .minimumExpectedValue(Duration.ofMillis(1).toNanos())
//                                        .maximumExpectedValue(Duration.ofSeconds(5).toNanos())
                                        .build()
                                        .merge(config);
                            } else {
                                return config;
                            }
                        }
                    }).commonTags("application", applicationName);
        };
//        return (registry) -> registry.config().commonTags("application", applicationName);
    }

    @Autowired
    private MeterRegistry registry;

    public Counter count(String name){
        return registry.counter(name);
    }

    public Timer time(String name){
        return registry.timer(name);
    }

    public Timer.Sample timeStart(){
        return Timer.start(registry);
    }

    public void timeStop(Timer timer,Timer.Sample sample){
        if(sample!=null){
            sample.stop(timer);
        }
        else{
            log.warn("sample is null ,timer is invalid,id:{}",timer.getId());
        }
    }

    public void timeStop(String key,Timer.Sample sample){
        if(sample!=null){
            sample.stop(registry.timer(key));
        }
        else{
            log.warn("sample is null ,timer is invalid,key:{}",key);
        }
    }

    public DistributionSummary summary(String name){
        return DistributionSummary.builder(name).publishPercentileHistogram(true).register(registry);
    }

    public Counter count(String name, String... tags){
        return registry.counter(name,tags);
    }

}
