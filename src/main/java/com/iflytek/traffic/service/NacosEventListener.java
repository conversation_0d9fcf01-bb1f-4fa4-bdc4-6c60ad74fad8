package com.iflytek.traffic.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class NacosEventListener {

    @Value("${spring.application.name}")
    private String serviceId;

    private final DiscoveryClient discoveryClient;

    public NacosEventListener(DiscoveryClient discoveryClient) {
        this.discoveryClient = discoveryClient;
    }

    public int getServiceCount() {
        if (discoveryClient != null) {
            try {
                return discoveryClient.getInstances(serviceId).size();
            } catch (Exception e) {
                log.error("Failed to get service count for {}: {}", serviceId, e.getMessage());
            }
        } else {
            log.warn("DiscoveryClient is not available, returning default service count: {}", 1);
        }
        return 1;
    }
}
