package com.iflytek.traffic.service;

import cn.hutool.core.map.MapUtil;
import com.google.common.util.concurrent.RateLimiter;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class QpsControlService implements ApplicationRunner {

    @Autowired
    private NacosEventListener nacosEventListener;

    @Value("${qps.ctrl.timeout:1}")
    private long qpsCtrlTimeout;

    @Value("${qps.ctrl.refresh.delay:10}")
    private long refreshDelay;

    private final Map<String, QpsControlNode> controlNodeMap = new ConcurrentHashMap<>();

    private ScheduledExecutorService scheduledExecutor;

    public boolean qpsCtrl(String key, int totalQps) {
        if (key == null || key.isEmpty()) {
            log.warn("QPS control key is null or empty");
            return true; // No control, allow all requests
        }

//        if (totalQps < 0) {
//            log.debug("{} qps {} is below 0, ignore qps control", key, totalQps);
//            return true;
//        }

        if (totalQps == 0) {
            log.debug("{} qps is 0, qps control invalid", key);
            return false;
        }

        QpsControlNode node = controlNodeMap.get(key);
        if (node == null) {
            node = new QpsControlNode();
            node.key = key;
            node.totalQps = new AtomicInteger(totalQps);
            node.localRateLimiter = RateLimiter.create(totalQps / nacosEventListener.getServiceCount());
            controlNodeMap.put(key, node);
        }
        // QPS 发生变化
        if (node.totalQps.get() != totalQps) {
            node.totalQps.getAndUpdate(i -> totalQps);
            node.localRateLimiter.setRate(totalQps / nacosEventListener.getServiceCount());
        }

        return node.localRateLimiter.tryAcquire(qpsCtrlTimeout, TimeUnit.MILLISECONDS);
    }

    static class QpsControlNode {

        String key;

        AtomicInteger totalQps;

        RateLimiter localRateLimiter;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        ScheduledExecutorService scheduled = Executors.newSingleThreadScheduledExecutor();
        scheduled.scheduleWithFixedDelay(new Runnable() {
            @Override
            public void run() {
                if (MapUtil.isEmpty(controlNodeMap)) {
                    return;
                }
                for (Map.Entry<String, QpsControlNode> entry : controlNodeMap.entrySet()) {
                    String key = entry.getKey();
                    QpsControlNode node = entry.getValue();
                    int totalQps = node.totalQps.get();
                    if (totalQps > 0) {
                        int nodeQps = totalQps / nacosEventListener.getServiceCount();
                        if (nodeQps == node.localRateLimiter.getRate()) {
                            continue; // No change in rate
                        }
                        node.localRateLimiter.setRate(nodeQps);
                        log.info("Updated QPS control for key: {}, total QPS: {}", key, nodeQps);
                    } else {
                        log.warn("Invalid total QPS for key: {}, resetting to default", key);
                    }
                }
            }
        }, refreshDelay, refreshDelay, TimeUnit.SECONDS);
        this.scheduledExecutor = scheduled;
    }

    @PreDestroy
    public void destroy() {
        if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.shutdown();
        }
    }
}
