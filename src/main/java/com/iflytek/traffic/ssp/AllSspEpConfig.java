package com.iflytek.traffic.ssp;


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/05/08 09:30
 * @description ssp ep 配置信息
 */
@Component
@Slf4j
public class AllSspEpConfig {

    private Map<String, SspEp> path2SspEpMap = new ConcurrentHashMap<>();


    public void addSspEp() {

    }

    public void deleteSspEp() {

    }

    public SspEp getSspEp(String path) {
        return path2SspEpMap.get(path);
    }
}
