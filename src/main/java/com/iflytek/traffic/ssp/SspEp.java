package com.iflytek.traffic.ssp;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SspEp {

    private Integer sspId;

    private String sspName;

    private Integer sspEpId;

    private String path;

    private int qps;

    private int impTtl;

    /**
     * 结算类型：1:adm；2:burl
     */
    private Integer settlementType;

    // 协议
    private String protocol;

    private Integer isGzip;

    public String genQpsCtrlKey() {
        return "SSP_" + sspId + "_EP_" + sspEpId + "_QPS_CTRL";
    }

}
