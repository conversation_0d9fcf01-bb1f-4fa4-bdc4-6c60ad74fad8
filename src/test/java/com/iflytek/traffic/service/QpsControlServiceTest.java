package com.iflytek.traffic.service;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * 复现：当 totalQps 小于实例数（serviceCount）时，整除为 0，RateLimiter.create/setRate(0) 抛 IllegalArgumentException。
 */
class QpsControlServiceTest {

    @Test
    void shouldThrowWhenCreateRateLimiterWithZeroRate() {
        // mock 实例数较大，导致每实例速率为 0
        NacosEventListener nacos = Mockito.mock(NacosEventListener.class);
        Mockito.when(nacos.getServiceCount()).thenReturn(10);

        QpsControlService svc = new QpsControlService();
        ReflectionTestUtils.setField(svc, "nacosEventListener", nacos);

        // totalQps = 1, serviceCount = 10 -> 1/10 = 0 -> RateLimiter.create(0) 抛异常
        Assertions.assertThrows(IllegalArgumentException.class, () -> svc.qpsCtrl("K_CREATE", 1));
    }

    @Test
    void shouldThrowWhenUpdateRateLimiterToZeroRate() {
        // 第一次创建为正速率，第二次更新为 0 速率
        NacosEventListener nacos = Mockito.mock(NacosEventListener.class);
        Mockito.when(nacos.getServiceCount()).thenReturn(10);

        QpsControlService svc = new QpsControlService();
        ReflectionTestUtils.setField(svc, "nacosEventListener", nacos);

        // 先创建：totalQps = 20, serviceCount = 10 -> 2/s -> 正常创建
        boolean first = svc.qpsCtrl("K_UPDATE", 20);
        Assertions.assertTrue(first || !first); // 仅确保未抛异常

        // 调整场景：模拟更小的 totalQps，且 serviceCount 更大导致整除为 0
        Mockito.when(nacos.getServiceCount()).thenReturn(100);

        // 第二次调用：触发 setRate(totalQps/serviceCount) = 1/100 = 0 -> 抛异常
        Assertions.assertThrows(IllegalArgumentException.class, () -> svc.qpsCtrl("K_UPDATE", 1));
    }
}


