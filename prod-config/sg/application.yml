spring:
  application:
    name: traffic-server
  profiles:
    active: prod
  config:
    import: "optional:nacos:traffic-server-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}"
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      discovery:
        server-addr: mse-5ce981110-nacos-ans.mse.aliyuncs.com:8848
        namespace: 3fc273c2-2f2a-43fc-8dfd-583da970e1ed
        group: DEFAULT_GROUP
      config:
        server-addr: mse-5ce981110-nacos-ans.mse.aliyuncs.com:8848
        namespace: 3fc273c2-2f2a-43fc-8dfd-583da970e1ed
        file-extension: properties
        group: DEFAULT_GROUP
        refresh-enabled: true
management:
  endpoints:
    web:
      exposure:
        include: '*'
  metrics:
    tags:
      application: ${spring.application.name}

logging:
  level:
    com.alibaba.cloud.nacos.refresh: DEBUG
    com.alibaba.cloud.nacos.client: DEBUG