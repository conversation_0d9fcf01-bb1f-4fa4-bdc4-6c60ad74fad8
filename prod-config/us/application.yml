spring:
  application:
    name: traffic-server
  profiles:
    active: prod
  config:
    import: "optional:nacos:traffic-server-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}"
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      discovery:
        server-addr: mse-80c76577-nacos-ans.mse.aliyuncs.com:8848
        namespace: 8ac37608-c8bd-4b52-aa48-fe17a6cfcd07
        group: DEFAULT_GROUP
      config:
        server-addr: mse-80c76577-nacos-ans.mse.aliyuncs.com:8848
        namespace: 8ac37608-c8bd-4b52-aa48-fe17a6cfcd07
        file-extension: properties
        group: DEFAULT_GROUP
        refresh-enabled: true
management:
  endpoints:
    web:
      exposure:
        include: '*'
  metrics:
    tags:
      application: ${spring.application.name}

logging:
  level:
    com.alibaba.cloud.nacos.refresh: DEBUG
    com.alibaba.cloud.nacos.client: DEBUG