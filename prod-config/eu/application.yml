spring:
  application:
    name: traffic-server
  profiles:
    active: prod
  config:
    import: "optional:nacos:traffic-server-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}"
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      discovery:
        server-addr: mse-fe09fb722-nacos-ans.mse.aliyuncs.com:8848
        namespace: 4ae5911d-f23d-49dc-bd1f-6d35c7810df0
        group: DEFAULT_GROUP
      config:
        server-addr: mse-fe09fb722-nacos-ans.mse.aliyuncs.com:8848
        namespace: 4ae5911d-f23d-49dc-bd1f-6d35c7810df0
        file-extension: properties
        group: DEFAULT_GROUP
        refresh-enabled: true
management:
  endpoints:
    web:
      exposure:
        include: '*'
  metrics:
    tags:
      application: ${spring.application.name}

logging:
  level:
    com.alibaba.cloud.nacos.refresh: DEBUG
    com.alibaba.cloud.nacos.client: DEBUG