diff --git a/src/main/java/com/iflytek/traffic/data/entity/DspEpInfo.java b/src/main/java/com/iflytek/traffic/data/entity/DspEpInfo.java
index 8242fe0..abe6553 100644
--- a/src/main/java/com/iflytek/traffic/data/entity/DspEpInfo.java
+++ b/src/main/java/com/iflytek/traffic/data/entity/DspEpInfo.java
@@ -5,93 +5,103 @@ import lombok.Data;
 @Data
 public class DspEpInfo {
     private Integer id;
     /**
      * dsp_id对应d_dsp表id
      */
     private Integer dspId;
 
     /**
      * EP节点ID：0：全部国家；1:东南亚；2:美洲；3:欧洲
      */
     private Integer nodeId;
 
     /**
      * 请求路径
      */
     private String path;
 
     /**
      * DSP 请求QPS限制
      */
     private long qps;
 
     /**
      * EP的可使用状态，1表示可使用，2表示不可使用，
      */
     private Integer epStatus;
 
     /**
      * 是否被删除，0：正常，1：已删除
      */
     private Integer isDel;
 
     /**
      * DSP平台名称
      */
     private String dspName;
 
     /**
      * 对接状态：1:测试；2:正式投放
      */
     private Integer dockingType;
 
     /**
      * 币种ID，1：美元；2:人民币
      */
     private Integer currencyId;
 
     /**
      * 是否支持 gzip：0:不支持；1:支持
      */
     private Integer isGzip;
 
     /**
      * 是否支持 pmp，0:不支持1；1:支持
      */
     private Integer isPmp;
 
     /**
      * 结算类型：1:adm；2:burl
      */
     private Integer settlementType;
 
     /**
      * DSP请求超时时间，单位毫秒
      */
     private Integer timeout;
 
     /**
      * DSP曝光有效期，单位秒
      */
     private Integer dspImpTimeout;
 
     /**
      * DSP EP曝光有效期，单位秒
      */
     private Integer impTimeout;
 
     /**
      * 平台的可状态：1表示可使用，2表示不可使用
      */
     private Integer dspStatus;
 
     /**
      * 协议
      */
     private String protocol;
 
     /**
      * 前缀，用于区分dsp ep
      */
     private String prefix;
+
+    /**
+     * 是否开启RPM，0:不开启；1:开启
+     */
+    private Integer rpmStatus;
+
+    /**
+     * RPM QPS最小值，大于0有效
+     */
+    private Integer qpsMin;
 }
diff --git a/src/main/java/com/iflytek/traffic/data/entity/DspEpLimited.java b/src/main/java/com/iflytek/traffic/data/entity/DspEpLimited.java
new file mode 100644
index 0000000..4de4b5a
--- /dev/null
+++ b/src/main/java/com/iflytek/traffic/data/entity/DspEpLimited.java
@@ -0,0 +1,20 @@
+package com.iflytek.traffic.data.entity;
+
+import lombok.Getter;
+import lombok.Setter;
+
+@Getter
+@Setter
+public class DspEpLimited {
+
+    private  Integer dspId;
+
+    private Integer epId;
+
+    private Integer hourQps;
+
+    private Integer isLimited;
+
+    private Integer isDel;
+
+}
diff --git a/src/main/java/com/iflytek/traffic/data/mapper/info/DspMapper.java b/src/main/java/com/iflytek/traffic/data/mapper/info/DspMapper.java
index a4a9600..8b7233b 100644
--- a/src/main/java/com/iflytek/traffic/data/mapper/info/DspMapper.java
+++ b/src/main/java/com/iflytek/traffic/data/mapper/info/DspMapper.java
@@ -1,6 +1,7 @@
 package com.iflytek.traffic.data.mapper.info;
 
 import com.iflytek.traffic.data.entity.DspEpInfo;
+import com.iflytek.traffic.data.entity.DspEpLimited;
 import com.iflytek.traffic.data.entity.DspInfo;
 import org.apache.ibatis.annotations.Select;
 
@@ -9,10 +10,13 @@ import java.util.List;
 public interface DspMapper {
     @Select("select dde.*,dd.protocol,dd.dsp_name,dd.docking_type,dd.currency_id,dd.is_gzip, "
             + "dd.is_pmp,dd.settlement_type,dd.timeout,dd.imp_timeout as dsp_imp_timeout,dd.dsp_status from iflytek_overseas_adx.d_dsp_ep dde "
             + "left join iflytek_overseas_adx.d_dsp dd on dde.dsp_id = dd.id "
             + "where dde.is_del = 0 and dde.ep_status = 1 and dd.dsp_status = 1;")
     List<DspEpInfo> selectValidEp();
 
     @Select("select * from iflytek_overseas_adx.d_dsp dd where dd.dsp_status =1;")
     List<DspInfo> selectValidDsp();
+
+    @Select("select * from iflytek_overseas_adx.d_dsp_ep_limited where is_del = 0 and is_limited = 1;")
+    List<DspEpLimited> selectDspEpRpmQps();
 }
diff --git a/src/main/java/com/iflytek/traffic/data/provider/DspDataProvider.java b/src/main/java/com/iflytek/traffic/data/provider/DspDataProvider.java
index 6841f8f..98674db 100644
--- a/src/main/java/com/iflytek/traffic/data/provider/DspDataProvider.java
+++ b/src/main/java/com/iflytek/traffic/data/provider/DspDataProvider.java
@@ -4,6 +4,7 @@ import cn.hutool.core.collection.CollUtil;
 import cn.hutool.core.collection.CollectionUtil;
 import cn.hutool.core.util.StrUtil;
 import com.iflytek.traffic.data.entity.DspEpInfo;
+import com.iflytek.traffic.data.entity.DspEpLimited;
 import com.iflytek.traffic.data.mapper.info.DspMapper;
 import com.iflytek.traffic.dsp.DspEpObj;
 import lombok.extern.slf4j.Slf4j;
@@ -18,99 +19,135 @@ import java.util.stream.Collectors;
 @Service
 @Slf4j
 public class DspDataProvider {
 
 
     @Autowired
     private DspMapper dspMapper;
 
     public Map<Integer, DspEpInfo> dspEpInfoMap = new HashMap<>();
 
+    public Map<Integer, Integer> dspEpRpmQpsHour = new HashMap<>();
+
 
     @Scheduled(fixedRateString = "${data.update.interval.dsp:300000}")
     public void update(){
         updateDspEpInfo();
+        updateRpmQps();
     }
 
     /**
      * 定时刷新ep信息
      */
-    public void updateDspEpInfo(){
+    private void updateDspEpInfo(){
         List<DspEpInfo> dspEpInfoList = dspMapper.selectValidEp();
         if (CollectionUtil.isEmpty(dspEpInfoList)) {
             log.error("update with empty dsp ep infos");
             return;
         }
         log.info("update dsp ep info, size: {}", dspEpInfoList.size());
         Iterator<DspEpInfo> iterator = dspEpInfoList.iterator();
         while (iterator.hasNext()) {
             DspEpInfo dspEpInfo = iterator.next();
             if (StrUtil.isBlank(dspEpInfo.getPrefix()) || dspEpInfo.getPrefix().equals("unknown")) {
                 log.warn("dsp ep path is blank or path is unknown: {}", dspEpInfo.getId());
                 iterator.remove();
             }
             if (StrUtil.isBlank(dspEpInfo.getProtocol())) {
                 log.warn("dsp ep protocol is blank: {}", dspEpInfo.getId());
                 iterator.remove();
             }
             if (StrUtil.isBlank(dspEpInfo.getPath())) {
                 log.warn("dsp ep path is blank: {}", dspEpInfo.getId());
                 iterator.remove();
             }
         }
         Map<Integer, DspEpInfo> tmp = dspEpInfoList.stream().collect(Collectors.toMap(DspEpInfo::getId, Function.identity()));
         dspEpInfoMap = tmp;
     }
 
+    private void updateRpmQps() {
+        List<DspEpLimited> dspEpLimiteds = dspMapper.selectDspEpRpmQps();
+        if (CollectionUtil.isEmpty(dspEpLimiteds)) {
+            log.info("update with empty dsp ep limited.");
+            return;
+        }
+        Map<Integer, Integer> tmp = dspEpLimiteds.stream().collect(Collectors.toMap(DspEpLimited::getEpId, DspEpLimited::getHourQps));
+        dspEpRpmQpsHour = tmp;
+    }
+
     public DspEpObj getDspEpObj(Integer dspEpId) {
         if (dspEpId != null && dspEpInfoMap.containsKey(dspEpId)) {
             DspEpObj dspEpObj = new DspEpObj();
             DspEpInfo info = dspEpInfoMap.get(dspEpId);
             dspEpObj.setDspId(info.getDspId());
             dspEpObj.setDspEpId(info.getId());
-            dspEpObj.setQps(Long.valueOf(info.getQps()).intValue());
+            dspEpObj.setQps(determineQps(info));
             dspEpObj.setPath(info.getPath());
             dspEpObj.setPrefix(info.getPrefix());
             dspEpObj.setName(info.getDspName());
             dspEpObj.setTimeout(info.getTimeout());
             dspEpObj.setIsGzip(info.getIsGzip());
             dspEpObj.setSettlementType(info.getSettlementType());
             dspEpObj.setProtocol(info.getProtocol());
             dspEpObj.setImpTtl(info.getDspImpTimeout());
             if (info.getImpTimeout() != null && info.getImpTimeout() > 0) {
                 dspEpObj.setImpTtl(info.getImpTimeout());
             }
             return dspEpObj;
         }
         return null;
     }
 
     public List<Integer> diffDspEpId(Set<Integer> dspEpId) {
         if (CollUtil.isEmpty(dspEpId)) {
             return CollUtil.newArrayList(dspEpInfoMap.keySet());
         }
         List<Integer> res = CollUtil.newArrayList();
         for (Integer id : dspEpInfoMap.keySet()) {
             if (!dspEpId.contains(id)) {
                 res.add(id);
             }
         }
         return res;
     }
 
     public Integer getDspId(Integer dspEpId) {
         if (dspEpId != null && dspEpInfoMap.containsKey(dspEpId)) {
             DspEpInfo dspEpInfo = dspEpInfoMap.get(dspEpId);
             return dspEpInfo.getDspId();
         }
         return null;
     }
 
     public Integer getDspEpQps(Integer dspEpId) {
         if (dspEpId != null && dspEpInfoMap.containsKey(dspEpId)) {
             DspEpInfo dspEpInfo = dspEpInfoMap.get(dspEpId);
-            return Long.valueOf(dspEpInfo.getQps()).intValue();
+            return determineQps(dspEpInfo);
         }
         return null;
     }
 
+    private int determineQps(DspEpInfo dspEpInfo) {
+        int qps = Long.valueOf(dspEpInfo.getQps()).intValue();
+        log.debug("dsp ep {}, rmp_status: {}, qps: {}, qps_min: {}, rpm_qps_hour: {}",
+                dspEpInfo.getId(), dspEpInfo.getRpmStatus(), qps, dspEpInfo.getQpsMin(), dspEpRpmQpsHour.get(dspEpInfo.getId()));
+        int finalQps = 0;
+        if (dspEpInfo.getRpmStatus() != 1) {
+            finalQps = qps;
+        } else  {
+            Integer rpmQps = dspEpRpmQpsHour.get(dspEpInfo.getId());
+            if (rpmQps == null) {
+                finalQps = qps;
+            } else {
+                if (dspEpInfo.getQpsMin() > 0 && rpmQps <= dspEpInfo.getQpsMin()) {
+                    finalQps = dspEpInfo.getQpsMin();
+                } else {
+                    finalQps =  rpmQps > qps ? qps : rpmQps;
+                }
+            }
+        }
+        log.debug("dsp ep {}, final qps: {}", dspEpInfo.getId(), finalQps);
+        return finalQps;
+    }
+
 }
