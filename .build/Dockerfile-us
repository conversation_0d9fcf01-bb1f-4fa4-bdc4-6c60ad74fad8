FROM harbor.voiceads.cn:1443/dsp-registry/abroad-engine-basic-maven:3.9.9-openjdk-17 as basic
COPY ./.build/settings.xml /usr/share/maven/conf/

COPY ./ /traffic-server
WORKDIR /traffic-server
RUN printf '************* artifacts.iflytek.com\n************* depend.iflytek.com' >> /etc/hosts && mvn clean package -Dmaven.test.skip=true

FROM harbor.voiceads.cn:1443/dsp-registry/abroad-engine-basic-maven:3.9.9-openjdk-17

MAINTAINER voiceads.dsp.service

COPY --from=basic /traffic-server/target/ /traffic-server/target
COPY --from=basic /traffic-server/prod-config/us /traffic-server/config
COPY --from=basic /traffic-server/data /traffic-server/data

WORKDIR /traffic-server

CMD ["/usr/bin/java", "-Dfile.encoding=utf-8", "-XX:NewRatio=1", "-verbose:gc", "-Xlog:gc*=info:file=/traffic-server/logs/gc.log", "-XX:+FlightRecorder", "-XX:StartFlightRecording=duration=1h,maxsize=1G,filename=/traffic-server/logs/traffic-server.jfr", "-Xms18432m", "-Xmx18432m", "-XX:+UseZGC", "-XX:ConcGCThreads=8", "-XX:MetaspaceSize=128m", "-XX:+AlwaysPreTouch", "-XX:+UseCompressedOops", "-XX:-OmitStackTraceInFastThrow", "-jar", "/traffic-server/target/traffic-server.jar", "--logging.config=file:config/log4j2.xml"]