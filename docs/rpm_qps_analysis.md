# RPM QPS 对流量分配功能的影响分析

## 概述

RPM (Revenue Per Mille) QPS 是一个基于收益管理的QPS控制机制，它根据DSP端点的实际收益表现动态调整流量分配，以优化整体收益。

## 1. RPM QPS 功能实现机制

### 1.1 核心控制逻辑

RPM QPS的控制逻辑在 `DspDataProvider.determineQps()` 方法中实现：

```java
private int determineQps(DspEpInfo dspEpInfo) {
    int qps = Long.valueOf(dspEpInfo.getQps()).intValue();
    int finalQps = 0;
    
    if (dspEpInfo.getRpmStatus() != 1) {
        // RPM功能未启用，使用原始QPS
        finalQps = qps;
    } else {
        // RPM功能启用
        Integer rpmQps = dspEpRpmQpsHour.get(dspEpInfo.getId());
        if (rpmQps == null) {
            // 无RPM数据，使用原始QPS
            finalQps = qps;
        } else {
            if (dspEpInfo.getQpsMin() > 0 && rpmQps <= dspEpInfo.getQpsMin()) {
                // RPM QPS低于最小值，使用最小值保底
                finalQps = dspEpInfo.getQpsMin();
            } else {
                // 使用RPM QPS和原始QPS的较小值
                finalQps = rpmQps > qps ? qps : rpmQps;
            }
        }
    }
    return finalQps;
}
```

### 1.2 关键字段说明

**DspEpInfo 实体中的RPM相关字段：**
- `rpmStatus`: RPM功能开关（0:不开启；1:开启）
- `qpsMin`: RPM QPS最小值，用于保底控制

**DspEpLimited 实体字段：**
- `epId`: DSP端点ID
- `hourQps`: 每小时的RPM QPS限制值
- `isLimited`: 是否受限制
- `isDel`: 是否删除

## 2. 数据加载和更新机制

### 2.1 更新频率

系统采用多层次的数据更新机制：

1. **DSP EP 基础信息更新**
   - 方法: `DspDataProvider.updateDspEpInfo()`
   - 频率: `data.update.interval.dsp` 配置，默认300000ms（5分钟）
   - 数据源: 从数据库查询有效的DSP EP配置

2. **RPM QPS 数据更新**
   - 方法: `DspDataProvider.updateRpmQps()`
   - 频率: `data.update.interval.dsp` 配置，默认300000ms（5分钟）
   - 数据源: 通过 `dspMapper.selectDspEpRpmQps()` 查询

3. **QPS 动态计算和调整**
   - 方法: `DspEpQpsProvider.refreshDspEpQps()`
   - 频率: `data.refresh.interval.dspEp-qps` 配置，默认30000ms（30秒）
   - 功能: 基于实时数据动态调整QPS分配

### 2.2 并非每次都重新加载

**重要发现**: RPM QPS数据**并非每次请求都重新加载**，而是采用内存缓存机制：

- 数据加载到 `dspEpRpmQpsHour` Map中缓存
- 每5分钟从数据库批量更新一次
- 请求处理时直接从内存读取，性能高效

## 3. 对原有流量分配功能的影响

### 3.1 QPS计算层面的影响

RPM QPS在QPS计算的最底层生效，影响整个流量分配链路：

```
原始QPS配置 → RPM QPS调整 → 多维度QPS分割 → 实际流量控制
```

### 3.2 流量分配的具体影响

1. **动态QPS调整**
   - 根据收益表现实时调整DSP端点的QPS上限
   - 高收益端点可能获得更多流量
   - 低收益端点流量受限，但有qpsMin保底

2. **多级QPS控制**
   - 全局QPS控制（基于RPM调整后的值）
   - 细分维度QPS控制（基于SSP、地域、广告类型等）
   - 实际请求级别的限流控制

3. **收益优化**
   - 通过RPM数据指导流量分配
   - 自动减少低效流量，增加高效流量
   - 保持最小流量保证服务稳定性

### 3.3 对请求处理的影响

在 `RequestDspVerticle` 中，流量分配的处理流程：

1. **获取调整后的QPS**: `dspDataProvider.getDspEpObj(id)` 返回已经过RPM调整的QPS
2. **细分QPS计算**: `dspEpQpsProvider.getDspEpQpsByReq()` 基于调整后的QPS进行维度分割
3. **多层QPS控制**: 同时应用全局QPS和细分QPS控制

## 4. 性能和稳定性考虑

### 4.1 性能优势

- **内存缓存**: 避免每次请求查询数据库
- **批量更新**: 5分钟批量更新减少数据库压力
- **异步处理**: 定时任务异步更新，不影响请求处理

### 4.2 稳定性保障

- **最小QPS保底**: qpsMin字段确保最低流量
- **降级机制**: RPM数据缺失时使用原始QPS
- **开关控制**: rpmStatus可随时关闭RPM功能

## 5. 总结

RPM QPS功能是一个**智能的收益导向流量分配机制**，具有以下特点：

1. **动态调整**: 基于实际收益数据自动调整流量分配
2. **高效实现**: 采用内存缓存，避免频繁数据库查询
3. **稳定可靠**: 具备降级和保底机制
4. **精细控制**: 在原有QPS控制基础上增加收益维度
5. **实时生效**: 30秒级别的快速调整响应

该功能显著提升了流量变现效率，同时保持了系统的稳定性和性能。