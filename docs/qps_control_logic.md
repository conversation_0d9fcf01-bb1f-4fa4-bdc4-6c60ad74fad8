## QPS 控制整体逻辑与“qps=0 不投”设计说明

本文档梳理当前代码中的 QPS 控制链路、关键计算与控制点、“qps=0 不投”的统一语义，以及发现的风险与建议。适用于研发、运维与产品同学快速理解与排障。

### 1. 背景与目标
- 统一“qps=0 不投”语义：当任意控制点的 QPS 计算结果为 0，则视为不放量（直接拦截，不请求下游）。
- 收敛限流入口：SSP 入口、DSP 维度限流（subQps）、DSP EP 总限流统一通过限流器实现，保持一致行为与指标打点。

### 2. 主要组件与职责
- `MediaRequestPoolService.execute(...)`：SSP 入口限流。基于 `SspEp.genQpsCtrlKey()` 与 `SspEp.qps` 调用 `QpsControlService.qpsCtrl(...)`。
- `RequestDspVerticle.doHandle(...)`：对每个候选的 DSP EP 执行两层限流：
  - 维度限流（subQps）：`DspEpQpsProvider.getDspEpQpsByReq(...)` 计算 key 与子 QPS，随后 `qpsCtrl`。
  - EP 总限流：`DspDataProvider.getDspEpObj(...)` 内部经 `determineQps(...)` 计算最终 EP QPS，随后 `qpsCtrl`。
- `DspDataProvider`：周期更新 `DspEpInfo` 基础配置与 RPM 小时 QPS，`determineQps(...)` 产出 EP 最终 QPS。
- `DspEpQpsProvider`：
  - 周期从 DB 读取维度 QPS 配置，汇总为 `DspEpQps`；
  - 定时任务 `calDspEpQps()` 结合实时观测（ssp/req/bid）计算 gap，并调整/缓存各维度控制 key 的 QPS；
  - 提供 `getDspEpQpsByReq(...)` 在请求时动态产出 subQps。
- `QpsControlService`：限流器实现（本地 `RateLimiter`），`totalQps==0` 返回 false 实现“0不投”。

### 3. 全链路序列图

```mermaid
sequenceDiagram
  participant Media as MediaRequestPoolService
  participant Bus as Vert.x EventBus
  participant Req as RequestDspVerticle
  participant Qps as QpsControlService
  participant DSP as DSP EPs
  participant Pick as PickOneVerticle

  Media->>Qps: qpsCtrl(SSP_KEY, sspEp.qps)
  alt 通过
    Media->>Bus: 进入处理流程
    Bus->>Req: 请求 DSP
    loop 遍历候选 DSP EP
      Req->>DspEpQpsProvider: 计算 subQps(Key, QPS)
      alt subQps 存在
        Req->>Qps: qpsCtrl(subKey, subQps)
        alt 被限流
          Req-->>Req: 跳过该 EP 子维度
        end
      end
      Req->>Qps: qpsCtrl(DSP_EP_KEY, determineQps(...))
      alt 被限流
        Req-->>Req: 跳过该 EP
      else 通过
        Req->>DSP: 异步发起请求
      end
    end
    DSP-->>Pick: 返回响应
    Pick-->>Media: 选优并响应下游
  else SSP 被限流
    Media-->>Media: 返回 204
  end
```

### 4. QPS 计算与控制点
- SSP 入口：
  - 控制键：`SSP_${sspId}_EP_${sspEpId}_QPS_CTRL`
  - QPS 值：`SspEp.qps`
  - 行为：`qpsCtrl(key, qps)`，若 `qps == 0` 则直接 false → 返回 204。

- DSP 维度限流（subQps）：
  - 控制键：`DSPEP_${dspEpId}_QPS_${维度拼接}`（请求时生成，维度包括 sspId/adType/region/bundle 的组合）
  - QPS 值：以 `DspEpObj.qps` 为基，乘以各维度 ratio（比例见第 6 节），并配合 `calDspEpQps()` 的动态调整缓存
  - 行为：`qpsCtrl(subKey, subQps)`，`subQps == 0` → false（不投）

- DSP EP 总限流：
  - 控制键：`DSP_${dspId}_EP_${dspEpId}_QPS_CTRL`
  - QPS 值：`determineQps(dspEpInfo)`（详见第 5 节）
  - 行为：`qpsCtrl(key, finalQps)`，`finalQps == 0` → false（不投）

### 5. RPM 融合与 `determineQps(...)` 决策
文件：`com.iflytek.traffic.data.provider.DspDataProvider`

```mermaid
flowchart TD
  A[读取基础 qps = info.qps] --> B{rpmStatus == 1?}
  B -- 否 --> Z[finalQps = qps]
  B -- 是 --> C{rpmQps 存在?}
  C -- 否 --> Z
  C -- 是 --> D{rpmQps <= qpsMin?}
  D -- 是 --> E[finalQps = qpsMin]
  D -- 否 --> F[finalQps = min(qps, rpmQps)]
  E --> G[输出 finalQps]
  F --> G
  Z --> G
```

伪代码（参考 `determineQps` 当前实现）：
```
finalQps = 0
if rpmStatus != 1:
  finalQps = qps
else:
  if rpmQps == null:
    finalQps = qps
  else if rpmQps <= qpsMin:
    finalQps = qpsMin
  else:
    finalQps = min(qps, rpmQps)
```

语义要点：
- 当启用 RPM 且 `rpmQps <= qpsMin` 时，以 `qpsMin` 为最终 QPS；若 `qpsMin == 0`，则最终为 0 → 不投。
- 当 `rpmQps == null`（无数据）回退为基础 `qps`。

### 6. 维度 QPS 配置与比例使用
文件：`com.iflytek.traffic.data.provider.DspEpQpsProvider`、`com.iflytek.traffic.data.entity.DspEpQps`

- 维度：`ssp(1001) / bundle(1003) / region(1004) / adType(1005)`
- 组合：叉乘生成维度组合，比例按百分比相乘（`/100`）累积。
- 请求时计算 subQps：以 `DspEpObj.qps` 为基，按所命中的维度比例累计计算得到 subQps，然后调用 `qpsCtrl(subKey, subQps)`。
- 定时调整：`calDspEpQps()` 通过实时观测（ssp/req/bid）计算 gap，对不足的组合下发更高的控制值并缓存到 `dspEpKeyAndQpsMap`（作为优先生效的 subQps）。

注意：当前代码中不同比较/计算处对“比例”的单位使用有不一致现象（部分当作百分比，部分当作小数），详见第 8 节风险 3。

### 7. 限流器实现与“0 不投”
文件：`com.iflytek.traffic.service.QpsControlService`

- 规则：
  - `key` 为空：放行（不控）
  - `totalQps == 0`：返回 false（不投）
  - `totalQps > 0`：按实例数 `serviceCount` 均分，使用本地 `RateLimiter` 尝试获取。
- 关键点：若每实例速率计算为 0（如 `totalQps < serviceCount` 且使用整除），将导致 `RateLimiter` 创建/更新异常。

### 8. 已知风险与建议
- 风险 1（新增触发面更大）：`qpsMin` 为空可能触发 NPE
  - 位置：`determineQps(...)` 中 `rpmQps <= qpsMin` 比较。
  - 建议：默认 `qpsMin` 为空按 0 处理，并约束 `qpsMin <= qps`。

- 风险 2（历史问题）：每实例速率整除为 0 导致 `RateLimiter` 异常
  - 位置：`QpsControlService.qpsCtrl(...)` 创建/更新 `RateLimiter`。
  - 场景：`totalQps > 0` 且 `< serviceCount`。
  - 建议：使用 double 速率并设置最小正值（如 `max(1e-6, totalQps/serviceCount)`），或策略性直接拒绝（更保守）。

- 风险 3：维度“比例”单位不一致（百分比/小数混用）
  - 位置：`getDspEpQpsByReq(...)` 与 `calDspEpQps()` 的比例使用方式不一致。
  - 影响：请求期 subQps 与离线 gap 计算目标不一致，导致分配偏差。
  - 建议：统一为“百分比 Double”，一律除以 `100.0d`，避免 `intValue()` 截断。

- 风险 4：负值 QPS 的语义与保护
  - 场景：维度配置或数据异常导致 subQps 为负。
  - 建议：对负值做前置保护（直接视为不投或归一到 0），保持“0不投”同时避免 `RateLimiter` 负速率异常。

### 9. “qps=0 不投”的统一语义覆盖面
- SSP 入口：`SspEp.qps == 0` → 入口直接 204。
- DSP 子维度：`subQps == 0` → `qpsCtrl` false，跳过该子维度请求。
- DSP EP 总量：`determineQps(...) == 0` → `qpsCtrl` false，跳过该 EP。
- 记录与监控：对应“qps.filter.count”计数器会增长；日志打印“QPS limit exceeded”。

### 10. 需求确认点
- 当 `rpmQps <= 0` 时的业务意图：
  - 目前实现：若启用 RPM，`rpmQps <= qpsMin` 则取 `qpsMin`，在 `qpsMin == 0` 时不投；仅 `rpmQps == null` 回退到基础 `qps`。
  - 需要确认：RPM 的 0 是“停投”还是“暂无数据/异常需回退”？如为回退，则应恢复“`rpmQps <= 0` 回退”的旧策略。

### 11. 最小验证用例（建议）
- RPM 融合：
  - `rpmStatus=1, rpmQps=null, qps=100, qpsMin=0` → 100
  - `rpmStatus=1, rpmQps=0, qps=100, qpsMin=0` → 0（不投）
  - `rpmStatus=1, rpmQps=8, qps=100, qpsMin=10` → 10
  - `rpmStatus=1, rpmQps=50, qps=40, qpsMin=10` → 40
  - `qpsMin=null` → 不应 NPE（需空值保护）

- 限流器：
  - `serviceCount=10, totalQps=1` → 不应抛异常（double 速率或拒绝策略）
  - `totalQps=0` → false（不投）
  - `subQps=-1` → 不应抛异常（负值前置保护）

- 比例一致性：同一组配置在请求期与离线计算应得到一致的目标值与 gap 结果。

### 12. 关键文件索引
- `com.iflytek.traffic.service.MediaRequestPoolService`
- `com.iflytek.traffic.procedure.RequestDspVerticle`
- `com.iflytek.traffic.data.provider.DspDataProvider`
- `com.iflytek.traffic.data.provider.DspEpQpsProvider`
- `com.iflytek.traffic.data.entity.DspEpQps`
- `com.iflytek.traffic.service.QpsControlService`

### 13. 术语补充
- subQps：按 `ssp/adType/region/bundle` 等维度切分的更细粒度 QPS 配额。
- gap：离线统计期内，目标配额与实际请求量（或参竞指标）的差值，用于动态补量。

---
维护人：
- 研发：
- 最近更新：2025-08-08（随“qps=0 不投”改动添加）


